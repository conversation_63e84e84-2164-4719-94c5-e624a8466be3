#ifndef THEMEMANAGER_H
#define THEMEMANAGER_H

#include <QString>
#include <QColor>
#include <QMap>
#include <QObject>

/**
 * @brief The ThemeType enum defines the available themes
 */
enum class ThemeType {
    Light,    ///< Light theme
    Dark,     ///< Dark theme
    System    ///< Use system theme
};

/**
 * @brief The ThemeManager class manages application theming
 */
class ThemeManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Gets the singleton instance
     * @return The singleton instance
     */
    static ThemeManager* instance();

    /**
     * @brief Destructor
     */
    ~ThemeManager();

    /**
     * @brief Set the current theme
     * @param theme The theme to set
     */
    void setTheme(ThemeType theme);

    /**
     * @brief Get the current theme
     * @return The current theme
     */
    ThemeType getCurrentTheme() const;

    /**
     * @brief Get a color from the current theme
     * @param colorName The name of the color
     * @return The color
     */
    QColor getColor(const QString &colorName) const;

    /**
     * @brief Get the stylesheet for the current theme
     * @return The stylesheet as a string
     */
    QString getStyleSheet() const;

Q_SIGNALS:
    /**
     * @brief Signal emitted when theme is changed
     * @param theme The new theme
     */
    void themeChanged(ThemeType theme);

private:
    /**
     * @brief Private constructor (singleton pattern)
     */
    ThemeManager();

    /**
     * @brief Load the light theme
     */
    void loadLightTheme();

    /**
     * @brief Load the dark theme
     */
    void loadDarkTheme();

    /**
     * @brief Determine and load the system theme
     */
    void loadSystemTheme();

    static ThemeManager* _instance; ///< Singleton instance
    ThemeType currentTheme;         ///< Current theme
    QMap<QString, QColor> colors;   ///< Theme colors
    QString styleSheet;             ///< Theme stylesheet
};

#endif // THEMEMANAGER_H 