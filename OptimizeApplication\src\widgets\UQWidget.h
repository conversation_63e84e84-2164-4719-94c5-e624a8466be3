#ifndef UQWIDGET_H
#define UQWIDGET_H

#include <QWidget>

QT_BEGIN_NAMESPACE
class QComboBox;
class QSpinBox;
class QDoubleSpinBox;
class QCheckBox;
class QPushButton;
class QLabel;
class QGroupBox;
class QFormLayout;
class QVBoxLayout;
QT_END_NAMESPACE

namespace Ui {
class UQWidget;
}

class UQWidget : public QWidget
{
    Q_OBJECT

public:
    explicit UQWidget(QWidget *parent = nullptr);
    ~UQWidget();

    // Getter methods for Dakota UQ methods
    QString getMethod() const;
    int getSamples() const;
    QString getSamplingType() const;
    int getPolynomialOrder() const;
    QString getExpansionType() const;
    double getConvergenceTolerance() const;
    int getSeed() const;
    bool isAdaptiveRefinement() const;
    
    // Setter methods
    void setMethod(const QString &method);
    void setSamples(int samples);
    void setSamplingType(const QString &type);
    void setPolynomialOrder(int order);
    void setExpansionType(const QString &type);
    void setConvergenceTolerance(double tolerance);
    void setSeed(int seed);
    void setAdaptiveRefinement(bool enabled);

signals:
    void parametersChanged();
    void methodChanged(const QString &method);

private slots:
    void onMethodChanged();
    void onParameterChanged();
    void onExpansionTypeChanged();
    void onAdaptiveRefinementToggled(bool enabled);

private:
    void setupUI();
    void setupConnections();
    void updateMethodDescription();
    void updateParameterVisibility();

    Ui::UQWidget *ui;
};

#endif // UQWIDGET_H 