#include "ChartWidget.h"
#include "../utils/Logger.h"
#include <QtPrintSupport/QPrinter>
#include <QtPrintSupport/QPrintDialog>
#include <QApplication>
#include <QRandomGenerator>
#include <QtCharts/QBarCategoryAxis>
#include <QtCharts/QBarSeries>
#include <QtCharts/QBarSet>
#include <QtCharts/QCategoryAxis>
#include <QtCharts/QChart>
#include <QtCharts/QChartView>
#include <QtCharts/QLegend>
#include <QtCharts/QLegendMarker>
#include <QtCharts/QLineSeries>
#include <QtCharts/QScatterSeries>
#include <QtCharts/QAreaSeries>
#include <QtCharts/QPieSeries>
#include <QtCharts/QPieSlice>
#include <QtCharts/QValueAxis>
#include <limits>
#include <QFileDialog>
#include <QMessageBox>
#include <QColorDialog>
#include <QInputDialog>
#include <QPainter>
#include <QDir>
#include <QPixmap>

ChartWidget::ChartWidget(QWidget *parent)
    : QWidget(parent)
    , m_chart(new QChart())
    , m_chartView(new QChartView(m_chart, this))
    , m_layout(new QGridLayout(this))
    , m_toolBar(nullptr)
    , m_chartTypeCombo(nullptr)
    , m_themeCombo(nullptr)
    , m_exportButton(nullptr)
    , m_addSeriesButton(nullptr)
    , m_editSeriesButton(nullptr)
    , m_removeSeriesButton(nullptr)
    , m_zoomToDataButton(nullptr)
    , m_resetZoomButton(nullptr)
    , m_barSeries(nullptr)
    , m_pieSeries(nullptr)
    , m_axisX(new QValueAxis())
    , m_axisY(new QValueAxis())
    , m_barAxisX(new QBarCategoryAxis())
    , m_chartType(LineChart)
    , m_toolBarEnabled(true)
    , m_animationEnabled(true)
    , m_themeSelectEnabled(true)
{
    LOG_INFO("Creating ChartWidget", "ChartWidget");
    initUI();
    createToolBar();
    createConnections();
    createChart();
    LOG_INFO("ChartWidget created", "ChartWidget");
}

ChartWidget::~ChartWidget()
{
    LOG_INFO("Destroying ChartWidget", "ChartWidget");
    
    // Clean up series
    clearAllSeries();
    
    // Clean up axes
    if (m_axisX) {
        m_chart->removeAxis(m_axisX);
        delete m_axisX;
        m_axisX = nullptr;
    }
    
    if (m_axisY) {
        m_chart->removeAxis(m_axisY);
        delete m_axisY;
        m_axisY = nullptr;
    }
    
    if (m_barAxisX) {
        m_chart->removeAxis(m_barAxisX);
        delete m_barAxisX;
        m_barAxisX = nullptr;
    }
}

void ChartWidget::initUI()
{
    // Set up chart view
    m_chartView->setRenderHint(QPainter::Antialiasing);
    m_chartView->setMinimumSize(400, 300);
    
    // Set up chart
    m_chart->setTitle("Chart");
    m_chart->setAnimationOptions(QChart::SeriesAnimations);
    m_chart->legend()->setVisible(true);
    m_chart->legend()->setAlignment(Qt::AlignBottom);
    
    // Set up layout
    m_layout->addWidget(m_chartView, 1, 0);
    setLayout(m_layout);
}

void ChartWidget::createToolBar()
{
    if (!m_toolBarEnabled) {
        return;
    }
    
    // Create toolbar
    m_toolBar = new QToolBar(this);
    m_layout->addWidget(m_toolBar, 0, 0);
    
    // Chart type combo
    m_chartTypeCombo = new QComboBox(this);
    m_chartTypeCombo->addItem("Line Chart", LineChart);
    m_chartTypeCombo->addItem("Scatter Chart", ScatterChart);
    m_chartTypeCombo->addItem("Bar Chart", BarChart);
    m_chartTypeCombo->addItem("Pie Chart", PieChart);
    m_chartTypeCombo->addItem("Area Chart", AreaChart);
    m_toolBar->addWidget(new QLabel("Chart Type:", this));
    m_toolBar->addWidget(m_chartTypeCombo);
    m_toolBar->addSeparator();
    
    // Theme combo
    if (m_themeSelectEnabled) {
        m_themeCombo = new QComboBox(this);
        m_themeCombo->addItem("Light", QChart::ChartThemeLight);
        m_themeCombo->addItem("Dark", QChart::ChartThemeDark);
        m_themeCombo->addItem("Blue Cerulean", QChart::ChartThemeBlueCerulean);
        m_themeCombo->addItem("Brown Sand", QChart::ChartThemeBrownSand);
        m_themeCombo->addItem("Blue NCS", QChart::ChartThemeBlueNcs);
        m_themeCombo->addItem("High Contrast", QChart::ChartThemeHighContrast);
        m_themeCombo->addItem("Blue Icy", QChart::ChartThemeBlueIcy);
        m_themeCombo->addItem("Qt", QChart::ChartThemeQt);
        m_toolBar->addWidget(new QLabel("Theme:", this));
        m_toolBar->addWidget(m_themeCombo);
        m_toolBar->addSeparator();
    }
    
    // Buttons
    m_exportButton = new QPushButton("Export", this);
    m_toolBar->addWidget(m_exportButton);
    
    m_addSeriesButton = new QPushButton("Add Series", this);
    m_toolBar->addWidget(m_addSeriesButton);
    
    m_editSeriesButton = new QPushButton("Edit Series", this);
    m_toolBar->addWidget(m_editSeriesButton);
    
    m_removeSeriesButton = new QPushButton("Remove Series", this);
    m_toolBar->addWidget(m_removeSeriesButton);
    
    m_toolBar->addSeparator();
    
    m_zoomToDataButton = new QPushButton("Zoom to Data", this);
    m_toolBar->addWidget(m_zoomToDataButton);
    
    m_resetZoomButton = new QPushButton("Reset Zoom", this);
    m_toolBar->addWidget(m_resetZoomButton);
}

void ChartWidget::createConnections()
{
    // Connect toolbar signals
    if (m_toolBarEnabled) {
        if (m_chartTypeCombo) {
            connect(m_chartTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), 
                    this, &ChartWidget::onChartTypeChanged);
        }
        
        if (m_themeCombo && m_themeSelectEnabled) {
            connect(m_themeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), 
                    this, &ChartWidget::onThemeChanged);
        }
        
        if (m_exportButton) {
            connect(m_exportButton, &QPushButton::clicked, this, &ChartWidget::onExportChart);
        }
        
        if (m_addSeriesButton) {
            connect(m_addSeriesButton, &QPushButton::clicked, this, &ChartWidget::onAddSeries);
        }
        
        if (m_editSeriesButton) {
            connect(m_editSeriesButton, &QPushButton::clicked, this, &ChartWidget::onEditSeries);
        }
        
        if (m_removeSeriesButton) {
            connect(m_removeSeriesButton, &QPushButton::clicked, this, &ChartWidget::onRemoveSeries);
        }
        
        if (m_zoomToDataButton) {
            connect(m_zoomToDataButton, &QPushButton::clicked, this, &ChartWidget::zoomToData);
        }
        
        if (m_resetZoomButton) {
            connect(m_resetZoomButton, &QPushButton::clicked, this, &ChartWidget::resetZoom);
        }
    }
    
    // Connect legend signals
    // In Qt Charts, we need to handle legend markers individually
    // This will be set up when we update the chart
}

void ChartWidget::createChart()
{
    switch (m_chartType) {
    case LineChart:
        createLineChart();
        break;
    case ScatterChart:
        createScatterChart();
        break;
    case BarChart:
        createBarChart();
        break;
    case PieChart:
        createPieChart();
        break;
    case AreaChart:
        createAreaChart();
        break;
    }
    
    updateAxes();
}

void ChartWidget::createLineChart()
{
    LOG_INFO("Creating line chart", "ChartWidget");
    
    // Clear existing series
    clearAllSeries();
    
    // Create a default series if none exists
    if (m_series.isEmpty()) {
        addSeries("Series 1", QColor(Qt::blue));
        // Add some sample data
        for (int i = 0; i < 10; ++i) {
            addDataPoint(0, i, QRandomGenerator::global()->bounded(10));
        }
    }
}

void ChartWidget::createScatterChart()
{
    LOG_INFO("Creating scatter chart", "ChartWidget");
    
    // Clear existing series
    clearAllSeries();
    
    // Create a default series if none exists
    if (m_series.isEmpty()) {
        addSeries("Series 1", QColor(Qt::red));
        // Add some sample data
        for (int i = 0; i < 20; ++i) {
            addDataPoint(0, QRandomGenerator::global()->bounded(10), 
                         QRandomGenerator::global()->bounded(10));
        }
    }
}

void ChartWidget::createBarChart()
{
    LOG_INFO("Creating bar chart", "ChartWidget");
    
    // Clear existing series
    clearAllSeries();
    
    // Create bar series
    m_barSeries = new QBarSeries();
    m_chart->addSeries(m_barSeries);
    
    // Create default data if none exists
    if (m_barSets.isEmpty()) {
        QBarSet *set1 = new QBarSet("Set 1");
        QBarSet *set2 = new QBarSet("Set 2");
        
        *set1 << 1 << 2 << 3 << 4 << 5;
        *set2 << 5 << 4 << 3 << 2 << 1;
        
        m_barSets.append(set1);
        m_barSets.append(set2);
        
        m_barSeries->append(set1);
        m_barSeries->append(set2);
        
        // Set default categories if none exists
        if (m_barCategories.isEmpty()) {
            m_barCategories << "Category 1" << "Category 2" << "Category 3" 
                           << "Category 4" << "Category 5";
        }
        
        m_barAxisX->setCategories(m_barCategories);
        m_chart->addAxis(m_barAxisX, Qt::AlignBottom);
        m_barSeries->attachAxis(m_barAxisX);
        
        m_chart->addAxis(m_axisY, Qt::AlignLeft);
        m_barSeries->attachAxis(m_axisY);
    }
}

void ChartWidget::createPieChart()
{
    LOG_INFO("Creating pie chart", "ChartWidget");
    
    // Clear existing series
    clearAllSeries();
    
    // Create pie series
    m_pieSeries = new QPieSeries();
    
    // Create default data if none exists
    if (m_pieSlices.isEmpty()) {
        QPieSlice *slice1 = new QPieSlice("Slice 1", 1);
        QPieSlice *slice2 = new QPieSlice("Slice 2", 2);
        QPieSlice *slice3 = new QPieSlice("Slice 3", 3);
        QPieSlice *slice4 = new QPieSlice("Slice 4", 4);
        QPieSlice *slice5 = new QPieSlice("Slice 5", 5);
        
        m_pieSeries->append(slice1);
        m_pieSeries->append(slice2);
        m_pieSeries->append(slice3);
        m_pieSeries->append(slice4);
        m_pieSeries->append(slice5);
        
        m_pieSlices.append(slice1);
        m_pieSlices.append(slice2);
        m_pieSlices.append(slice3);
        m_pieSlices.append(slice4);
        m_pieSlices.append(slice5);
        
        // Connect slice signals - using lambda to bridge the parameter mismatch
        for (QPieSlice *slice : m_pieSlices) {
            connect(slice, &QPieSlice::clicked, this, [this, slice]() {
                this->onPieSliceClicked(slice);
            });
        }
        
        // Customize slices
        slice1->setBrush(QColor(Qt::red));
        slice2->setBrush(QColor(Qt::green));
        slice3->setBrush(QColor(Qt::blue));
        slice4->setBrush(QColor(Qt::yellow));
        slice5->setBrush(QColor(Qt::cyan));
        
        slice1->setLabelVisible(true);
        slice2->setLabelVisible(true);
        slice3->setLabelVisible(true);
        slice4->setLabelVisible(true);
        slice5->setLabelVisible(true);
    }
    
    m_chart->addSeries(m_pieSeries);
}

void ChartWidget::createAreaChart()
{
    LOG_INFO("Creating area chart", "ChartWidget");
    
    // Clear existing series
    clearAllSeries();
    
    // Create a default series if none exists
    if (m_series.isEmpty()) {
        // Create the upper line series
        int seriesIndex = addSeries("Upper Series", QColor(Qt::blue));
        
        // Add some sample data
        for (int i = 0; i < 10; ++i) {
            addDataPoint(seriesIndex, i, 5 + QRandomGenerator::global()->bounded(5));
        }
        
        // Create the lower line series
        seriesIndex = addSeries("Lower Series", QColor(Qt::red));
        
        // Add some sample data
        for (int i = 0; i < 10; ++i) {
            addDataPoint(seriesIndex, i, QRandomGenerator::global()->bounded(5));
        }
        
        // Create area series between the two line series
        QAreaSeries *areaSeries = new QAreaSeries(
            qobject_cast<QLineSeries*>(m_series.at(0)),
            qobject_cast<QLineSeries*>(m_series.at(1))
        );
        areaSeries->setName("Area");
        areaSeries->setBrush(QColor(0, 0, 255, 100));
        
        m_chart->addSeries(areaSeries);
        areaSeries->attachAxis(m_axisX);
        areaSeries->attachAxis(m_axisY);
    }
}

void ChartWidget::updateAxes()
{
    // Skip for pie charts (they don't have axes)
    if (m_chartType == PieChart) {
        return;
    }
    
    // For bar charts, axes are handled in createBarChart()
    if (m_chartType == BarChart) {
        return;
    }
    
    // For other chart types
    if (m_chartType == LineChart || m_chartType == ScatterChart || m_chartType == AreaChart) {
        m_axisX->setTitleText("X Axis");
        m_axisY->setTitleText("Y Axis");
        
        // Set range if there's data
        if (!m_series.isEmpty()) {
            QXYSeries *xySeries = m_series.first();
            
            if (xySeries && xySeries->count() > 0) {
                qreal minX = std::numeric_limits<qreal>::max();
                qreal maxX = std::numeric_limits<qreal>::min();
                qreal minY = std::numeric_limits<qreal>::max();
                qreal maxY = std::numeric_limits<qreal>::min();
                
                // Find data range across all series
                for (QXYSeries *series : m_series) {
                    // Get points - note that we need to iterate directly as points() might return QList not QVector
                    for (int i = 0; i < series->count(); ++i) {
                        QPointF point = series->at(i);
                        minX = qMin(minX, point.x());
                        maxX = qMax(maxX, point.x());
                        minY = qMin(minY, point.y());
                        maxY = qMax(maxY, point.y());
                    }
                }
                
                // Add some padding
                qreal xPadding = (maxX - minX) * 0.05;
                qreal yPadding = (maxY - minY) * 0.05;
                
                // Handle zero ranges
                if (qFuzzyIsNull(xPadding)) xPadding = 1.0;
                if (qFuzzyIsNull(yPadding)) yPadding = 1.0;
                
                m_axisX->setRange(minX - xPadding, maxX + xPadding);
                m_axisY->setRange(minY - yPadding, maxY + yPadding);
            } else {
                // Default range if no data
                m_axisX->setRange(0, 10);
                m_axisY->setRange(0, 10);
            }
        }
        
        // Attach axes to chart and series
        m_chart->addAxis(m_axisX, Qt::AlignBottom);
        m_chart->addAxis(m_axisY, Qt::AlignLeft);
        
        for (QXYSeries *series : m_series) {
            series->attachAxis(m_axisX);
            series->attachAxis(m_axisY);
        }
    }
}

void ChartWidget::setChartTitle(const QString &title)
{
    m_chart->setTitle(title);
}

void ChartWidget::setChartType(ChartType type)
{
    if (m_chartType != type) {
        m_chartType = type;
        
        // Update UI
        if (m_chartTypeCombo) {
            m_chartTypeCombo->setCurrentIndex(m_chartTypeCombo->findData(type));
        }
        
        createChart();
    }
}

void ChartWidget::setXAxisTitle(const QString &title)
{
    m_axisX->setTitleText(title);
}

void ChartWidget::setYAxisTitle(const QString &title)
{
    m_axisY->setTitleText(title);
}

void ChartWidget::setXAxisRange(qreal min, qreal max)
{
    m_axisX->setRange(min, max);
}

void ChartWidget::setYAxisRange(qreal min, qreal max)
{
    m_axisY->setRange(min, max);
}

int ChartWidget::addSeries(const QString &name, const QColor &color)
{
    LOG_INFO(QString("Adding series: %1").arg(name), "ChartWidget");
    
    QColor seriesColor = color;
    if (!seriesColor.isValid()) {
        // Generate random color if none provided
        seriesColor = QColor(
            QRandomGenerator::global()->bounded(200) + 20,
            QRandomGenerator::global()->bounded(200) + 20,
            QRandomGenerator::global()->bounded(200) + 20
        );
    }
    
    QXYSeries *series = nullptr;
    
    // Create appropriate series type
    if (m_chartType == ScatterChart) {
        series = new QScatterSeries();
        static_cast<QScatterSeries*>(series)->setMarkerSize(10);
    } else {
        series = new QLineSeries();
    }
    
    series->setName(name);
    series->setPen(QPen(seriesColor, 2));
    
    // Add to chart
    m_chart->addSeries(series);
    
    // Add to our lists
    m_series.append(series);
    m_seriesNames.append(name);
    m_seriesColors.append(seriesColor);
    
    // Attach to axes
    if (m_axisX && m_axisY && m_chartType != PieChart && m_chartType != BarChart) {
        series->attachAxis(m_axisX);
        series->attachAxis(m_axisY);
    }
    
    return m_series.count() - 1;
} 

void ChartWidget::removeSeries(int index)
{
    if (index >= 0 && index < m_series.count()) {
        LOG_INFO(QString("Removing series at index: %1").arg(index), "ChartWidget");
        
        QXYSeries *series = m_series.at(index);
        
        // Remove from chart
        m_chart->removeSeries(series);
        
        // Remove from our lists
        m_series.removeAt(index);
        m_seriesNames.removeAt(index);
        m_seriesColors.removeAt(index);
        
        // Delete the series
        delete series;
        
        // Update axes
        updateAxes();
    }
}

void ChartWidget::clearAllSeries()
{
    LOG_INFO("Clearing all series", "ChartWidget");
    
    // Remove XY series
    for (QXYSeries *series : m_series) {
        m_chart->removeSeries(series);
        delete series;
    }
    m_series.clear();
    m_seriesNames.clear();
    m_seriesColors.clear();
    
    // Remove bar series and sets
    if (m_barSeries) {
        m_chart->removeSeries(m_barSeries);
        m_barSets.clear(); // QBarSeries owns the bar sets, so we don't delete them
        delete m_barSeries;
        m_barSeries = nullptr;
    }
    
    // Remove pie series and slices
    if (m_pieSeries) {
        m_chart->removeSeries(m_pieSeries);
        m_pieSlices.clear(); // QPieSeries owns the slices, so we don't delete them
        delete m_pieSeries;
        m_pieSeries = nullptr;
    }
}

void ChartWidget::addDataPoint(int seriesIndex, qreal x, qreal y)
{
    if (seriesIndex >= 0 && seriesIndex < m_series.count()) {
        QXYSeries *series = m_series.at(seriesIndex);
        series->append(x, y);
        
        // Update axes range
        updateAxes();
    }
}

void ChartWidget::setSeriesData(int seriesIndex, const QVector<qreal> &xValues, const QVector<qreal> &yValues)
{
    if (seriesIndex >= 0 && seriesIndex < m_series.count() && xValues.size() == yValues.size()) {
        QXYSeries *series = m_series.at(seriesIndex);
        
        // Clear existing data
        series->clear();
        
        // Add new data points
        for (int i = 0; i < xValues.size(); ++i) {
            series->append(xValues.at(i), yValues.at(i));
        }
        
        // Update axes range
        updateAxes();
    }
}

void ChartWidget::setBarCategories(const QStringList &categories)
{
    m_barCategories = categories;
    
    if (m_chartType == BarChart && m_barAxisX) {
        m_barAxisX->setCategories(categories);
    }
}

void ChartWidget::setPieData(const QStringList &labels, const QVector<qreal> &values)
{
    if (labels.size() != values.size()) {
        LOG_WARNING("Labels and values counts don't match", "ChartWidget");
        return;
    }
    
    // Clear existing pie series
    if (m_pieSeries) {
        m_chart->removeSeries(m_pieSeries);
        m_pieSlices.clear();
        delete m_pieSeries;
    }
    
    // Create new pie series
    m_pieSeries = new QPieSeries();
    
    // Add slices
    for (int i = 0; i < labels.size(); ++i) {
        QPieSlice *slice = new QPieSlice(labels.at(i), values.at(i));
        m_pieSeries->append(slice);
        m_pieSlices.append(slice);
        
            // Connect slice signal - using lambda to bridge the parameter mismatch
    connect(slice, &QPieSlice::clicked, this, [this, slice]() {
        this->onPieSliceClicked(slice);
    });
        
        // Set label visible
        slice->setLabelVisible(true);
        
        // Set random color
        slice->setBrush(QColor(
            QRandomGenerator::global()->bounded(200) + 20,
            QRandomGenerator::global()->bounded(200) + 20,
            QRandomGenerator::global()->bounded(200) + 20
        ));
    }
    
    // Add to chart
    m_chart->addSeries(m_pieSeries);
}

void ChartWidget::setLegendVisible(bool visible)
{
    m_chart->legend()->setVisible(visible);
}

void ChartWidget::setToolBarEnabled(bool enabled)
{
    if (m_toolBarEnabled != enabled) {
        m_toolBarEnabled = enabled;
        
        if (m_toolBar) {
            m_toolBar->setVisible(enabled);
        } else if (enabled) {
            createToolBar();
            createConnections();
        }
    }
}

void ChartWidget::setAnimationEnabled(bool enabled)
{
    m_animationEnabled = enabled;
    m_chart->setAnimationOptions(enabled ? QChart::SeriesAnimations : QChart::NoAnimation);
}

void ChartWidget::setThemeSelectEnabled(bool enabled)
{
    if (m_themeSelectEnabled != enabled) {
        m_themeSelectEnabled = enabled;
        
        // Recreate toolbar to reflect change
        if (m_toolBar) {
            delete m_toolBar;
            m_toolBar = nullptr;
            
            if (m_toolBarEnabled) {
                createToolBar();
                createConnections();
            }
        }
    }
}

QChart* ChartWidget::chart() const
{
    return m_chart;
}

QChartView* ChartWidget::chartView() const
{
    return m_chartView;
} 

void ChartWidget::saveChart(const QString &filePath)
{
    LOG_INFO("Saving chart as image", "ChartWidget");
    
    QString path = filePath;
    
    if (path.isEmpty()) {
        path = QFileDialog::getSaveFileName(this, tr("Save Chart"),
                                           QDir::homePath(),
                                           tr("Images (*.png *.jpg *.jpeg *.bmp)"));
        if (path.isEmpty()) {
            return;
        }
    }
    
    QPixmap pixmap = m_chartView->grab();
    pixmap.save(path);
}

void ChartWidget::printChart()
{
    LOG_INFO("Printing chart", "ChartWidget");
    
    QPrinter printer(QPrinter::HighResolution);
    
    QPrintDialog dialog(&printer, this);
    if (dialog.exec() == QDialog::Accepted) {
        QPainter painter(&printer);
        m_chartView->render(&painter);
        painter.end();
    }
}

void ChartWidget::setTheme(QChart::ChartTheme theme)
{
    m_chart->setTheme(theme);
    
    // Update UI
    if (m_themeCombo && m_themeSelectEnabled) {
        m_themeCombo->setCurrentIndex(m_themeCombo->findData(theme));
    }
}

void ChartWidget::zoomToData()
{
    updateAxes();
}

void ChartWidget::resetZoom()
{
    m_chartView->chart()->zoomReset();
}

void ChartWidget::onChartTypeChanged(int index)
{
    if (index >= 0) {
        ChartType type = static_cast<ChartType>(m_chartTypeCombo->itemData(index).toInt());
        setChartType(type);
    }
}

void ChartWidget::onThemeChanged(int index)
{
    if (index >= 0 && m_themeCombo) {
        QChart::ChartTheme theme = static_cast<QChart::ChartTheme>(m_themeCombo->itemData(index).toInt());
        setTheme(theme);
    }
}

void ChartWidget::onExportChart()
{
    saveChart();
}

void ChartWidget::onAddSeries()
{
    bool ok;
    QString name = QInputDialog::getText(this, tr("Add Series"),
                                        tr("Series name:"), QLineEdit::Normal,
                                        QString("Series %1").arg(m_series.count() + 1), &ok);
    if (ok && !name.isEmpty()) {
        QColor color = QColorDialog::getColor(Qt::blue, this, tr("Choose Series Color"));
        if (color.isValid()) {
            int index = addSeries(name, color);
            
            // Add sample data for the new series
            if (m_chartType == LineChart || m_chartType == ScatterChart) {
                for (int i = 0; i < 10; ++i) {
                    addDataPoint(index, i, QRandomGenerator::global()->bounded(10));
                }
            }
        }
    }
}

void ChartWidget::onEditSeries()
{
    if (m_series.isEmpty()) {
        return;
    }
    
    // Create a list of series names
    QStringList items;
    for (const QString &name : m_seriesNames) {
        items << name;
    }
    
    bool ok;
    QString item = QInputDialog::getItem(this, tr("Edit Series"),
                                        tr("Select series:"), items, 0, false, &ok);
    if (ok && !item.isEmpty()) {
        int index = m_seriesNames.indexOf(item);
        if (index >= 0) {
            // Prompt for new name
            QString newName = QInputDialog::getText(this, tr("Edit Series"),
                                                  tr("New series name:"), QLineEdit::Normal,
                                                  item, &ok);
            if (ok && !newName.isEmpty()) {
                m_seriesNames[index] = newName;
                m_series[index]->setName(newName);
                
                // Prompt for new color
                QColor newColor = QColorDialog::getColor(m_seriesColors[index], this, 
                                                       tr("Choose New Series Color"));
                if (newColor.isValid()) {
                    m_seriesColors[index] = newColor;
                    m_series[index]->setPen(QPen(newColor, 2));
                }
            }
        }
    }
}

void ChartWidget::onRemoveSeries()
{
    if (m_series.isEmpty()) {
        return;
    }
    
    // Create a list of series names
    QStringList items;
    for (const QString &name : m_seriesNames) {
        items << name;
    }
    
    bool ok;
    QString item = QInputDialog::getItem(this, tr("Remove Series"),
                                        tr("Select series to remove:"), items, 0, false, &ok);
    if (ok && !item.isEmpty()) {
        int index = m_seriesNames.indexOf(item);
        if (index >= 0) {
            removeSeries(index);
        }
    }
}

void ChartWidget::onLegendClicked()
{
    // This function is now handled by connecting to individual legend markers
    // after they are created in updateChart method
}

void ChartWidget::onPieSliceClicked(QPieSlice *slice)
{
    // Toggle exploded state of the slice
    slice->setExploded(!slice->isExploded());
} 