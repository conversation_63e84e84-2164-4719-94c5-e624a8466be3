#ifndef SERVICELOCATOR_H
#define SERVICELOCATOR_H

#include <QMap>
#include <QString>
#include <QObject>
#include <typeinfo>
#include <stdexcept>

/**
 * @brief 服务定位器类，用于管理和访问应用程序服务
 * 
 * ServiceLocator提供了一个中心化的服务注册和查找机制，
 * 允许应用程序组件通过类型安全的方式访问共享服务。
 */
class ServiceLocator
{
public:
    /**
     * @brief 构造函数
     */
    ServiceLocator();

    /**
     * @brief 析构函数
     */
    ~ServiceLocator();

    /**
     * @brief 注册服务
     * @tparam T 服务类型
     * @param service 服务实例指针
     */
    template<typename T>
    void registerService(T* service)
    {
        if (!service) {
            throw std::invalid_argument("Cannot register null service");
        }

        QString typeName = QString(typeid(T).name());
        if (m_services.contains(typeName)) {
            throw std::runtime_error(QString("Service of type %1 already registered").arg(typeName).toStdString());
        }

        m_services[typeName] = service;
    }

    /**
     * @brief 获取服务
     * @tparam T 服务类型
     * @return 服务实例指针
     * @throws std::runtime_error 如果服务未注册
     */
    template<typename T>
    T* getService() const
    {
        QString typeName = QString(typeid(T).name());
        if (!m_services.contains(typeName)) {
            throw std::runtime_error(QString("Service of type %1 not registered").arg(typeName).toStdString());
        }

        return static_cast<T*>(m_services[typeName]);
    }

    /**
     * @brief 检查服务是否已注册
     * @tparam T 服务类型
     * @return 服务是否已注册
     */
    template<typename T>
    bool hasService() const
    {
        QString typeName = QString(typeid(T).name());
        return m_services.contains(typeName);
    }

    /**
     * @brief 注销服务
     * @tparam T 服务类型
     */
    template<typename T>
    void unregisterService()
    {
        QString typeName = QString(typeid(T).name());
        if (m_services.contains(typeName)) {
            m_services.remove(typeName);
        }
    }

    /**
     * @brief 清理所有服务
     */
    void cleanup();

private:
    QMap<QString, QObject*> m_services;
};

#endif // SERVICELOCATOR_H
