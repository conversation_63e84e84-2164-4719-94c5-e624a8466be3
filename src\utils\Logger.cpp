#include "Logger.h"
#include <QDir>
#include <QStandardPaths>
#include <QCoreApplication>

Logger* Logger::m_instance = nullptr;
QMutex Logger::m_mutex;

Logger* Logger::instance()
{
    if (!m_instance) {
        QMutexLocker locker(&m_mutex);
        if (!m_instance) {
            m_instance = new Logger();
        }
    }
    return m_instance;
}

Logger::Logger(QObject *parent)
    : QObject(parent)
    , m_logLevel(LogLevel::Info)
    , m_logToFile(true)
    , m_logFile(nullptr)
    , m_logStream(nullptr)
{
    // Set default log file path
    QString logDir = Utils::getLogPath();
    Utils::ensureDirectoryExists(logDir);
    
            QString logFileName = QString("%1_%2.log")
                                .arg(AppConstants::APP_NAME)
                         .arg(QDateTime::currentDateTime().toString("yyyyMMdd"));
    
    m_logFilePath = QDir(logDir).absoluteFilePath(logFileName);
    
    if (m_logToFile) {
        m_logFile = new QFile(m_logFilePath);
        if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
            m_logStream = new QTextStream(m_logFile);
            m_logStream->setCodec("UTF-8");
        }
    }
}

Logger::~Logger()
{
    if (m_logStream) {
        delete m_logStream;
        m_logStream = nullptr;
    }
    
    if (m_logFile) {
        if (m_logFile->isOpen()) {
            m_logFile->close();
        }
        delete m_logFile;
        m_logFile = nullptr;
    }
}

void Logger::setLogLevel(LogLevel level)
{
    m_logLevel = level;
}

LogLevel Logger::getLogLevel() const
{
    return m_logLevel;
}

void Logger::setLogToFile(bool enabled)
{
    QMutexLocker locker(&m_fileMutex);
    
    if (m_logToFile == enabled) {
        return;
    }
    
    m_logToFile = enabled;
    
    if (enabled && !m_logFile) {
        m_logFile = new QFile(m_logFilePath);
        if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
            m_logStream = new QTextStream(m_logFile);
            m_logStream->setCodec("UTF-8");
        }
    } else if (!enabled && m_logFile) {
        if (m_logStream) {
            delete m_logStream;
            m_logStream = nullptr;
        }
        
        if (m_logFile->isOpen()) {
            m_logFile->close();
        }
        delete m_logFile;
        m_logFile = nullptr;
    }
}

bool Logger::isLogToFileEnabled() const
{
    return m_logToFile;
}

void Logger::setLogFilePath(const QString& path)
{
    QMutexLocker locker(&m_fileMutex);
    
    if (m_logFilePath == path) {
        return;
    }
    
    m_logFilePath = path;
    
    if (m_logToFile) {
        // Reopen file
        if (m_logStream) {
            delete m_logStream;
            m_logStream = nullptr;
        }
        
        if (m_logFile) {
            if (m_logFile->isOpen()) {
                m_logFile->close();
            }
            delete m_logFile;
        }
        
        m_logFile = new QFile(m_logFilePath);
        if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
            m_logStream = new QTextStream(m_logFile);
            m_logStream->setCodec("UTF-8");
        }
    }
}

QString Logger::getLogFilePath() const
{
    return m_logFilePath;
}

void Logger::log(LogLevel level, const QString& message, const QString& category)
{
    if (level < m_logLevel) {
        return;
    }
    
    QString formattedMessage = formatMessage(level, message, category);
    
    // Output to console
    switch (level) {
        case LogLevel::Debug:
            qDebug().noquote() << formattedMessage;
            break;
        case LogLevel::Info:
            qInfo().noquote() << formattedMessage;
            break;
        case LogLevel::Warning:
            qWarning().noquote() << formattedMessage;
            break;
        case LogLevel::Error:
            qCritical().noquote() << formattedMessage;
            break;
        case LogLevel::Critical:
            qCritical().noquote() << formattedMessage;
            break;
    }
    
    // Write to file
    if (m_logToFile) {
        writeToFile(formattedMessage);
    }
    
    // 发送信号
    emit logMessage(level, message, category);
}

void Logger::debug(const QString& message, const QString& category)
{
    log(LogLevel::Debug, message, category);
}

void Logger::info(const QString& message, const QString& category)
{
    log(LogLevel::Info, message, category);
}

void Logger::warning(const QString& message, const QString& category)
{
    log(LogLevel::Warning, message, category);
}

void Logger::error(const QString& message, const QString& category)
{
    log(LogLevel::Error, message, category);
}

void Logger::critical(const QString& message, const QString& category)
{
    log(LogLevel::Critical, message, category);
}

void Logger::writeToFile(const QString& formattedMessage)
{
    QMutexLocker locker(&m_fileMutex);
    
    if (m_logStream) {
        *m_logStream << formattedMessage << Qt::endl;
        m_logStream->flush();
    }
}

QString Logger::formatMessage(LogLevel level, const QString& message, const QString& category)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString levelStr = logLevelToString(level);
    
    if (category.isEmpty()) {
        return QString("[%1] [%2] %3").arg(timestamp, levelStr, message);
    } else {
        return QString("[%1] [%2] [%3] %4").arg(timestamp, levelStr, category, message);
    }
}

QString Logger::logLevelToString(LogLevel level)
{
    switch (level) {
        case LogLevel::Debug:    return "DEBUG";
        case LogLevel::Info:     return "INFO";
        case LogLevel::Warning:  return "WARNING";
        case LogLevel::Error:    return "ERROR";
        case LogLevel::Critical: return "CRITICAL";
        default:                 return "UNKNOWN";
    }
} 