#include "Logger.h"
#include <QDir>
#include <QFileInfo>
#include <QMutexLocker>

// Initialize static members
QFile Logger::logFile;
QTextStream Logger::logStream;
QMutex Logger::mutex;
bool Logger::initialized = false;

Logger::Logger()
{
    // Private constructor to prevent instantiation
}

bool Logger::initialize(const QString &logFilePath)
{
    QMutexLocker locker(&mutex);
    
    if (initialized) {
        return true;
    }
    
    // Create directory if it doesn't exist
    QFileInfo fileInfo(logFilePath);
    QDir dir = fileInfo.dir();
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            qDebug() << "Failed to create log directory:" << dir.path();
            return false;
        }
    }
    
    // Open log file
    logFile.setFileName(logFilePath);
    if (!logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        qDebug() << "Failed to open log file:" << logFilePath;
        return false;
    }
    
    logStream.setDevice(&logFile);
    
    initialized = true;
    
    // Write initialization message directly without calling info()
    // to avoid potential recursive locking
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString logMessage = QString("%1 [INFO] [Logger]: Logger initialized\n").arg(timestamp);
    
    logStream << logMessage;
    logStream.flush();
    
    // Also output to debug console
    qDebug().noquote() << logMessage.trimmed();
    
    return true;
}

void Logger::debug(const QString &message, const QString &source)
{
    log(Debug, message, source);
}

void Logger::info(const QString &message, const QString &source)
{
    log(Info, message, source);
}

void Logger::warning(const QString &message, const QString &source)
{
    log(Warning, message, source);
}

void Logger::error(const QString &message, const QString &source)
{
    log(Error, message, source);
}

void Logger::critical(const QString &message, const QString &source)
{
    log(Critical, message, source);
}

void Logger::log(LogLevel level, const QString &message, const QString &source)
{
    // Static flag to prevent recursive initialization
    static bool initializing = false;
    
    if (!initialized && !initializing) {
        initializing = true;
        if (!initialize()) {
            // If initialization fails, output to debug console
            qDebug() << levelToString(level) << (source.isEmpty() ? "" : " [" + source + "]") << ": " << message;
            initializing = false;
            return;
        }
        initializing = false;
    }
    
    QMutexLocker locker(&mutex);
    
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString logMessage = QString("%1 [%2]%3: %4\n")
        .arg(timestamp)
        .arg(levelToString(level))
        .arg(source.isEmpty() ? "" : " [" + source + "]")
        .arg(message);
    
    if (initialized) {
        logStream << logMessage;
        logStream.flush();
    }
    
    // Also output to debug console
    qDebug().noquote() << logMessage.trimmed();
}

QString Logger::levelToString(LogLevel level)
{
    switch (level) {
    case Debug:
        return "DEBUG";
    case Info:
        return "INFO";
    case Warning:
        return "WARNING";
    case Error:
        return "ERROR";
    case Critical:
        return "CRITICAL";
    default:
        return "UNKNOWN";
    }
} 