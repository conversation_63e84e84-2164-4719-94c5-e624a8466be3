# Variables Module

This document contains all variables related documentation organized by hierarchy.

---

## variables

# variables

Specifies the parameter set to be iterated by a particular method.

**Topics**

block

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [id_variables](variables-id_variables.html) | Name the variables block; helpful when there are multiple  
Optional | [active](variables-active.html) | Set the active variables view a method will see  
Optional (Choose One) | Variable Domain | [mixed](variables-mixed.html) | Maintain continuous/discrete variable distinction  
[relaxed](variables-relaxed.html) | Allow treatment of discrete variables as continuous  
Optional | [continuous_design](variables-continuous_design.html) | Design variable - continuous  
Optional | [discrete_design_range](variables-discrete_design_range.html) | Design variable - discrete range-valued  
Optional | [discrete_design_set](variables-discrete_design_set.html) | Design variable - discrete set-valued  
Optional | [normal_uncertain](variables-normal_uncertain.html) | Aleatory uncertain variable - normal (Gaussian)  
Optional | [lognormal_uncertain](variables-lognormal_uncertain.html) | Aleatory uncertain variable - lognormal  
Optional | [uniform_uncertain](variables-uniform_uncertain.html) | Aleatory uncertain variable - uniform  
Optional | [loguniform_uncertain](variables-loguniform_uncertain.html) | Aleatory uncertain variable - loguniform  
Optional | [triangular_uncertain](variables-triangular_uncertain.html) | Aleatory uncertain variable - triangular  
Optional | [exponential_uncertain](variables-exponential_uncertain.html) | Aleatory uncertain variable - exponential  
Optional | [beta_uncertain](variables-beta_uncertain.html) | Aleatory uncertain variable - beta  
Optional | [gamma_uncertain](variables-gamma_uncertain.html) | Aleatory uncertain variable - gamma  
Optional | [gumbel_uncertain](variables-gumbel_uncertain.html) | Aleatory uncertain variable - gumbel  
Optional | [frechet_uncertain](variables-frechet_uncertain.html) | Aleatory uncertain variable - Frechet  
Optional | [weibull_uncertain](variables-weibull_uncertain.html) | Aleatory uncertain variable - Weibull  
Optional | [histogram_bin_uncertain](variables-histogram_bin_uncertain.html) | Aleatory uncertain variable - continuous histogram  
Optional | [poisson_uncertain](variables-poisson_uncertain.html) | Aleatory uncertain discrete variable - Poisson  
Optional | [binomial_uncertain](variables-binomial_uncertain.html) | Aleatory uncertain discrete variable - binomial  
Optional | [negative_binomial_uncertain](variables-negative_binomial_uncertain.html) | Aleatory uncertain discrete variable - negative binomial  
Optional | [geometric_uncertain](variables-geometric_uncertain.html) | Aleatory uncertain discrete variable - geometric  
Optional | [hypergeometric_uncertain](variables-hypergeometric_uncertain.html) | Aleatory uncertain discrete variable - hypergeometric  
Optional | [histogram_point_uncertain](variables-histogram_point_uncertain.html) | Aleatory uncertain variable - discrete histogram  
Optional | [uncertain_correlation_matrix](variables-uncertain_correlation_matrix.html) | Correlation among aleatory uncertain variables  
Optional | [continuous_interval_uncertain](variables-continuous_interval_uncertain.html) | Epistemic uncertain variable - values from one or more continuous intervals  
Optional | [discrete_interval_uncertain](variables-discrete_interval_uncertain.html) | Epistemic uncertain variable - values from one or more discrete intervals  
Optional | [discrete_uncertain_set](variables-discrete_uncertain_set.html) | Epistemic uncertain variable - discrete set-valued  
Optional | [continuous_state](variables-continuous_state.html) | State variable - continuous  
Optional | [discrete_state_range](variables-discrete_state_range.html) | State variables - discrete range-valued  
Optional | [discrete_state_set](variables-discrete_state_set.html) | State variable - discrete set-valued  
Optional | [linear_inequality_constraint_matrix](variables-linear_inequality_constraint_matrix.html) | Define coefficients of the linear inequality constraints  
Optional | [linear_inequality_lower_bounds](variables-linear_inequality_lower_bounds.html) | Define lower bounds for the linear inequality constraint  
Optional | [linear_inequality_upper_bounds](variables-linear_inequality_upper_bounds.html) | Define upper bounds for the linear inequality constraint  
Optional | [linear_inequality_scale_types](variables-linear_inequality_scale_types.html) | How to scale each linear inequality constraint  
Optional | [linear_inequality_scales](variables-linear_inequality_scales.html) | Characteristic values to scale linear inequalities  
Optional | [linear_equality_constraint_matrix](variables-linear_equality_constraint_matrix.html) | Define coefficients of the linear equalities  
Optional | [linear_equality_targets](variables-linear_equality_targets.html) | Define target values for the linear equality constraints  
Optional | [linear_equality_scale_types](variables-linear_equality_scale_types.html) | How to scale each linear equality constraint  
Optional | [linear_equality_scales](variables-linear_equality_scales.html) | Characteristic values to scale linear equalities  
  
**Description**

The `variables` specification in a Dakota input file specifies the parameter set to be iterated by a particular method. In the case of

  * An optimization study: These variables are adjusted in order to locate an optimal design.

  * Parameter studies/sensitivity analysis/design of experiments: These parameters are perturbed to explore the parameter space.

  * Uncertainty analysis: The variables are associated with distribution/interval characterizations which are used to compute corresponding distribution/interval characterizations for response functions.

To accommodate these different studies, Dakota supports different:

  * Variable types: design

    * aleatory uncertain

    * epistemic uncertain

    * state

  * Variable domains: continuous

    * discrete

      * discrete range

      * discrete integer set

      * discrete string set

      * discrete real set

See the variables [Overview](../inputfile/variables.html#variables-overview) for another summary of the available variables by type and domain.

_Variable Types_

  * _Design Variables:_ Design variables are those variables which are modified for the purposes of seeking an optimal design.

    * The most common type of design variables encountered in engineering applications are of the continuous type. These variables may assume any real value within their bounds.

    * All but a handful of the optimization algorithms in Dakota support continuous design variables exclusively.

  * _Aleatory Uncertain Variables:_ Aleatory uncertainty is also known as inherent variability, irreducible uncertainty, or randomness.

    * Aleatory uncertainty is predominantly characterized using probability theory. This is the only option implemented in Dakota.

  * _Epistemic Uncertain Variables:_ Epistemic uncertainty is uncertainty due to lack of knowledge.

    * In Dakota, epistemic uncertainty is assessed by interval analysis or the Dempster-Shafer theory of evidence

    * Continuous or discrete interval or set-valued variables are used to define set-valued probabilities or basic probabiliy assignments (BPA) which define a belief structure.

    * Note that epistemic uncertainty can also be modeled with probability density functions (as done with aleatory uncertainty). Dakota does not support this capability.

  * _State Variables:_ State variables consist of “other” variables which are to be mapped through the simulation interface, in that they are not to be used for design and they are not modeled as being uncertain.

    * State variables provide a convenient mechanism for managing additional model parameterizations such as mesh density, simulation convergence tolerances, and time step controls.

    * Only parameter studies and design of experiments methods will iterate on state variables.

    * The `initial_value` is used as the only value for the state variable for all other methods, unless `active` `state` is invoked.

    * See more details in [State Variables](../inputfile/variables.html#variables-state).

_Variable Domains_

Continuous variables are typically defined by bounds. Discrete variables can be defined in one of three ways, which are discussed on in [Discrete Design Variables](../inputfile/variables.html#variables-design-ddv).

_Ordering of Variables_

The ordering of variables is important, and a consistent ordering is employed throughout the Dakota software. The ordering is shown in dakota.input.summary (and in the hierarchical order of this reference manual) and can be summarized as:

  1. design

     1. continuous

     2. discrete integer

     3. discrete string

     4. discrete real

  2. aleatory uncertain

     1. continuous

     2. discrete integer

     3. discrete string

     4. discrete real

  3. epistemic uncertain

     1. continuous

     2. discrete integer

     3. discrete string

     4. discrete real

  4. state

     1. continuous

     2. discrete integer

     3. discrete string

     4. discrete real

Ordering of variable types below this granularity (e.g., from normal to histogram bin within aleatory uncertain - continuous ) is defined somewhat arbitrarily, but is enforced consistently throughout the code.

_Active Variables_

The reason variable types exist is that methods have the capability to treat variable types differently. All methods have default behavior that determines which variable types are “active” and will be assigned values by the method. For example, optimization methods will only vary the design variables - by default.

The default behavior should be described on each method page, or on topics pages that relate to classes of methods. In addition, the default behavior can be modified using the `[active](../../usingdakota/reference/variables-active.html)` keyword.

At least one type of variables that are active for the method in use must have nonzero size (at least 1 active variable) or an input error message will result.

_Inferred Default Values and Bounds_

The concept of active variables allows any Dakota variable type to be used in any method context. Some methods, e.g., bound-constrained optimization or multi-dimensional or centered parameter studies, require bounds and/or an initial point on the variables, however uncertain variables may not be naturally defined in terms of these characteristics.

Distribution lower and upper bounds are explicit portions of the normal, lognormal, uniform, loguniform, triangular, and beta specifications, whereas they are implicitly defined for others. For example, bounds are naturally defined for histogram bin, histogram point, and interval variables (from the extreme values within the bin/point/interval specifications) as well as for binomial (0 to `num_trials`) and hypergeometric (0 to min( `num_drawn`, `num_selected`)) variables.

If not specified, distribution bounds are also inferred for normal and lognormal (if optional bounds are unspecified) as well as for exponential, gamma, gumbel, frechet, weibull, poisson, negative binomial, and geometric (which have no bounds specifications); these bounds are [0, \\(\mu + 3 \sigma\\) ] for exponential, gamma, frechet, weibull, poisson, negative binomial, geometric, and unspecified lognormal, and [ \\(\mu - 3 \sigma\\) , \\(\mu + 3 \sigma\\) ] for gumbel and unspecified normal.

When an intial point is needed and not explcitly specified in user input, it is assigned as described in the `initial_point` or `initial_state` specification, e.g., `[initial_point](../../usingdakota/reference/variables-normal_uncertain-initial_point.html)`. For example, uncertain variables are initialized to their means, where mean values for bounded normal and bounded lognormal may be further adjusted to satisfy any user-specified distribution bounds, mean values for discrete integer range distributions are rounded down to the nearest integer, and mean values for discrete set distributions are rounded to the nearest set value.

**Examples**

Several examples follow. In the first example, two continuous design variables are specified:

    variables,
     continuous_design = 2
       initial_point    0.9    1.1
       upper_bounds     5.8    2.9
       lower_bounds     0.5   -2.9
       descriptors   'radius' 'location'

In the next example, defaults are employed. In this case, `initial_point` will default to a vector of `0`. values, `upper_bounds` will default to vector values of `DBL_MAX` (the maximum number representable in double precision for a particular platform), `lower_bounds` will default to a vector of `-DBL_MAX` values, and `descriptors` will default to a vector of <tt>’cdv_i’</tt> strings, where `i` ranges from one to two:

    variables,
     continuous_design = 2

In the following example, the syntax for a normal-lognormal distribution is shown. One normal and one lognormal uncertain variable are completely specified by their means and standard deviations. In addition, the dependence structure between the two variables is specified using the `uncertain_correlation_matrix`.

    variables,
            normal_uncertain    =  1
              means             =  1.0
              std_deviations    =  1.0
              descriptors       =  'TF1n'
            lognormal_uncertain =  1
              means             =  2.0
              std_deviations    =  0.5
              descriptors       =  'TF2ln'
            uncertain_correlation_matrix =  1.0 0.2
                                            0.2 1.0

An example of the syntax for a state variables specification follows:

    variables,
            continuous_state = 1
              initial_state       4.0
              lower_bounds        0.0
              upper_bounds        8.0
              descriptors        'CS1'
            discrete_state_range = 1
              initial_state       104
              lower_bounds        100
              upper_bounds        110
              descriptors        'DS1'

And in a more advanced example, a variables specification containing a set identifier, continuous and discrete design variables, normal and uniform uncertain variables, and continuous and discrete state variables is shown:

    variables,
     id_variables = 'V1'
     continuous_design = 2
       initial_point    0.9    1.1
       upper_bounds     5.8    2.9
       lower_bounds     0.5   -2.9
       descriptors   'radius' 'location'
     discrete_design_range = 1
       initial_point    2
       upper_bounds     1
       lower_bounds     3
       descriptors   'material'
     normal_uncertain = 2
       means          =  248.89, 593.33
       std_deviations =   12.4,   29.7
       descriptors    =  'TF1n'   'TF2n'
     uniform_uncertain = 2
       lower_bounds =  199.3,  474.63
       upper_bounds =  298.5,  712.
       descriptors  =  'TF1u'   'TF2u'
     continuous_state = 2
       initial_state = 1.e-4  1.e-6
       descriptors   = 'EPSIT1' 'EPSIT2'
     discrete_state_set
              integer = 1
         initial_state = 100
         set_values    = 100 212 375
         descriptors   = 'load_case'


---

### variables → active

# active

Set the active variables view a method will see

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ Infer from response or method specification

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Active Variables | [all](variables-active-all.html) | Option for the `active` keyword  
[design](variables-active-design.html) | Option for the `active` keyword  
[uncertain](variables-active-uncertain.html) | Option for the `active` keyword  
[aleatory](variables-active-aleatory.html) | Option for the `active` keyword  
[epistemic](variables-active-epistemic.html) | Option for the `active` keyword  
[state](variables-active-state.html) | Option for the `active` keyword  
  
**Description**

There are certain situations where the user may want to explicitly control the subset of variables that is considered active for a certain Dakota method. This is done by specifying the keyword `active` in the variables specification block, followed by one of the following: `all`, `design`, `uncertain`, `aleatory`, `epistemic`, or `state`.

Specifying one of these subsets of variables will allow the Dakota method to operate on the specified variable types and override the default active subset.

If the user does not specify any explicit override of the active view of the variables, Dakota first considers the response function specification.

  * If the user specifies objectivefunctions or calibration terms in the response specification block, then we can infer that the active variables should be the design variables (since design variables are used within optimization and least squares methods).

  * If the user instead specifies the genericresponse type of `response_functions`, then Dakota cannot infer the active variable subset from the response specification and will instead infer it from the method selection.

    1. If the method is a parameter study, or any of the methods available under dace, psuade, or fsu methods, the active view is set to all variables.

    2. For uncertainty quantification methods, if the method is sampling, then the view is set to aleatory if only aleatory variables are present, epistemic if only epistemic variables are present, or uncertain (covering both aleatory and epistemic) if both are present.

    3. If the uncertainty method involves interval estimation or evidence calculations, the view is set to epistemic.

    4. For other uncertainty quantification methods not mentioned in the previous sentences (e.g., reliability methods or stochastic expansion methods), the default view is set to aleatory.

    5. Finally, for verification studies using the Richardson extrapolation method, the active view is set to state.

    6. Note that in surrogate-based optimization, where the surrogate is built on points defined by the method defined by the `dace_method_pointer`, the sampling used to generate the points is performed only over the design variables as a default unless otherwise specified (e.g. state variables will not be sampled for surrogate construction).

As alluded to in the previous section, the iterative method selected for use in Dakota determines what subset, or view, of the variables data is active in the iteration. The general case of having a mixture of various different types of variables is supported within all of the Dakota methods even though certain methods will only modify certain types of variables (e.g., optimizers and least squares methods only modify design variables, and uncertainty quantification methods typically only utilize uncertain variables). This implies that variables which are not under the direct control of a particular iterator will be mapped through the interface in an unmodified state. This allows for a variety of parameterizations within the model in addition to those which are being used by a particular iterator, which can provide the convenience of consolidating the control over various modeling parameters in a single file (the Dakota input file). An important related point is that the variable set that is active with a particular iterator is the same variable set for which derivatives are typically computed.

**Examples**

For example, the default behavior for a nondeterministic sampling method is to sample the uncertain variables. However, if the user specifed `active all` in the variables specification block, the sampling would be performed over all variables (e.g. design and state variables in addition to the uncertain variables). This may be desired in situations such as surrogate-based optimization under uncertainty, where a surrogate may be constructed to span both design and uncertain variables. This is an example where we expand the active subset beyond the default, but in other situations, we may wish to restrict from the default. An example of this would be performing design of experiments in the presence of multiple variable types (for which all types are active by default), but only wanting to sample over the design variables for purposes of constructing a surrogate model for optimization.

**Theory**

The optional status of the different variable type specifications allows the user to specify only those variables which are present (rather than explicitly specifying that the number of a particular type of variables is zero). However, at least one type of variables that are active for the iterator in use must have nonzero size or an input error message will result.


---

#### variables → active → aleatory

# aleatory

Option for the `active` keyword

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See the `[active](../../usingdakota/reference/variables-active.html)` keyword


---

#### variables → active → all

# all

Option for the `active` keyword

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See the `[active](../../usingdakota/reference/variables-active.html)` keyword


---

#### variables → active → design

# design

Option for the `active` keyword

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See the `[active](../../usingdakota/reference/variables-active.html)` keyword


---

#### variables → active → epistemic

# epistemic

Option for the `active` keyword

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See the `[active](../../usingdakota/reference/variables-active.html)` keyword


---

#### variables → active → state

# state

Option for the `active` keyword

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See the `[active](../../usingdakota/reference/variables-active.html)` keyword


---

#### variables → active → uncertain

# uncertain

Option for the `active` keyword

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See the `[active](../../usingdakota/reference/variables-active.html)` keyword


---

### variables → beta_uncertain

# beta_uncertain

Aleatory uncertain variable - beta

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no beta uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [alphas](variables-beta_uncertain-alphas.html) | First parameter of the beta distribution  
Required | [betas](variables-beta_uncertain-betas.html) | Second parameter of the beta distribution  
Required | [lower_bounds](variables-beta_uncertain-lower_bounds.html) | Specify minimum values  
Required | [upper_bounds](variables-beta_uncertain-upper_bounds.html) | Specify maximium values  
Optional | [initial_point](variables-beta_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-beta_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The number of beta uncertain variables, the alpha and beta parameters, and the distribution upper and lower bounds are required specifications, while the variable descriptors is an optional specification. The beta distribution can be helpful when the actual distribution of an uncertain variable is unknown, but the user has a good idea of the bounds, the mean, and the standard deviation of the uncertain variable. The density function for the beta distribution is

\\[f(x)= \frac{\Gamma(\alpha+\beta)}{\Gamma(\alpha)\Gamma(\beta)}\frac{(x-L)^{\alpha-1}(U-x)^{\beta-1}}{(U-L)^{\alpha+\beta-1}},\\]

where \\(\Gamma(\alpha)\\) is the gamma function and

\\[`B(\alpha, \beta) = \frac{\Gamma(\alpha)\Gamma(\beta)}{\Gamma(\alpha+\beta)}`\\]

is the beta function. To calculate the mean and standard deviation from the alpha, beta, upper bound, and lower bound parameters of the beta distribution, the following expressions may be used.

\\[\mu = L+\frac{\alpha}{\alpha+\beta}(U-L)\\]

\\[\sigma^2 =\frac{\alpha\beta}{(\alpha+\beta)^2(\alpha+\beta+1)}(U-L)^2\\]

Solving these for \\(\alpha\\) and \\(\beta\\) gives:

\\[\alpha = (\mu-L)\frac{(\mu-L)(U-\mu)-\sigma^2}{\sigma^2(U-L)}\\]

\\[\beta = (U-\mu)\frac{(\mu-L)(U-\mu)-\sigma^2}{\sigma^2(U-L)}\\]

Note that the uniform distribution is a special case of this distribution for parameters \\(\alpha = \beta = 1\\) .

**Theory**

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means.


---

#### variables → beta_uncertain → alphas

# alphas

First parameter of the beta distribution

**Specification**

  * _Alias:_ buv_alphas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\alpha\\) parameters to define the distributions of the beta random variables. Length must match the other parameters and the number of beta random variables.


---

#### variables → beta_uncertain → betas

# betas

Second parameter of the beta distribution

**Specification**

  * _Alias:_ buv_betas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\beta\\) parameters to define the distributions of the beta random variables. Length must match the other parameters and the number of beta random variables.


---

#### variables → beta_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ buv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ buv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → beta_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → beta_uncertain → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ buv_lower_bounds

  * _Arguments:_ REALLIST

**Description**

Specify minimum values


---

#### variables → beta_uncertain → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ buv_upper_bounds

  * _Arguments:_ REALLIST

**Description**

Specify maximium values


---

### variables → binomial_uncertain

# binomial_uncertain

Aleatory uncertain discrete variable - binomial

**Topics**

discrete_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no binomial uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [probability_per_trial](variables-binomial_uncertain-probability_per_trial.html) | A distribution parameter for the binomial distribution  
Required | [num_trials](variables-binomial_uncertain-num_trials.html) | A distribution parameter  
Optional | [initial_point](variables-binomial_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-binomial_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The binomial distribution describes probabilities associated with a series of independent Bernoulli trials. A Bernoulli trial is an event with two mutually exclusive outcomes, such as 0 or 1, yes or no, success or fail. The probability of success remains the same (the trials are independent).

The density function for the binomial distribution is given by:

\\[\begin{split}f(x) = \left(\begin{array}{c}n\\\x\end{array}\right){p^x}{(1-p)^{(n-x)}},\end{split}\\]

where \\(p\\) is the probability of failure per trial, \\(n\\) is the number of trials and \\(x\\) is the number of successes.

**Theory**

The binomial distribution is typically used to predict the number of failures or defective items in a total of \\(n\\) independent tests or trials, where each trial has the probability \\(p\\) of failing or being defective.


---

#### variables → binomial_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ biuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → binomial_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → binomial_uncertain → num_trials

# num_trials

A distribution parameter

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The binomial distribution is typically used to predict the number of failures (or defective items or some type of event) in a total of `n` independent tests or trials, where each trial has the probability `p` of failing or being defective. Each particular test can be considered as a Bernoulli trial.


---

#### variables → binomial_uncertain → probability_per_trial

# probability_per_trial

A distribution parameter for the binomial distribution

**Specification**

  * _Alias:_ prob_per_trial

  * _Arguments:_ REALLIST

**Description**

The binomial distribution is typically used to predict the number of failures (or defective items or some type of event) in a total of `n` independent tests or trials, where each trial has the probability `p` of failing or being defective. Each particular test can be considered as a Bernoulli trial.


---

### variables → continuous_design

# continuous_design

Design variable - continuous

**Topics**

continuous_variables, design_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no continuous design variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [initial_point](variables-continuous_design-initial_point.html) | Initial values for variables  
Optional | [lower_bounds](variables-continuous_design-lower_bounds.html) | Specify minimum values  
Optional | [upper_bounds](variables-continuous_design-upper_bounds.html) | Specify maximium values  
Optional | [scale_types](variables-continuous_design-scale_types.html) | How to scale each continuous design variable  
Optional | [scales](variables-continuous_design-scales.html) | Characteristic values to scale continuous design variables  
Optional | [descriptors](variables-continuous_design-descriptors.html) | Labels for the variables  
  
**Description**

Continuous variables are defined by a real interval and are changed during the search for the optimal design.


---

#### variables → continuous_design → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ cdv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ cdv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → continuous_design → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ cdv_initial_point

  * _Arguments:_ REALLIST

  * _Default:_ 0.0

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → continuous_design → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ cdv_lower_bounds

  * _Arguments:_ REALLIST

  * _Default:_ -infinity

**Description**

Specify minimum values


---

#### variables → continuous_design → scale_types

# scale_types

How to scale each continuous design variable

**Specification**

  * _Alias:_ cdv_scale_types

  * _Arguments:_ STRINGLIST

  * _Default:_ vector values = ‘none’

**Description**

Each string in `scale_types` indicates the scaling type for each continuous design variable. They only have effect when the associated method specifies `scaling`.

The options are:

  * <tt>’value’</tt> \- characteristic value by which variables will be divided. If this is chosen, then `[scales](../../usingdakota/reference/variables-continuous_design-scales.html)` must also be specified; ‘value’ is assumed if scales are given without `scale_types`

  * <tt>’auto’</tt> \- automatic scaling based on bounds.

  * <tt>’log’</tt> \- logarithmic scaling (can be used together with `[scales](../../usingdakota/reference/variables-continuous_design-scales.html)`).

If a single string is specified it will apply to all continuous design variables. Otherwise, a scale type must be specified for each continuous design variable.

_Usage Tips:_

See the scaling information under specific methods, e.g., `method-*-scaling` for details on how to use this keyword.

**Examples**

Two continuous design variables, one scaled by 4.0, the other by 0.1, then further log scaled:

    continuous_design = 2
      initial_point    -1.2      1.0
      lower_bounds     -2.0      0.001
      upper_bounds      2.0      2.0
      descriptors       'x1'     "x2"
      scale_types =     'value' 'log'
      scales =          4.0      0.1


---

#### variables → continuous_design → scales

# scales

Characteristic values to scale continuous design variables

**Specification**

  * _Alias:_ cdv_scales

  * _Arguments:_ REALLIST

  * _Default:_ vector values = 1 . (no scaling)

**Description**

Each real value in `scales` is a nonzero characteristic value to be used in scaling each continuous design variable. They only have effect when the associated method specifies `scaling`.

This keyword is required for `[scale_types](../../usingdakota/reference/variables-continuous_design-scale_types.html)` of <tt>’value’</tt> and optional for <tt>’auto’</tt> and <tt>’log’</tt>. When specified in conjunction with log, scale values are applied prior to the logarithm, to permit log scaling of strictly negative design variables.

If a single real value is specified it will apply to all continuous design variables. Otherwise, a scale value must be specified for each continuous design variable.

_Usage Tips:_

When `scales` are specified, but not `[scale_types](../../usingdakota/reference/variables-continuous_design-scale_types.html)`, the scaling type is assumed to be ‘value’ for all continuous design variables.

Use scale values of 1.0 to selectively avoid scaling a subset of the variables.

Scaling for linear constraints is applied _after_ any continuous variable scaling.

See the scaling information under specific methods, e.g., `method-*-scaling` for details on how to use this keyword.

**Examples**

Two continuous design variables, one scaled by the characteristic value 4.0, the other log-scaled without additional value scaling

    continuous_design = 2
      initial_point    -1.2      1.0
      lower_bounds     -200      0.001
      upper_bounds      200      2.0
      descriptors       'x1'     "x2"
      scale_types = 'value' 'log'
      scales = 4.0 1.0


---

#### variables → continuous_design → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ cdv_upper_bounds

  * _Arguments:_ REALLIST

  * _Default:_ infinity

**Description**

Specify maximium values


---

### variables → continuous_interval_uncertain

# continuous_interval_uncertain

Epistemic uncertain variable - values from one or more continuous intervals

**Topics**

continuous_variables, epistemic_uncertain_variables

**Specification**

  * _Alias:_ interval_uncertain

  * _Arguments:_ INTEGER

  * _Default:_ no continuous interval uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [num_intervals](variables-continuous_interval_uncertain-num_intervals.html) | Specify the number of intervals for each variable  
Optional | [interval_probabilities](variables-continuous_interval_uncertain-interval_probabilities.html) | Assign probability mass to each interval  
Required | [lower_bounds](variables-continuous_interval_uncertain-lower_bounds.html) | Specify minimum values  
Required | [upper_bounds](variables-continuous_interval_uncertain-upper_bounds.html) | Specify maximium values  
Optional | [initial_point](variables-continuous_interval_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-continuous_interval_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

Continuous interval uncertain variables are epistemic types. They can specify a single interval per variable which may be used in interval analysis, where the goal is to determine the interval bounds on the output corresponding to the interval bounds on the input. All values between the bounds are permissible. More detailed continuous interval representations can specify a set of belief structures based on intervals that may be contiguous, overlapping, or disjoint. This is used in specifying the inputs necessary for an epistemic uncertainty analysis using Dempster-Shafer theory of evidence.

Other epistemic types include:

  * `[discrete_interval_uncertain](../../usingdakota/reference/variables-discrete_interval_uncertain.html)`

  * `[integer](../../usingdakota/reference/variables-discrete_uncertain_set-integer.html)`

  * `[string](../../usingdakota/reference/variables-discrete_uncertain_set-string.html)`

  * `[real](../../usingdakota/reference/variables-discrete_uncertain_set-real.html)`

**Examples**

The following specification is for an interval analysis:

    continuous_interval_uncertain = 2
     lower_bounds = 2.0 4.0
     upper_bounds = 2.5 5.0

The following specification is for a Dempster-Shafer analysis:

    continuous_interval_uncertain = 2
     num_intervals = 3 2
     interval_probs = 0.25 0.5 0.25 0.4 0.6
     lower_bounds = 2.0 4.0 4.5 1.0 3.0
     upper_bounds = 2.5 5.0 6.0 5.0 5.0

Here there are 2 interval uncertain variables. The first one is defined by three intervals, and the second by two intervals. The three intervals for the first variable have basic probability assignments of 0.2, 0.5, and 0.3, respectively, while the basic probability assignments for the two intervals for the second variable are 0.4 and 0.6. The basic probability assignments for each interval variable must sum to one. The interval bounds for the first variable are [2, 2.5], [4, 5], and [4.5, 6], and the interval bounds for the second variable are [1.0, 5.0] and [3.0, 5.0]. Note that the intervals can be overlapping or disjoint. The BPA for the first variable indicates that it is twice as likely that the value occurs on the interval [4,5] than either [2,2.5] or [4.5,6].

**Theory**

The continuous interval uncertain variable is NOT a probability distribution. Although it may seem similar to a histogram, the interpretation of this uncertain variable is different. It is used in epistemic uncertainty analysis, where one is trying to model uncertainty due to lack of knowledge. The continuous interval uncertain variable is used in both interval analysis and in Dempster-Shafer theory of evidence.

**Interval Analysis**

  * Only one interval is allowed for each `continuous_interval_uncertain` variable

  * The interval is defined by lower and upper bounds

  * The value of the random variable lies somewhere in this interval

  * Output is the minimum and maximum function value conditional on the specified interval

**Dempster-Shafer Theory of Evidence**

  * Multiple intervals can be assigned to each `continuous_interval_uncertain` variable

  * A Basic Probability Assignment (BPA) is associated with each interval. The BPA represents a probability that the value of the uncertain variable is located within that interval.

  * Each interval is defined by lower and upper bounds

  * Outputs are called “belief” and “plausibility.” Belief represents the smallest possible probability that is consistent with the evidence, while plausibility represents the largest possible probability that is consistent with the evidence. Evidence is the intervals together with their BPA.


---

#### variables → continuous_interval_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ iuv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ ciuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → continuous_interval_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → continuous_interval_uncertain → interval_probabilities

# interval_probabilities

Assign probability mass to each interval

**Specification**

  * _Alias:_ interval_probs iuv_interval_probs

  * _Arguments:_ REALLIST

  * _Default:_ Equal probability assignments for each interval (1/num_intervals[i])

**Description**

The basic probability assignments for each interval variable must sum to one. For example, if an interval variable is defined with three intervals, the probabilities for these intervals could be 0.2, 0.5, and 0.3 which sum to one, but could not be 0.5,0.5, and 0.5 which do not sum to one.


---

#### variables → continuous_interval_uncertain → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specify minimum values


---

#### variables → continuous_interval_uncertain → num_intervals

# num_intervals

Specify the number of intervals for each variable

**Specification**

  * _Alias:_ iuv_num_intervals

  * _Arguments:_ INTEGERLIST

  * _Default:_ Equal apportionment of intervals among variables

**Description**

In Dakota, epistemic uncertainty analysis is performed using either interval estimation or Dempster-Shafer theory of evidence. In these approaches, one does not assign a probability distribution to each uncertain input variable. Rather, one divides each uncertain input variable into one or more intervals. The input parameters are only known to occur within intervals; nothing more is assumed. `num_intervals` specifies the number of such intervals associated with each interval uncertain parameter.


---

#### variables → continuous_interval_uncertain → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specify maximium values


---

### variables → continuous_state

# continuous_state

State variable - continuous

**Topics**

state_variables, continuous_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ No continuous state variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [initial_state](variables-continuous_state-initial_state.html) | Initial values for variables  
Optional | [lower_bounds](variables-continuous_state-lower_bounds.html) | Specify minimum values  
Optional | [upper_bounds](variables-continuous_state-upper_bounds.html) | Specify maximium values  
Optional | [descriptors](variables-continuous_state-descriptors.html) | Labels for the variables  
  
**Description**

Continuous state variables are defined by bounds.

Default behavior for most methods is that only the initial_state values are used.

See [State Variables](../inputfile/variables.html#variables-state) for details on the behavior of state variables.


---

#### variables → continuous_state → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ csv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ csv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → continuous_state → initial_state

# initial_state

Initial values for variables

**Specification**

  * _Alias:_ csv_initial_state

  * _Arguments:_ REALLIST

  * _Default:_ 0.0

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → continuous_state → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ csv_lower_bounds

  * _Arguments:_ REALLIST

  * _Default:_ -infinity

**Description**

Specify minimum values


---

#### variables → continuous_state → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ csv_upper_bounds

  * _Arguments:_ REALLIST

  * _Default:_ infinity

**Description**

Specify maximium values


---

### variables → discrete_design_range

# discrete_design_range

Design variable - discrete range-valued

**Topics**

discrete_variables, design_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete design variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [initial_point](variables-discrete_design_range-initial_point.html) | Initial values for variables  
Optional | [lower_bounds](variables-discrete_design_range-lower_bounds.html) | Specify minimum values  
Optional | [upper_bounds](variables-discrete_design_range-upper_bounds.html) | Specify maximium values  
Optional | [descriptors](variables-discrete_design_range-descriptors.html) | Labels for the variables  
  
**Description**

These variables take on a range of integer values from the specified lower bound to the specified upper bound (integer interval).


---

#### variables → discrete_design_range → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ ddv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ ddriv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → discrete_design_range → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ ddv_initial_point

  * _Arguments:_ INTEGERLIST

  * _Default:_ 0

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → discrete_design_range → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ ddv_lower_bounds

  * _Arguments:_ INTEGERLIST

  * _Default:_ INT_MIN

**Description**

Specify minimum values


---

#### variables → discrete_design_range → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ ddv_upper_bounds

  * _Arguments:_ INTEGERLIST

  * _Default:_ INT_MAX

**Description**

Specify maximium values


---

### variables → discrete_design_set

# discrete_design_set

Design variable - discrete set-valued

**Topics**

discrete_variables, design_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [integer](variables-discrete_design_set-integer.html) | Integer-valued discrete design variables  
Optional | [string](variables-discrete_design_set-string.html) | String-valued discrete design set variables  
Optional | [real](variables-discrete_design_set-real.html) | Real-valued discrete design variables  
  
**Description**

Discrete design variables whose values come from a set of admissible elements. Each variable specified must be of type integer, string, or real.


---

#### variables → discrete_design_set → integer

# integer

Integer-valued discrete design variables

**Topics**

discrete_variables, design_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete design set integer variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [elements_per_variable](variables-discrete_design_set-integer-elements_per_variable.html) | Number of admissible elements for each set variable  
Required | [elements](variables-discrete_design_set-integer-elements.html) | The permissible values for each discrete variable  
Optional | [categorical](variables-discrete_design_set-integer-categorical.html) | Whether the set-valued variables are categorical or relaxable  
Optional | [initial_point](variables-discrete_design_set-integer-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_design_set-integer-descriptors.html) | Labels for the variables  
  
**Description**

A design variable whose values come from a specified set of admissible integers. See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.

**Examples**

Four integer variables whose values will be selected from the following sets during the search for an optimal design. \\(y1 \in \\{0,1\\}\\) , \\(y2 \in \\{0, 1\\}\\) , \\(y3 \in \\{0,5\\}\\) and \\(y4 \in \\{10, 15, 20, 23\\}\\) .

    discrete_design_set
      integer 4
        descriptors           'y1'  'y2'  'y3'  'y4'
        elements_per_variable 2     2     2     4
        elements              0 1   0 1   0 5   10 15 20 23


---

##### variables → discrete_design_set → integer → categorical

# categorical

Whether the set-valued variables are categorical or relaxable

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [adjacency_matrix](variables-discrete_design_set-integer-categorical-adjacency_matrix.html) | 1-0 matrix defining which categorical variable levels are related.  
  
**Description**

A list of strings of length equal to the number of set (integer, string, or real) variables indicating whether they are strictly categorical, meaning may only take on values from the provided set, or relaxable, meaning may take on any integer or real value between the lowest and highest specified element. Valid categorical strings include ‘yes’, ‘no’, ‘true’, and ‘false’, or any abbreviation in [yYnNtTfF][.]*

**Examples**

Discrete_design_set variable, ‘rotor_blades’, can take on only integer values, 2, 4, or 7 by default. Since categorical is specified to be false, the integrality can be relaxed and ‘rotor_blades’ can take on any value between 2 and 7, e.g., 3, 6, or 5.5.

    discrete_design_set
     integer 1
            elements 2 4 7
     descriptor 'rotor_blades'
     categorical 'no'


---

###### variables → discrete_design_set → integer → categorical → adjacency_matrix

# adjacency_matrix

1-0 matrix defining which categorical variable levels are related.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The `adjacency_matrix` keyword is associated with `discrete_design_set` variables that are specified to be categorical. Each such variable is associated with one \\(k \times k\\) symmetric matrix, where \\(k\\) is the number of values (or levels) of the variable. Entry \\(i,j\\) of a matrix should be 1 if level \\(i\\) and level \\(j\\) are related by some subjective criteria or if \\(i=j\\) ; it should be 0 otherwise. The matrices for all variables of the same type ( `string`, `real`, or `integer`) are entered sequentially as a list of integers as shown in the examples below.

_Default Behavior_

The `adjacency_matrix` keyword is only relevant for `discrete_design_set` `real` and `discrete_design_set` `integer` variables if one or more of them have been specified to be `categorical`. It is always relevant for `discrete_design_set` `string` variables. If the user does not define an adjacency matrix, the default is method dependent. Currently, the only method that makes use of the adjacency matrix is `[mesh_adaptive_search](../../usingdakota/reference/method-mesh_adaptive_search.html)`, which uses a tri-diagonal adjacency matrix by default.

_Expected Output_

The expected output is method dependent.

_Usage Tips_

If an adjacency matrix is defined for one type of (categorical) `discrete_design_set` variable, if must be defined for all variables of that type, even for those not defined to be categorical. Those for the non-categorical set variables will be ignored.

**Examples**

The following example shows a variables specification where some real and some integer `discrete_design_set` variables are categorical.

    variables
      continuous_design = 3
        initial_point  -1.0    1.5   2.0
        lower_bounds  -10.0  -10.0 -10.0
        upper_bounds   10.0   10.0  10.0
        descriptors    'x1'   'x2'  'x3'
      discrete_design_range = 2
        initial_point  2     2
        lower_bounds   1     1
        upper_bounds   4     9
        descriptors   'y1'  'y2'
      discrete_design_set
        real = 2
          elements_per_variable = 4 5
          elements  =  1.2 2.3 3.4 4.5 1.2 3.3 4.4 5.5 7.7
          descriptors  'y3'            'y4'
          categorical  'no'            'yes'
          adjacency_matrix 1 1 0 0  # Begin entry of 4x4 matrix for y3
                           1 1 1 0
                           0 1 1 1
                           0 0 1 1
                           1 0 1 0 1 # Begin entry of 5x5 matrix for y4
                           0 1 0 1 0
                           1 0 1 0 1
                           0 1 0 1 0
                           1 0 1 0 1
        integer = 2
          elements_per_variable = 2 3
          elements  =  4  7  8  9  12
          descriptors  'z1'  'z2'
          categorical  'yes' 'yes'

Note that for the real case, the user wants to define an adjacency matrix for the categorical variable, so adjacency matrices for both variables must be specified. The matrix for the first one will be ignored. Note that no adjacency matrix is specified for either integer categorical variable. The default will be used in both cases. Currently the only method taking advantage of adjacency matrices is `mesh_adaptive_search`, which uses a tri-diagonal adjacency matrix by default. Thus, the matrices used would be

    z1: 1 1
        1 1
    z2: 1 1 0
        1 1 1
        0 1 1

The following example shows a variables specification for string variables. Note that string variables are always considered to be categorical. If an adjacency matrix is not specified, a method-dependent default matrix will be used.

    variables,
      continuous_design = 2
        initial_point  0.5  0.5
        lower_bounds   0.   0.
        upper_bounds   1.   1.
        descriptors =  'x'  'y'
      discrete_design_set string = 1
        elements = 'aniso1' 'aniso2' 'iso1' 'iso2' 'iso3'
        descriptors = 'ancomp'
        adjacency_matrix 1 1 0 0 0
                         1 1 0 0 0
                         0 0 1 1 1
                         0 0 1 1 1
                         0 0 1 1 1


---

##### variables → discrete_design_set → integer → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ ddsiv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → discrete_design_set → integer → elements

# elements

The permissible values for each discrete variable

**Specification**

  * _Alias:_ set_values

  * _Arguments:_ INTEGERLIST

**Description**

Specify the permissible values for discrete set variables (of type integer, string, or real). See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.


---

##### variables → discrete_design_set → integer → elements_per_variable

# elements_per_variable

Number of admissible elements for each set variable

**Specification**

  * _Alias:_ num_set_values

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

Discrete set variables (including design, uncertain, and state) take on only a fixed set of values. For each type (integer, string, or real), this keyword specifies how many admissible values are provided for each variable. If not specified, equal apportionment of elements among variables is assumed, and the number of elements must be evenly divisible by the number of variables.


---

##### variables → discrete_design_set → integer → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

  * _Default:_ middle set value, or rounded down

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → discrete_design_set → real

# real

Real-valued discrete design variables

**Topics**

discrete_variables, design_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete design set real variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [elements_per_variable](variables-discrete_design_set-real-elements_per_variable.html) | Number of admissible elements for each set variable  
Required | [elements](variables-discrete_design_set-real-elements.html) | The permissible values for each discrete variable  
Optional | [categorical](variables-discrete_design_set-real-categorical.html) | Whether the set-valued variables are categorical or relaxable  
Optional | [initial_point](variables-discrete_design_set-real-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_design_set-real-descriptors.html) | Labels for the variables  
  
**Description**

A design variable whose values come from a specified set of admissible reals. See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.

**Examples**

Two continuous, restricted variables whose values will be selected from the following sets during the search for an optimal design. \\(y1 \in \\{0.25,1.25, 2.25, 3.25, 4.25\\}\\) , \\(y2 \in \\{0,5\\}\\)

    discrete_design_set
      real 2
        descriptors           'y1'                       'y2'
        elements_per_variable 5                          2
        elemetns              0.25 1.25 2.25 3.25 4.25   0 5


---

##### variables → discrete_design_set → real → categorical

# categorical

Whether the set-valued variables are categorical or relaxable

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [adjacency_matrix](variables-discrete_design_set-real-categorical-adjacency_matrix.html) | 1-0 matrix defining which categorical variable levels are related.  
  
**Description**

A list of strings of length equal to the number of set (integer, string, or real) variables indicating whether they are strictly categorical, meaning may only take on values from the provided set, or relaxable, meaning may take on any integer or real value between the lowest and highest specified element. Valid categorical strings include ‘yes’, ‘no’, ‘true’, and ‘false’, or any abbreviation in [yYnNtTfF][.]*

**Examples**

Discrete_design_set variable, ‘rotor_blades’, can take on only integer values, 2, 4, or 7 by default. Since categorical is specified to be false, the integrality can be relaxed and ‘rotor_blades’ can take on any value between 2 and 7, e.g., 3, 6, or 5.5.

    discrete_design_set
     integer 1
            elements 2 4 7
     descriptor 'rotor_blades'
     categorical 'no'


---

###### variables → discrete_design_set → real → categorical → adjacency_matrix

# adjacency_matrix

1-0 matrix defining which categorical variable levels are related.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The `adjacency_matrix` keyword is associated with `discrete_design_set` variables that are specified to be categorical. Each such variable is associated with one \\(k \times k\\) symmetric matrix, where \\(k\\) is the number of values (or levels) of the variable. Entry \\(i,j\\) of a matrix should be 1 if level \\(i\\) and level \\(j\\) are related by some subjective criteria or if \\(i=j\\) ; it should be 0 otherwise. The matrices for all variables of the same type ( `string`, `real`, or `integer`) are entered sequentially as a list of integers as shown in the examples below.

_Default Behavior_

The `adjacency_matrix` keyword is only relevant for `discrete_design_set` `real` and `discrete_design_set` `integer` variables if one or more of them have been specified to be `categorical`. It is always relevant for `discrete_design_set` `string` variables. If the user does not define an adjacency matrix, the default is method dependent. Currently, the only method that makes use of the adjacency matrix is `[mesh_adaptive_search](../../usingdakota/reference/method-mesh_adaptive_search.html)`, which uses a tri-diagonal adjacency matrix by default.

_Expected Output_

The expected output is method dependent.

_Usage Tips_

If an adjacency matrix is defined for one type of (categorical) `discrete_design_set` variable, if must be defined for all variables of that type, even for those not defined to be categorical. Those for the non-categorical set variables will be ignored.

**Examples**

The following example shows a variables specification where some real and some integer `discrete_design_set` variables are categorical.

    variables
      continuous_design = 3
        initial_point  -1.0    1.5   2.0
        lower_bounds  -10.0  -10.0 -10.0
        upper_bounds   10.0   10.0  10.0
        descriptors    'x1'   'x2'  'x3'
      discrete_design_range = 2
        initial_point  2     2
        lower_bounds   1     1
        upper_bounds   4     9
        descriptors   'y1'  'y2'
      discrete_design_set
        real = 2
          elements_per_variable = 4 5
          elements  =  1.2 2.3 3.4 4.5 1.2 3.3 4.4 5.5 7.7
          descriptors  'y3'            'y4'
          categorical  'no'            'yes'
          adjacency_matrix 1 1 0 0  # Begin entry of 4x4 matrix for y3
                           1 1 1 0
                           0 1 1 1
                           0 0 1 1
                           1 0 1 0 1 # Begin entry of 5x5 matrix for y4
                           0 1 0 1 0
                           1 0 1 0 1
                           0 1 0 1 0
                           1 0 1 0 1
        integer = 2
          elements_per_variable = 2 3
          elements  =  4  7  8  9  12
          descriptors  'z1'  'z2'
          categorical  'yes' 'yes'

Note that for the real case, the user wants to define an adjacency matrix for the categorical variable, so adjacency matrices for both variables must be specified. The matrix for the first one will be ignored. Note that no adjacency matrix is specified for either integer categorical variable. The default will be used in both cases. Currently the only method taking advantage of adjacency matrices is `mesh_adaptive_search`, which uses a tri-diagonal adjacency matrix by default. Thus, the matrices used would be

    z1: 1 1
        1 1
    z2: 1 1 0
        1 1 1
        0 1 1

The following example shows a variables specification for string variables. Note that string variables are always considered to be categorical. If an adjacency matrix is not specified, a method-dependent default matrix will be used.

    variables,
      continuous_design = 2
        initial_point  0.5  0.5
        lower_bounds   0.   0.
        upper_bounds   1.   1.
        descriptors =  'x'  'y'
      discrete_design_set string = 1
        elements = 'aniso1' 'aniso2' 'iso1' 'iso2' 'iso3'
        descriptors = 'ancomp'
        adjacency_matrix 1 1 0 0 0
                         1 1 0 0 0
                         0 0 1 1 1
                         0 0 1 1 1
                         0 0 1 1 1


---

##### variables → discrete_design_set → real → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ ddsrv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → discrete_design_set → real → elements

# elements

The permissible values for each discrete variable

**Specification**

  * _Alias:_ set_values

  * _Arguments:_ REALLIST

**Description**

Specify the permissible values for discrete set variables (of type integer, string, or real). See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.


---

##### variables → discrete_design_set → real → elements_per_variable

# elements_per_variable

Number of admissible elements for each set variable

**Specification**

  * _Alias:_ num_set_values

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

Discrete set variables (including design, uncertain, and state) take on only a fixed set of values. For each type (integer, string, or real), this keyword specifies how many admissible values are provided for each variable. If not specified, equal apportionment of elements among variables is assumed, and the number of elements must be evenly divisible by the number of variables.


---

##### variables → discrete_design_set → real → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ middle set value, or rounded down

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → discrete_design_set → string

# string

String-valued discrete design set variables

**Topics**

discrete_variables, design_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete design set string variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [elements_per_variable](variables-discrete_design_set-string-elements_per_variable.html) | Number of admissible elements for each set variable  
Required | [elements](variables-discrete_design_set-string-elements.html) | The permissible values for each discrete variable  
Optional | [adjacency_matrix](variables-discrete_design_set-string-adjacency_matrix.html) | 1-0 matrix defining which categorical variable levels are related.  
Optional | [initial_point](variables-discrete_design_set-string-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_design_set-string-descriptors.html) | Labels for the variables  
  
**Description**

Discrete design variables whose values come from a specified set of admissible strings.

See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables. Each string element value must be quoted and may contain alphanumeric, dash, underscore, and colon. White space, quote characters, and backslash/metacharacters are not permitted.

**Examples**

Two string variables whose values will be selected from the set of provided elements. The first variable, ‘linear solver’, takes on values from a set of three possible elements and the second variable, ‘mesh_file’, from a set of two possible elements.

    discrete_design_set
      string 2
        descriptors           'linear_solver'  'mesh_file'
        elements_per_variable 3                2
        elements              'cg' 'gmres' 'direct'
                              'mesh64.exo' 'mesh128.exo'


---

##### variables → discrete_design_set → string → adjacency_matrix

# adjacency_matrix

1-0 matrix defining which categorical variable levels are related.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The `adjacency_matrix` keyword is associated with `discrete_design_set` variables that are specified to be categorical. Each such variable is associated with one \\(k \times k\\) symmetric matrix, where \\(k\\) is the number of values (or levels) of the variable. Entry \\(i,j\\) of a matrix should be 1 if level \\(i\\) and level \\(j\\) are related by some subjective criteria or if \\(i=j\\) ; it should be 0 otherwise. The matrices for all variables of the same type ( `string`, `real`, or `integer`) are entered sequentially as a list of integers as shown in the examples below.

_Default Behavior_

The `adjacency_matrix` keyword is only relevant for `discrete_design_set` `real` and `discrete_design_set` `integer` variables if one or more of them have been specified to be `categorical`. It is always relevant for `discrete_design_set` `string` variables. If the user does not define an adjacency matrix, the default is method dependent. Currently, the only method that makes use of the adjacency matrix is `[mesh_adaptive_search](../../usingdakota/reference/method-mesh_adaptive_search.html)`, which uses a tri-diagonal adjacency matrix by default.

_Expected Output_

The expected output is method dependent.

_Usage Tips_

If an adjacency matrix is defined for one type of (categorical) `discrete_design_set` variable, if must be defined for all variables of that type, even for those not defined to be categorical. Those for the non-categorical set variables will be ignored.

**Examples**

The following example shows a variables specification where some real and some integer `discrete_design_set` variables are categorical.

    variables
      continuous_design = 3
        initial_point  -1.0    1.5   2.0
        lower_bounds  -10.0  -10.0 -10.0
        upper_bounds   10.0   10.0  10.0
        descriptors    'x1'   'x2'  'x3'
      discrete_design_range = 2
        initial_point  2     2
        lower_bounds   1     1
        upper_bounds   4     9
        descriptors   'y1'  'y2'
      discrete_design_set
        real = 2
          elements_per_variable = 4 5
          elements  =  1.2 2.3 3.4 4.5 1.2 3.3 4.4 5.5 7.7
          descriptors  'y3'            'y4'
          categorical  'no'            'yes'
          adjacency_matrix 1 1 0 0  # Begin entry of 4x4 matrix for y3
                           1 1 1 0
                           0 1 1 1
                           0 0 1 1
                           1 0 1 0 1 # Begin entry of 5x5 matrix for y4
                           0 1 0 1 0
                           1 0 1 0 1
                           0 1 0 1 0
                           1 0 1 0 1
        integer = 2
          elements_per_variable = 2 3
          elements  =  4  7  8  9  12
          descriptors  'z1'  'z2'
          categorical  'yes' 'yes'

Note that for the real case, the user wants to define an adjacency matrix for the categorical variable, so adjacency matrices for both variables must be specified. The matrix for the first one will be ignored. Note that no adjacency matrix is specified for either integer categorical variable. The default will be used in both cases. Currently the only method taking advantage of adjacency matrices is `mesh_adaptive_search`, which uses a tri-diagonal adjacency matrix by default. Thus, the matrices used would be

    z1: 1 1
        1 1
    z2: 1 1 0
        1 1 1
        0 1 1

The following example shows a variables specification for string variables. Note that string variables are always considered to be categorical. If an adjacency matrix is not specified, a method-dependent default matrix will be used.

    variables,
      continuous_design = 2
        initial_point  0.5  0.5
        lower_bounds   0.   0.
        upper_bounds   1.   1.
        descriptors =  'x'  'y'
      discrete_design_set string = 1
        elements = 'aniso1' 'aniso2' 'iso1' 'iso2' 'iso3'
        descriptors = 'ancomp'
        adjacency_matrix 1 1 0 0 0
                         1 1 0 0 0
                         0 0 1 1 1
                         0 0 1 1 1
                         0 0 1 1 1


---

##### variables → discrete_design_set → string → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ ddssv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → discrete_design_set → string → elements

# elements

The permissible values for each discrete variable

**Specification**

  * _Alias:_ set_values

  * _Arguments:_ STRINGLIST

**Description**

Specify the permissible values for discrete set variables (of type integer, string, or real). See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.


---

##### variables → discrete_design_set → string → elements_per_variable

# elements_per_variable

Number of admissible elements for each set variable

**Specification**

  * _Alias:_ num_set_values

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

Discrete set variables (including design, uncertain, and state) take on only a fixed set of values. For each type (integer, string, or real), this keyword specifies how many admissible values are provided for each variable. If not specified, equal apportionment of elements among variables is assumed, and the number of elements must be evenly divisible by the number of variables.


---

##### variables → discrete_design_set → string → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ middle set value, or rounded down

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

### variables → discrete_interval_uncertain

# discrete_interval_uncertain

Epistemic uncertain variable - values from one or more discrete intervals

**Topics**

discrete_variables, epistemic_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ No discrete interval uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [num_intervals](variables-discrete_interval_uncertain-num_intervals.html) | Specify the number of intervals for each variable  
Optional | [interval_probabilities](variables-discrete_interval_uncertain-interval_probabilities.html) | Assign probability mass to each interval  
Required | [lower_bounds](variables-discrete_interval_uncertain-lower_bounds.html) | Specify minimum values  
Required | [upper_bounds](variables-discrete_interval_uncertain-upper_bounds.html) | Specify maximium values  
Optional | [initial_point](variables-discrete_interval_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_interval_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

Discrete interval uncertain variables are epistemic types. They can specify a single interval per variable which may be used in interval analysis, where the goal is to determine the interval bounds on the output corresponding to the interval bounds on the input. Permissible values are any integer within the bound, e.g., [1, 4], allowing discrete values of 1, 2, 3, or 4)

Discrete variables may be used to represent things like epistemic model form uncertainty. For example, if one wants to analyze the effect of model 1 vs. model 2 vs. model 3 in an epistemic analysis (either an interval analysis or a Dempster-Shafer evidence theory analysis), one can use a discrete epistemic variable to represent the uncertainty in the model form.

More detailed continuous interval representations can specify a set of belief structures based on intervals that may be contiguous, overlapping, or disjoint. This is used in specifying the inputs necessary for an epistemic uncertainty analysis using Dempster-Shafer theory of evidence.

Other epistemic types include:

  * `[continuous_interval_uncertain](../../usingdakota/reference/variables-continuous_interval_uncertain.html)`

  * `[integer](../../usingdakota/reference/variables-discrete_uncertain_set-integer.html)`

  * `[string](../../usingdakota/reference/variables-discrete_uncertain_set-string.html)`

  * `[real](../../usingdakota/reference/variables-discrete_uncertain_set-real.html)`

**Examples**

Let d1 be 2, 3 or 4 with probability 0.2, 4 or 5 with probability 0.5 and 6 with probability 0.3. Let d2 be 4, 5 or 6 with probability 0.4 and 6, 7 or 8 with probability 0.6. The following specification is for a Dempster-Shafer analysis:

    discrete_interval_uncertain = 2
     num_intervals = 3 2
     interval_probs = 0.2 0.5 0.3 0.4 0.6
     lower_bounds = 2 4 6 4 6
     upper_bounds = 4 5 6 6 8

**Theory**

**Dempster-Shafer Theory of Evidence**

  * Multiple intervals can be assigned to each `discrete_interval_uncertain` variable

  * A Basic Probability Assignment (BPA) is associated with each interval. The BPA represents a probability that the value of the uncertain variable is located within that interval.

  * Each interval is defined by lower and upper bounds

  * Outputs are called “belief” and “plausibility.”Belief represents the smallest possible probability that is consistent with the evidence, while plausibility represents the largest possible probability that is consistent with the evidence. Evidence is the intervals together with their BPA.


---

#### variables → discrete_interval_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ diuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → discrete_interval_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → discrete_interval_uncertain → interval_probabilities

# interval_probabilities

Assign probability mass to each interval

**Specification**

  * _Alias:_ interval_probs range_probabilities range_probs

  * _Arguments:_ REALLIST

  * _Default:_ Equal probability assignments for each interval (1/num_intervals[i])

**Description**

The basic probability assignments for each interval variable must sum to one. For example, if an interval variable is defined with three intervals, the probabilities for these intervals could be 0.2, 0.5, and 0.3 which sum to one, but could not be 0.5,0.5, and 0.5 which do not sum to one.


---

#### variables → discrete_interval_uncertain → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specify minimum values


---

#### variables → discrete_interval_uncertain → num_intervals

# num_intervals

Specify the number of intervals for each variable

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

  * _Default:_ Equal apportionment of intervals among variables

**Description**

In Dakota, epistemic uncertainty analysis is performed using either interval estimation or Dempster-Shafer theory of evidence. In these approaches, one does not assign a probability distribution to each uncertain input variable. Rather, one divides each uncertain input variable into one or more intervals. The input parameters are only known to occur within intervals; nothing more is assumed. `num_intervals` specifies the number of such intervals associated with each interval uncertain parameter.


---

#### variables → discrete_interval_uncertain → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specify maximium values


---

### variables → discrete_state_range

# discrete_state_range

State variables - discrete range-valued

**Topics**

discrete_variables, state_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ No discrete state variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [initial_state](variables-discrete_state_range-initial_state.html) | Initial values for variables  
Optional | [lower_bounds](variables-discrete_state_range-lower_bounds.html) | Specify minimum values  
Optional | [upper_bounds](variables-discrete_state_range-upper_bounds.html) | Specify maximium values  
Optional | [descriptors](variables-discrete_state_range-descriptors.html) | Labels for the variables  
  
**Description**

Discrete state variables defined by bounds (an integer interval).

See [State Variables](../inputfile/variables.html#variables-state) for details on the behavior of state variables.


---

#### variables → discrete_state_range → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ dsv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ dsriv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → discrete_state_range → initial_state

# initial_state

Initial values for variables

**Specification**

  * _Alias:_ dsv_initial_state

  * _Arguments:_ INTEGERLIST

  * _Default:_ 0

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → discrete_state_range → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ dsv_lower_bounds

  * _Arguments:_ INTEGERLIST

  * _Default:_ INT_MIN

**Description**

Specify minimum values


---

#### variables → discrete_state_range → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ dsv_upper_bounds

  * _Arguments:_ INTEGERLIST

  * _Default:_ INT_MAX

**Description**

Specify maximium values


---

### variables → discrete_state_set

# discrete_state_set

State variable - discrete set-valued

**Topics**

discrete_variables, state_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [integer](variables-discrete_state_set-integer.html) | Discrete state variables, each defined by a set of permissible integers  
Optional | [string](variables-discrete_state_set-string.html) | String-valued discrete state set variables  
Optional | [real](variables-discrete_state_set-real.html) | Discrete state variables, each defined by a set of permissible real numbers  
  
**Description**

Discrete state variables whose values come from a set of admissible elements. Each variable specified must be of type integer, string, or real.


---

#### variables → discrete_state_set → integer

# integer

Discrete state variables, each defined by a set of permissible integers

**Topics**

discrete_variables, state_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete state set integer variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [elements_per_variable](variables-discrete_state_set-integer-elements_per_variable.html) | Number of admissible elements for each set variable  
Required | [elements](variables-discrete_state_set-integer-elements.html) | The permissible values for each discrete variable  
Optional | [categorical](variables-discrete_state_set-integer-categorical.html) | Whether the set-valued variables are categorical or relaxable  
Optional | [initial_state](variables-discrete_state_set-integer-initial_state.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_state_set-integer-descriptors.html) | Labels for the variables  
  
**Description**

Discrete state variables defined by a set of permissible integers.

See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables and [State Variables](../inputfile/variables.html#variables-state) for details on the behavior of state variables.


---

##### variables → discrete_state_set → integer → categorical

# categorical

Whether the set-valued variables are categorical or relaxable

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Description**

A list of strings of length equal to the number of set (integer, string, or real) variables indicating whether they are strictly categorical, meaning may only take on values from the provided set, or relaxable, meaning may take on any integer or real value between the lowest and highest specified element. Valid categorical strings include ‘yes’, ‘no’, ‘true’, and ‘false’, or any abbreviation in [yYnNtTfF][.]*

**Examples**

Discrete_design_set variable, ‘rotor_blades’, can take on only integer values, 2, 4, or 7 by default. Since categorical is specified to be false, the integrality can be relaxed and ‘rotor_blades’ can take on any value between 2 and 7, e.g., 3, 6, or 5.5.

    discrete_design_set
     integer 1
            elements 2 4 7
     descriptor 'rotor_blades'
     categorical 'no'


---

##### variables → discrete_state_set → integer → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ dssiv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → discrete_state_set → integer → elements

# elements

The permissible values for each discrete variable

**Specification**

  * _Alias:_ set_values

  * _Arguments:_ INTEGERLIST

**Description**

Specify the permissible values for discrete set variables (of type integer, string, or real). See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.


---

##### variables → discrete_state_set → integer → elements_per_variable

# elements_per_variable

Number of admissible elements for each set variable

**Specification**

  * _Alias:_ num_set_values

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

Discrete set variables (including design, uncertain, and state) take on only a fixed set of values. For each type (integer, string, or real), this keyword specifies how many admissible values are provided for each variable. If not specified, equal apportionment of elements among variables is assumed, and the number of elements must be evenly divisible by the number of variables.


---

##### variables → discrete_state_set → integer → initial_state

# initial_state

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

  * _Default:_ middle set value, or rounded down

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → discrete_state_set → real

# real

Discrete state variables, each defined by a set of permissible real numbers

**Topics**

discrete_variables, state_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete state set real variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [elements_per_variable](variables-discrete_state_set-real-elements_per_variable.html) | Number of admissible elements for each set variable  
Required | [elements](variables-discrete_state_set-real-elements.html) | The permissible values for each discrete variable  
Optional | [categorical](variables-discrete_state_set-real-categorical.html) | Whether the set-valued variables are categorical or relaxable  
Optional | [initial_state](variables-discrete_state_set-real-initial_state.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_state_set-real-descriptors.html) | Labels for the variables  
  
**Description**

Discrete state variables defined by a set of permissible real numbers.

See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables and [State Variables](../inputfile/variables.html#variables-state) for details on the behavior of state variables.


---

##### variables → discrete_state_set → real → categorical

# categorical

Whether the set-valued variables are categorical or relaxable

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Description**

A list of strings of length equal to the number of set (integer, string, or real) variables indicating whether they are strictly categorical, meaning may only take on values from the provided set, or relaxable, meaning may take on any integer or real value between the lowest and highest specified element. Valid categorical strings include ‘yes’, ‘no’, ‘true’, and ‘false’, or any abbreviation in [yYnNtTfF][.]*

**Examples**

Discrete_design_set variable, ‘rotor_blades’, can take on only integer values, 2, 4, or 7 by default. Since categorical is specified to be false, the integrality can be relaxed and ‘rotor_blades’ can take on any value between 2 and 7, e.g., 3, 6, or 5.5.

    discrete_design_set
     integer 1
            elements 2 4 7
     descriptor 'rotor_blades'
     categorical 'no'


---

##### variables → discrete_state_set → real → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ dssrv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → discrete_state_set → real → elements

# elements

The permissible values for each discrete variable

**Specification**

  * _Alias:_ set_values

  * _Arguments:_ REALLIST

**Description**

Specify the permissible values for discrete set variables (of type integer, string, or real). See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.


---

##### variables → discrete_state_set → real → elements_per_variable

# elements_per_variable

Number of admissible elements for each set variable

**Specification**

  * _Alias:_ num_set_values

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

Discrete set variables (including design, uncertain, and state) take on only a fixed set of values. For each type (integer, string, or real), this keyword specifies how many admissible values are provided for each variable. If not specified, equal apportionment of elements among variables is assumed, and the number of elements must be evenly divisible by the number of variables.


---

##### variables → discrete_state_set → real → initial_state

# initial_state

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ middle set value, or rounded down

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → discrete_state_set → string

# string

String-valued discrete state set variables

**Topics**

discrete_variables, state_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete state set string variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [elements_per_variable](variables-discrete_state_set-string-elements_per_variable.html) | Number of admissible elements for each set variable  
Required | [elements](variables-discrete_state_set-string-elements.html) | The permissible values for each discrete variable  
Optional | [initial_state](variables-discrete_state_set-string-initial_state.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_state_set-string-descriptors.html) | Labels for the variables  
  
**Description**

Discrete state variables whose values come from a specified set of admissible strings.

See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables and [State Variables](../inputfile/variables.html#variables-state) for details on the behavior of state variables.

Each string element value must be quoted and may contain alphanumeric, dash, underscore, and colon. White space, quote characters, and backslash/metacharacters are not permitted.


---

##### variables → discrete_state_set → string → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ dsssv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → discrete_state_set → string → elements

# elements

The permissible values for each discrete variable

**Specification**

  * _Alias:_ set_values

  * _Arguments:_ STRINGLIST

**Description**

Specify the permissible values for discrete set variables (of type integer, string, or real). See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.


---

##### variables → discrete_state_set → string → elements_per_variable

# elements_per_variable

Number of admissible elements for each set variable

**Specification**

  * _Alias:_ num_set_values

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

Discrete set variables (including design, uncertain, and state) take on only a fixed set of values. For each type (integer, string, or real), this keyword specifies how many admissible values are provided for each variable. If not specified, equal apportionment of elements among variables is assumed, and the number of elements must be evenly divisible by the number of variables.


---

##### variables → discrete_state_set → string → initial_state

# initial_state

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ middle set value, or rounded down

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

### variables → discrete_uncertain_set

# discrete_uncertain_set

Epistemic uncertain variable - discrete set-valued

**Topics**

discrete_variables, epistemic_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [integer](variables-discrete_uncertain_set-integer.html) | Discrete, epistemic uncertain variable - integers within a set  
Optional | [string](variables-discrete_uncertain_set-string.html) | Discrete, epistemic uncertain variable - strings within a set  
Optional | [real](variables-discrete_uncertain_set-real.html) | Discrete, epistemic uncertain variable - real numbers within a set  
  
**Description**

Discrete uncertain variables whose values come from a set of admissible elements. Each variable specified must be of type integer, string, or real.


---

#### variables → discrete_uncertain_set → integer

# integer

Discrete, epistemic uncertain variable - integers within a set

**Topics**

discrete_variables, epistemic_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete uncertain set integer variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [elements_per_variable](variables-discrete_uncertain_set-integer-elements_per_variable.html) | Number of admissible elements for each set variable  
Required | [elements](variables-discrete_uncertain_set-integer-elements.html) | The permissible values for each discrete variable  
Optional | [set_probabilities](variables-discrete_uncertain_set-integer-set_probabilities.html) | This keyword defines the probabilities for the various elements of discrete sets.  
Optional | [categorical](variables-discrete_uncertain_set-integer-categorical.html) | Whether the set-valued variables are categorical or relaxable  
Optional | [initial_point](variables-discrete_uncertain_set-integer-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_uncertain_set-integer-descriptors.html) | Labels for the variables  
  
**Description**

Discrete set variables may be used to specify categorical choices which are epistemic. For example, if we have three possible forms for a physics model (model 1, 2, or 3) and there is epistemic uncertainty about which one is correct, a discrete uncertain set may be used to represent this type of uncertainty.

This variable is defined by a set of integers, in which the discrete value may take any value within the integer set (for example, the set may be defined as 1, 2, and 4)

Other epistemic types include:

  * `[continuous_interval_uncertain](../../usingdakota/reference/variables-continuous_interval_uncertain.html)`

  * `[discrete_interval_uncertain](../../usingdakota/reference/variables-discrete_interval_uncertain.html)`

  * discrete_uncertain_set `[string](../../usingdakota/reference/variables-discrete_uncertain_set-string.html)`

  * discrete_uncertain_set `[real](../../usingdakota/reference/variables-discrete_uncertain_set-real.html)`

**Examples**

Let di1 be 2 or 13 and di2 be 4, 5 or 26. The following specification is for an interval analysis:

    discrete_uncertain_set
     integer
     num_set_values 2     3
     set_values     2 13  4 5 26
     descriptors    'di1' 'di2'

**Theory**

The `discrete_uncertain_set-integer` variable is NOT a discrete random variable. It can be contrasted to a the histogram-defined random variables: `[histogram_bin_uncertain](../../usingdakota/reference/variables-histogram_bin_uncertain.html)` and `[histogram_point_uncertain](../../usingdakota/reference/variables-histogram_point_uncertain.html)`. It is used in epistemic uncertainty analysis, where one is trying to model uncertainty due to lack of knowledge.

The discrete uncertain set integer variable is used in both interval analysis and in Dempster-Shafer theory of evidence.

_interval analysis_

  * the values are integers, equally weighted

  * the true value of the random variable is one of the integers in this set

  * output is the minimum and maximum function value conditionalon the specified inputs

_Dempster-Shafer theory of evidence_

  * the values are integers, but they can be assigned different weights

  * outputs are called “belief” and “plausibility.”Belief represents the smallest possible probability that is consistent with the evidence, while plausibility represents the largest possible probability that is consistent with the evidence. Evidence is the values together with their weights.


---

##### variables → discrete_uncertain_set → integer → categorical

# categorical

Whether the set-valued variables are categorical or relaxable

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Description**

A list of strings of length equal to the number of set (integer, string, or real) variables indicating whether they are strictly categorical, meaning may only take on values from the provided set, or relaxable, meaning may take on any integer or real value between the lowest and highest specified element. Valid categorical strings include ‘yes’, ‘no’, ‘true’, and ‘false’, or any abbreviation in [yYnNtTfF][.]*

**Examples**

Discrete_design_set variable, ‘rotor_blades’, can take on only integer values, 2, 4, or 7 by default. Since categorical is specified to be false, the integrality can be relaxed and ‘rotor_blades’ can take on any value between 2 and 7, e.g., 3, 6, or 5.5.

    discrete_design_set
     integer 1
            elements 2 4 7
     descriptor 'rotor_blades'
     categorical 'no'


---

##### variables → discrete_uncertain_set → integer → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ dusiv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → discrete_uncertain_set → integer → elements

# elements

The permissible values for each discrete variable

**Specification**

  * _Alias:_ set_values

  * _Arguments:_ INTEGERLIST

**Description**

Specify the permissible values for discrete set variables (of type integer, string, or real). See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.


---

##### variables → discrete_uncertain_set → integer → elements_per_variable

# elements_per_variable

Number of admissible elements for each set variable

**Specification**

  * _Alias:_ num_set_values

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

Discrete set variables (including design, uncertain, and state) take on only a fixed set of values. For each type (integer, string, or real), this keyword specifies how many admissible values are provided for each variable. If not specified, equal apportionment of elements among variables is assumed, and the number of elements must be evenly divisible by the number of variables.


---

##### variables → discrete_uncertain_set → integer → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

##### variables → discrete_uncertain_set → integer → set_probabilities

# set_probabilities

This keyword defines the probabilities for the various elements of discrete sets.

**Specification**

  * _Alias:_ set_probs

  * _Arguments:_ REALLIST

  * _Default:_ Equal probability assignments for each set member (1/num_set_values[i])

**Description**

There are three types of `discrete_uncertain_set` variables: integer, string, or real sets. With each of these types, one defines the number of elements of the set per that variable, the values of those elements, and the associated probabilities. For example, if one has an integer discrete uncertain set variable with 3 elements {3,4,8}, then one could define the probabilities associated with those set elements as (for example) 0.2, 0.5, and 0.3. The `set_probabilities` for a particular variable should sum to one over all the elements in that set.


---

#### variables → discrete_uncertain_set → real

# real

Discrete, epistemic uncertain variable - real numbers within a set

**Topics**

discrete_variables, epistemic_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete uncertain set real variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [elements_per_variable](variables-discrete_uncertain_set-real-elements_per_variable.html) | Number of admissible elements for each set variable  
Required | [elements](variables-discrete_uncertain_set-real-elements.html) | The permissible values for each discrete variable  
Optional | [set_probabilities](variables-discrete_uncertain_set-real-set_probabilities.html) | This keyword defines the probabilities for the various elements of discrete sets.  
Optional | [categorical](variables-discrete_uncertain_set-real-categorical.html) | Whether the set-valued variables are categorical or relaxable  
Optional | [initial_point](variables-discrete_uncertain_set-real-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_uncertain_set-real-descriptors.html) | Labels for the variables  
  
**Description**

Discrete set variables may be used to specify categorical choices which are epistemic. For example, if we have three possible forms for a physics model (model 1, 2, or 3) and there is epistemic uncertainty about which one is correct, a discrete uncertain set may be used to represent this type of uncertainty.

This variable is defined by a set of reals, in which the discrete variable may take any value defined within the real set (for example, a parameter may have two allowable real values, 3.285 or 4.79).

Other epistemic types include:

  * `[continuous_interval_uncertain](../../usingdakota/reference/variables-continuous_interval_uncertain.html)`

  * `[discrete_interval_uncertain](../../usingdakota/reference/variables-discrete_interval_uncertain.html)`

  * discrete_uncertain_set `[integer](../../usingdakota/reference/variables-discrete_uncertain_set-integer.html)`

  * discrete_uncertain_set `[string](../../usingdakota/reference/variables-discrete_uncertain_set-string.html)`

**Examples**

Let dr1 be 2.1 or 1.3 and dr2 be 0.4, 5 or 2.6. The following specification is for an interval analysis:

    discrete_uncertain_set
     integer
     num_set_values  2           3
     set_values      2.1  1.3    0.4  5  2.6
     descriptors     'dr1'       'dr2'

**Theory**

The `discrete_uncertain_set-integer` variable is NOT a discrete random variable. It can be contrasted to a the histogram-defined random variables: `[histogram_bin_uncertain](../../usingdakota/reference/variables-histogram_bin_uncertain.html)` and `[histogram_point_uncertain](../../usingdakota/reference/variables-histogram_point_uncertain.html)`. It is used in epistemic uncertainty analysis, where one is trying to model uncertainty due to lack of knowledge.

The discrete uncertain set integer variable is used in both interval analysis and in Dempster-Shafer theory of evidence.

_interval analysis_

  * the values are integers, equally weighted

  * the true value of the random variable is one of the integers in this set

  * output is the minimum and maximum function value conditionalon the specified inputs

_Dempster-Shafer theory of evidence_

  * the values are integers, but they can be assigned different weights

  * outputs are called “belief” and “plausibility.”Belief represents the smallest possible probability that is consistent with the evidence, while plausibility represents the largest possible probability that is consistent with the evidence. Evidence is the values together with their weights.


---

##### variables → discrete_uncertain_set → real → categorical

# categorical

Whether the set-valued variables are categorical or relaxable

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Description**

A list of strings of length equal to the number of set (integer, string, or real) variables indicating whether they are strictly categorical, meaning may only take on values from the provided set, or relaxable, meaning may take on any integer or real value between the lowest and highest specified element. Valid categorical strings include ‘yes’, ‘no’, ‘true’, and ‘false’, or any abbreviation in [yYnNtTfF][.]*

**Examples**

Discrete_design_set variable, ‘rotor_blades’, can take on only integer values, 2, 4, or 7 by default. Since categorical is specified to be false, the integrality can be relaxed and ‘rotor_blades’ can take on any value between 2 and 7, e.g., 3, 6, or 5.5.

    discrete_design_set
     integer 1
            elements 2 4 7
     descriptor 'rotor_blades'
     categorical 'no'


---

##### variables → discrete_uncertain_set → real → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ dusrv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → discrete_uncertain_set → real → elements

# elements

The permissible values for each discrete variable

**Specification**

  * _Alias:_ set_values

  * _Arguments:_ REALLIST

**Description**

Specify the permissible values for discrete set variables (of type integer, string, or real). See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.


---

##### variables → discrete_uncertain_set → real → elements_per_variable

# elements_per_variable

Number of admissible elements for each set variable

**Specification**

  * _Alias:_ num_set_values

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

Discrete set variables (including design, uncertain, and state) take on only a fixed set of values. For each type (integer, string, or real), this keyword specifies how many admissible values are provided for each variable. If not specified, equal apportionment of elements among variables is assumed, and the number of elements must be evenly divisible by the number of variables.


---

##### variables → discrete_uncertain_set → real → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

##### variables → discrete_uncertain_set → real → set_probabilities

# set_probabilities

This keyword defines the probabilities for the various elements of discrete sets.

**Specification**

  * _Alias:_ set_probs

  * _Arguments:_ REALLIST

  * _Default:_ Equal probability assignments for each set member (1/num_set_values[i])

**Description**

There are three types of `discrete_uncertain_set` variables: integer, string, or real sets. With each of these types, one defines the number of elements of the set per that variable, the values of those elements, and the associated probabilities. For example, if one has an integer discrete uncertain set variable with 3 elements {3,4,8}, then one could define the probabilities associated with those set elements as (for example) 0.2, 0.5, and 0.3. The `set_probabilities` for a particular variable should sum to one over all the elements in that set.


---

#### variables → discrete_uncertain_set → string

# string

Discrete, epistemic uncertain variable - strings within a set

**Topics**

discrete_variables, epistemic_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no discrete uncertain set string variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [elements_per_variable](variables-discrete_uncertain_set-string-elements_per_variable.html) | Number of admissible elements for each set variable  
Required | [elements](variables-discrete_uncertain_set-string-elements.html) | The permissible values for each discrete variable  
Optional | [set_probabilities](variables-discrete_uncertain_set-string-set_probabilities.html) | This keyword defines the probabilities for the various elements of discrete sets.  
Optional | [initial_point](variables-discrete_uncertain_set-string-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-discrete_uncertain_set-string-descriptors.html) | Labels for the variables  
  
**Description**

Discrete set variables may be used to specify categorical choices which are epistemic. For example, if we have three possible forms for a physics model (model 1, 2, or 3) and there is epistemic uncertainty about which one is correct, a discrete uncertain set may be used to represent this type of uncertainty.

This variable is defined by a set of strings, in which the discrete value may take any value within the string set (for example, the set may be defined as ‘coarse’, ‘medium’, and ‘fine’)

Other epistemic types include:

  * `[continuous_interval_uncertain](../../usingdakota/reference/variables-continuous_interval_uncertain.html)`

  * `[discrete_interval_uncertain](../../usingdakota/reference/variables-discrete_interval_uncertain.html)`

  * discrete_uncertain_set `[integer](../../usingdakota/reference/variables-discrete_uncertain_set-integer.html)`

  * discrete_uncertain_set `[real](../../usingdakota/reference/variables-discrete_uncertain_set-real.html)`

**Examples**

    discrete_uncertain_set
     string
     num_set_values 2             3
     set_values     'red' 'blue'  'coarse' 'medium' 'fine'
     descriptors    'ds1'         'ds2'


---

##### variables → discrete_uncertain_set → string → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ dussv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → discrete_uncertain_set → string → elements

# elements

The permissible values for each discrete variable

**Specification**

  * _Alias:_ set_values

  * _Arguments:_ STRINGLIST

**Description**

Specify the permissible values for discrete set variables (of type integer, string, or real). See [Usage Notes](../inputfile/variables.html#variables-usage) for tips on specifying discrete set variables.


---

##### variables → discrete_uncertain_set → string → elements_per_variable

# elements_per_variable

Number of admissible elements for each set variable

**Specification**

  * _Alias:_ num_set_values

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

Discrete set variables (including design, uncertain, and state) take on only a fixed set of values. For each type (integer, string, or real), this keyword specifies how many admissible values are provided for each variable. If not specified, equal apportionment of elements among variables is assumed, and the number of elements must be evenly divisible by the number of variables.


---

##### variables → discrete_uncertain_set → string → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

##### variables → discrete_uncertain_set → string → set_probabilities

# set_probabilities

This keyword defines the probabilities for the various elements of discrete sets.

**Specification**

  * _Alias:_ set_probs

  * _Arguments:_ REALLIST

  * _Default:_ Equal probability assignments for each set member (1/num_set_values[i])

**Description**

There are three types of `discrete_uncertain_set` variables: integer, string, or real sets. With each of these types, one defines the number of elements of the set per that variable, the values of those elements, and the associated probabilities. For example, if one has an integer discrete uncertain set variable with 3 elements {3,4,8}, then one could define the probabilities associated with those set elements as (for example) 0.2, 0.5, and 0.3. The `set_probabilities` for a particular variable should sum to one over all the elements in that set.


---

### variables → exponential_uncertain

# exponential_uncertain

Aleatory uncertain variable - exponential

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no exponential uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [betas](variables-exponential_uncertain-betas.html) | Parameter of the exponential distribution  
Optional | [initial_point](variables-exponential_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-exponential_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The exponential distribution is often used for modeling failure rates.

The density function for the exponential distribution is given by:

\\[f(x) = \frac{1}{\beta} \exp \left( \frac{-x}{\beta} \right),\\]

where \\(\mu_{E} = \beta\\) and \\(\sigma^2_{E} = \beta^2\\) .

Note that this distribution is a special case of the more general gamma distribution.

**Theory**

When used with some methods such as design of experiments and multidimensional parameter studies, distribution bounds are inferred to be [0, \\(\mu + 3 \sigma\\) ].

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means.


---

#### variables → exponential_uncertain → betas

# betas

Parameter of the exponential distribution

**Specification**

  * _Alias:_ euv_betas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\beta\\) parameters to define the distributions of the exponential random variables. Length must match the other parameters and the number of exponential random variables.


---

#### variables → exponential_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ euv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ euv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → exponential_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

### variables → frechet_uncertain

# frechet_uncertain

Aleatory uncertain variable - Frechet

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no frechet uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [alphas](variables-frechet_uncertain-alphas.html) | First parameter of the Frechet distribution  
Required | [betas](variables-frechet_uncertain-betas.html) | Second parameter of the Frechet distribution  
Optional | [initial_point](variables-frechet_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-frechet_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The Frechet distribution is also referred to as the Type II Largest Extreme Value distribution. The distribution of maxima in sample sets from a population with a lognormal distribution will asymptotically converge to this distribution. It is commonly used to model non-negative demand variables.

The density function for the frechet distribution is:

\\[f(x) = \frac{\alpha}{\beta} \left( \frac{\beta}{x} \right)^{\alpha+1} \exp \left( -\left(\frac{\beta}{x}\right)^\alpha \right),\\]

where \\(\mu = \beta\Gamma(1-\frac{1}{\alpha}),\\) and \\(\sigma^2 = \beta^2[\Gamma(1-\frac{2}{\alpha})-\Gamma^2(1-\frac{1}{\alpha})]\\)

**Theory**

When used with some methods such as design of experiments and multidimensional parameter studies, distribution bounds are inferred to be [0, \\(\mu + 3 \sigma\\) ].

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means.


---

#### variables → frechet_uncertain → alphas

# alphas

First parameter of the Frechet distribution

**Specification**

  * _Alias:_ fuv_alphas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\alpha\\) parameters to define the distributions of the Frechet random variables. Length must match the other parameters and the number of Frechet random variables.


---

#### variables → frechet_uncertain → betas

# betas

Second parameter of the Frechet distribution

**Specification**

  * _Alias:_ fuv_betas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\beta\\) parameters to define the distributions of the Frechet random variables. Length must match the other parameters and the number of Frechet random variables.


---

#### variables → frechet_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ fuv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ fuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → frechet_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

### variables → gamma_uncertain

# gamma_uncertain

Aleatory uncertain variable - gamma

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no gamma uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [alphas](variables-gamma_uncertain-alphas.html) | First parameter of the gamma distribution  
Required | [betas](variables-gamma_uncertain-betas.html) | Second parameter of the gamma distribution  
Optional | [initial_point](variables-gamma_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-gamma_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The gamma distribution is sometimes used to model time to complete a task, such as a repair or service task. It is a very flexible distribution with its shape governed by alpha and beta.

The density function for the gamma distribution is given by:

\\[f(x) = \frac{ {x}^{\alpha-1} \exp \left( \frac{-x}{\beta} \right) } { \beta^{\alpha}\Gamma(\alpha) },\\]

where \\(\mu = \alpha\beta,\\) and \\(\sigma^2 = \alpha\beta^2\\) . Note that the exponential distribution is a special case of this distribution for parameter \\(\alpha = 1\\) .

**Theory**

When used with some methods such as design of experiments and multidimensional parameter studies, distribution bounds are inferred to be [0, \\(\mu + 3 \sigma\\) ].

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means.


---

#### variables → gamma_uncertain → alphas

# alphas

First parameter of the gamma distribution

**Specification**

  * _Alias:_ gauv_alphas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\alpha\\) parameters to define the distributions of the gamma random variables. Length must match the other parameters and the number of gamma random variables.


---

#### variables → gamma_uncertain → betas

# betas

Second parameter of the gamma distribution

**Specification**

  * _Alias:_ gauv_betas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\beta\\) parameters to define the distributions of the gamma random variables. Length must match the other parameters and the number of gamma random variables.


---

#### variables → gamma_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ gauv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ gauv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → gamma_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

### variables → geometric_uncertain

# geometric_uncertain

Aleatory uncertain discrete variable - geometric

**Topics**

discrete_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no geometric uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [probability_per_trial](variables-geometric_uncertain-probability_per_trial.html) | Geometric distribution parameter  
Optional | [initial_point](variables-geometric_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-geometric_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The geometric distribution represents the number of successful trials that might occur before a failure is observed.

The density function for the geometric distribution is given by:

\\[f(x) = {p}{(1-p)^{x}},\\]

where \\(p\\) is the probability of failure per trial.


---

#### variables → geometric_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ geuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → geometric_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → geometric_uncertain → probability_per_trial

# probability_per_trial

Geometric distribution parameter

**Specification**

  * _Alias:_ prob_per_trial

  * _Arguments:_ REALLIST

**Description**

The geometric distribution represents the number of successful trials that occur before a failure is observed. The density function for the geometric distribution is given by:

\\[f(x) = {p}{(1-p)^{x}}\\]

where `p` is the probability of failure per trial and `x` is the number of successful trials.


---

### variables → gumbel_uncertain

# gumbel_uncertain

Aleatory uncertain variable - gumbel

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no gumbel uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [alphas](variables-gumbel_uncertain-alphas.html) | First parameter of the gumbel distribution  
Required | [betas](variables-gumbel_uncertain-betas.html) | Second parameter of the gumbel distribution  
Optional | [initial_point](variables-gumbel_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-gumbel_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The Gumbel distribution is also referred to as the Type I Largest Extreme Value distribution. The distribution of maxima in sample sets from a population with a normal distribution will asymptotically converge to this distribution. It is commonly used to model demand variables such as wind loads and flood levels.

The density function for the Gumbel distribution is given by:

\\[f(x) = \alpha \exp \left( -\alpha(x-\beta) \right) \exp \left( -e^{-\alpha(x-\beta)} \right),\\]

where \\(\mu = \beta + \frac{0.5772}{\alpha},\\) and \\(\sigma = \frac{\pi}{\sqrt{6}\alpha}\\) .

**Theory**

When used with some methods such as design of experiments and multidimensional parameter studies, distribution bounds are inferred to be [ \\(\mu - 3 \sigma\\) , \\(\mu + 3 \sigma\\) ]

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means.


---

#### variables → gumbel_uncertain → alphas

# alphas

First parameter of the gumbel distribution

**Specification**

  * _Alias:_ guuv_alphas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\beta\\) parameters to define the distributions of the gumbel random variables. Length must match the other parameters and the number of gumbel random variables.


---

#### variables → gumbel_uncertain → betas

# betas

Second parameter of the gumbel distribution

**Specification**

  * _Alias:_ guuv_betas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\beta\\) parameters to define the distributions of the gumbel random variables. Length must match the other parameters and the number of gumbel random variables.


---

#### variables → gumbel_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ guuv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ guuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → gumbel_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

### variables → histogram_bin_uncertain

# histogram_bin_uncertain

Aleatory uncertain variable - continuous histogram

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no histogram bin uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [pairs_per_variable](variables-histogram_bin_uncertain-pairs_per_variable.html) | Number of pairs defining each histogram bin variable  
Required | [abscissas](variables-histogram_bin_uncertain-abscissas.html) | Real abscissas for a bin histogram  
Required (Choose One) | Density Values | [ordinates](variables-histogram_bin_uncertain-ordinates.html) | Ordinates specifying a “skyline” probability density function  
[counts](variables-histogram_bin_uncertain-counts.html) | Frequency or relative probability of each bin  
Optional | [initial_point](variables-histogram_bin_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-histogram_bin_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

Histogram uncertain variables are typically used to model a set of empirical data. The bin histogram (contrast: `[histogram_point_uncertain](../../usingdakota/reference/variables-histogram_point_uncertain.html)`) is a continuous aleatory distribution characterized by bins of non-zero width where the uncertain variable may lie, together with the relative frequencies of each bin. Hence it can be used to specify a marginal probability density function arising from data.

The `histogram_bin_uncertain` keyword specifies the number of variables to be characterized as continuous histograms. The required sub-keywords are: `[abscissas](../../usingdakota/reference/variables-histogram_bin_uncertain-abscissas.html)` (ranges of values the variable can take on) and either `[ordinates](../../usingdakota/reference/variables-histogram_bin_uncertain-ordinates.html)` or `[counts](../../usingdakota/reference/variables-histogram_bin_uncertain-counts.html)` (characterizing each variable’s frequency information). When using histogram bin variables, each variable must be defined by at least one bin (with two bounding value pairs). When more than one histogram bin variable is active, `[pairs_per_variable](../../usingdakota/reference/variables-histogram_bin_uncertain-pairs_per_variable.html)` can be used to specify unequal apportionment of provided bin pairs among the variables.

The `abscissas` specification defines abscissa values (\\(x\\) coordinates) for the probability density function of each histogram variable. When paired with `counts`, the specifications provide sets of \\((x,c)\\) pairs for each histogram variable where \\(c\\) defines a count (i.e., a frequency or relative probability) associated with a bin. If using bins of unequal width and specification of probability densities is more natural, then the `counts` specification can be replaced with an `ordinates` specification (\\(y\\) coordinates) in order to support interpretation of the input as \\((x,y)\\) pairs defining the profile of a “skyline” probability density function.

Conversion between the two specifications is straightforward: a count/frequency is a cumulative probability quantity defined from the product of the ordinate density value and the \\(x\\) bin width. Thus, in the cases of bins of equal width, ordinate and count specifications are equivalent. In addition, ordinates and counts may be relative values; it is not necessary to scale them as all user inputs will be normalized.

To fully specify a bin-based histogram with \\(n\\) bins (potentially of unequal width), \\(n+1\\) \\((x,c)\\) or \\((x,y)\\) pairs must be specified with the following features:

  * \\(x\\) is the parameter value for the left boundary of a histogram bin and \\(c\\) is the corresponding count for that bin. Alternatively, \\(y\\) defines the ordinate density value for this bin within a skyline probability density function. The right boundary of the bin is defined by the left boundary of the next pair.

  * the final pair specifies the right end of the last bin and must have a \\(c\\) or \\(y\\) value of zero.

  * the \\(x\\) values must be strictly increasing.

  * all \\(c\\) or \\(y\\) values must be positive, except for the last which must be zero.

  * a minimum of two pairs must be specified for each bin-based histogram variable.

**Examples**

The `pairs_per_variable` specification provides for the proper association of multiple sets of \\((x,c)\\) or \\((x,y)\\) pairs with individual histogram variables. For example, in this input snippet

    histogram_bin_uncertain = 2
      pairs_per_variable = 3           4
      abscissas          = 5  8  10    .1 .2 .3 .4
      counts             = 17 21 0     12 24 12 0
      descriptors        = 'hbu_1'     'hbu_2'

`pairs_per_variable` associates the first 3 \\((x,c)\\) pairs from `abscissas` and `counts` {(5,17),(8,21),(10,0)} with one bin-based histogram variable, where one bin is defined between 5 and 8 with a count of 17 and another bin is defined between 8 and 10 with a count of 21. The following set of 4 \\((x,c)\\) pairs {(.1,12),(.2,24),(.3,12),(.4,0)} defines a second bin-based histogram variable containing three equal-width bins with counts 12, 24, and 12 (middle bin is twice as probable as the other two).

**FAQ**

_Difference between bin and point histograms:_ A (continuous) bin histogram specifies bins of non-zero width, whereas a (discrete) point histogram specifies individual point values, which can be thought of as bins with zero width. In the terminology of LHS [[WJ98](../../misc/bibliography.html#id309 "G. D. Wyss and K. H. Jorgensen. A user's guide to LHS: Sandia's Latin hypercube sampling software. Technical Report SAND98-0210, Sandia National Laboratories, Albuquerque, NM, 1998.")], the bin pairs specification defines a “continuous linear” distribution and the point pairs specification defines a “discrete histogram” distribution (although the points are real-valued, the number of possible values is finite).


---

#### variables → histogram_bin_uncertain → abscissas

# abscissas

Real abscissas for a bin histogram

**Specification**

  * _Alias:_ huv_bin_abscissas

  * _Arguments:_ REALLIST

**Description**

A list of real abscissa (“x” coordinate) values characterizing the probability density function for each of the `histogram_bin_uncertain` variables. These are paired with either `[counts](../../usingdakota/reference/variables-histogram_bin_uncertain-counts.html)` or `[ordinates](../../usingdakota/reference/variables-histogram_bin_uncertain-ordinates.html)`. See `[histogram_bin_uncertain](../../usingdakota/reference/variables-histogram_bin_uncertain.html)` for details and examples.


---

#### variables → histogram_bin_uncertain → counts

# counts

Frequency or relative probability of each bin

**Specification**

  * _Alias:_ huv_bin_counts

  * _Arguments:_ REALLIST

**Description**

The `counts` list of real values gives the frequency or relative probability for each bin in a `histogram_bin_uncertain` specification. These are paired with the specified `[abscissas](../../usingdakota/reference/variables-histogram_bin_uncertain-abscissas.html)`. See `[histogram_bin_uncertain](../../usingdakota/reference/variables-histogram_bin_uncertain.html)` for details.


---

#### variables → histogram_bin_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ huv_bin_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ hbuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → histogram_bin_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → histogram_bin_uncertain → ordinates

# ordinates

Ordinates specifying a “skyline” probability density function

**Specification**

  * _Alias:_ huv_bin_ordinates

  * _Arguments:_ REALLIST

**Description**

The `ordinates` list of real values defines the profile of a “skyline” probability density function by pairing with the specified `[abscissas](../../usingdakota/reference/variables-histogram_bin_uncertain-abscissas.html)`. See `[histogram_bin_uncertain](../../usingdakota/reference/variables-histogram_bin_uncertain.html)` for details.


---

#### variables → histogram_bin_uncertain → pairs_per_variable

# pairs_per_variable

Number of pairs defining each histogram bin variable

**Specification**

  * _Alias:_ num_pairs

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

By default, the list of `abscissas` and `counts` or `ordinates` will be evenly divided among the `histogram_bin_uncertain` variables. `pairs_per_variable` is a list of integers that specify the number of pairs to apportion to each variable.


---

### variables → histogram_point_uncertain

# histogram_point_uncertain

Aleatory uncertain variable - discrete histogram

**Topics**

discrete_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no histogram point uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [integer](variables-histogram_point_uncertain-integer.html) | Integer valued point histogram variable  
Optional | [string](variables-histogram_point_uncertain-string.html) | String (categorical) valued point histogram variable  
Optional | [real](variables-histogram_point_uncertain-real.html) | Real valued point histogram variable  
  
**Description**

Histogram uncertain variables are typically used to model a set of empirical data. When the variables take on only discrete values or categories, a discrete, or point histogram is used to describe their probability mass function (one could think of this as a `[histogram_bin_uncertain](../../usingdakota/reference/variables-histogram_bin_uncertain.html)` variable with “bins” of zero width). Dakota supports integer-, string-, and real-valued point histograms.

Point histograms are similar to `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)`, but as they are uncertain variables, include the relative probabilities of observing the different values within the set.

The `histogram_point_uncertain` keyword is followed by one or more of `integer`, `string`, or `real`, each of which specify the number of variables to be characterized as discrete histograms of that sub-type.

Each discrete histogram variable is specified by one or more abscissa/count pairs. The `abscissas`, are the possible values the variable can take on (\\(x\\) coordinates of type integer, string, or real), and must be specified in increasing order. These are paired with `counts` \\(c\\) which provide the frequency of the given value or string, relative to other possible values/strings.

Thus, to fully specify a point-based histogram with \\(n\\) points, \\(n\\) \\((x,c)\\) pairs must be specified with the following features:

  * \\(x\\) is the point value (integer, string, or real) and \\(c\\) is the corresponding count for that value.

  * the \\(x\\) values must be strictly increasing (lexicographically for strings).

  * all \\(c\\) values must be positive.

  * a minimum of one pair must be specified for each point-based histogram.

**Examples**

The `pairs_per_variable` specification provides for the proper association of multiple sets of \\((x,c)\\) or \\((x,y)\\) pairs with individual histogram variables. For example, in the following specification,

    histogram_point_uncertain
      integer            = 2
      pairs_per_variable = 2     3
      abscissas          = 3 4   100 200 300
      counts             = 1 1   1   2   1

`pairs_per_variable` associates the \\((x,c)\\) pairs {(3,1),(4,1)} with one point-based histogram variable (where the values 3 and 4 are equally probable) and associates the \\((x,c)\\) pairs {(100,1),(200,2),(300,1)} with a second point-based histogram variable (where the value 200 is twice as probable as either 100 or 300).

**FAQ**

_Difference between bin and point histograms:_ A (continuous) bin histogram specifies bins of non-zero width, whereas a (discrete) point histogram specifies individual point values, which can be thought of as bins with zero width. In the terminology of LHS [[WJ98](../../misc/bibliography.html#id309 "G. D. Wyss and K. H. Jorgensen. A user's guide to LHS: Sandia's Latin hypercube sampling software. Technical Report SAND98-0210, Sandia National Laboratories, Albuquerque, NM, 1998.")], the bin pairs specification defines a “continuous linear” distribution and the point pairs specification defines a “discrete histogram” distribution (although the points are real-valued, the number of possible values is finite).


---

#### variables → histogram_point_uncertain → integer

# integer

Integer valued point histogram variable

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [pairs_per_variable](variables-histogram_point_uncertain-integer-pairs_per_variable.html) | Number of pairs defining each histogram point integer variable  
Required | [abscissas](variables-histogram_point_uncertain-integer-abscissas.html) | Integer abscissas for a point histogram  
Required | [counts](variables-histogram_point_uncertain-integer-counts.html) | Counts for integer-valued point histogram  
Optional | [initial_point](variables-histogram_point_uncertain-integer-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-histogram_point_uncertain-integer-descriptors.html) | Labels for the variables  
  
**Description**

This probability mass function is integer-valued; the abscissa values must all be integers. The `n` abscissa values are paired with `n` `counts` which indicate the relative frequency (mass) of each integer relative to the other specified integers.

**Examples**

    histogram_point_uncertain
      integer = 2
      pairs_per_variable = 2     3
      abscissas          = 3 4   100 200 300
      counts             = 1 1   1   2   1

There are two variables, the first one has two possible integer values which are equally probable. The second one has three options, and 200 is twice as probable as either 100 or 300.


---

##### variables → histogram_point_uncertain → integer → abscissas

# abscissas

Integer abscissas for a point histogram

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

A list of integer abscissa (“x” coordinate) values characterizing the probability density function for each of the integer `histogram_point_uncertain` variables. These must be listed in increasing order for each variable, and are paired with `[counts](../../usingdakota/reference/variables-histogram_point_uncertain-integer-counts.html)`. See `[histogram_point_uncertain](../../usingdakota/reference/variables-histogram_point_uncertain.html)` for details and examples.


---

##### variables → histogram_point_uncertain → integer → counts

# counts

Counts for integer-valued point histogram

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Count or frequency for each of `[abscissas](../../usingdakota/reference/variables-histogram_point_uncertain-integer-abscissas.html)`. See `[histogram_point_uncertain](../../usingdakota/reference/variables-histogram_point_uncertain.html)` for details and examples.


---

##### variables → histogram_point_uncertain → integer → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ hpiv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → histogram_point_uncertain → integer → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

##### variables → histogram_point_uncertain → integer → pairs_per_variable

# pairs_per_variable

Number of pairs defining each histogram point integer variable

**Specification**

  * _Alias:_ num_pairs

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

By default, the list of `abscissas` and `counts` will be evenly divided among the histogram point integer variables. The number of `pairs_per_variable` specifies the apportionment of abscissa/count pairs among the histogram point integer variables. It must specify one integer >=1 per variable that indicates how many of the (abscissa, count) = (x,c) pairs to associate with that variable.


---

#### variables → histogram_point_uncertain → real

# real

Real valued point histogram variable

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [pairs_per_variable](variables-histogram_point_uncertain-real-pairs_per_variable.html) | Number of pairs defining each histogram point real variable  
Required | [abscissas](variables-histogram_point_uncertain-real-abscissas.html) | Real abscissas for a point histogram  
Required | [counts](variables-histogram_point_uncertain-real-counts.html) | Counts for real-valued point histogram  
Optional | [initial_point](variables-histogram_point_uncertain-real-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-histogram_point_uncertain-real-descriptors.html) | Labels for the variables  
  
**Description**

This probability mass function is real-valued; the abscissa values must all be integers. The `n` abscissa values are paired with `n` `counts` which indicate the relative frequency (mass) of each real relative to the other specified reals.

**Examples**

    histogram_point_uncertain
      real = 2
      pairs_per_variable = 2               3
      abscissas          = 3.1415 4.5389   100 200.112345 300
      counts             = 1      1        1   2          1

There are two variables, the first one has two possible real values which are equally probable. The second one has three possible real value options, and 200.112345 is twice as probable as either 100 or 300.


---

##### variables → histogram_point_uncertain → real → abscissas

# abscissas

Real abscissas for a point histogram

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

A list of real abscissa (“x” coordinate) values characterizing the probability density function for each of the real `histogram_point_uncertain` variables. These must be listed in increasing order for each variable, and are paired with `[counts](../../usingdakota/reference/variables-histogram_point_uncertain-real-counts.html)`. See `[histogram_point_uncertain](../../usingdakota/reference/variables-histogram_point_uncertain.html)` for details and examples.


---

##### variables → histogram_point_uncertain → real → counts

# counts

Counts for real-valued point histogram

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Count or frequency for each of `[abscissas](../../usingdakota/reference/variables-histogram_point_uncertain-real-abscissas.html)`. See `[histogram_point_uncertain](../../usingdakota/reference/variables-histogram_point_uncertain.html)` for details and examples.


---

##### variables → histogram_point_uncertain → real → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ hpruv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → histogram_point_uncertain → real → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

##### variables → histogram_point_uncertain → real → pairs_per_variable

# pairs_per_variable

Number of pairs defining each histogram point real variable

**Specification**

  * _Alias:_ num_pairs

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

By default, the list of `abscissas` and `counts` will be evenly divided among the histogram point real variables. The number of `pairs_per_variable` specifies the apportionment of abscissa/count pairs among the histogram point real variables. It must specify one integer >=1 per variable that indicates how many of the (abscissa, count) = (x,c) pairs to associate with that variable.


---

#### variables → histogram_point_uncertain → string

# string

String (categorical) valued point histogram variable

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [pairs_per_variable](variables-histogram_point_uncertain-string-pairs_per_variable.html) | Number of pairs defining each histogram point string variable  
Required | [abscissas](variables-histogram_point_uncertain-string-abscissas.html) | String abscissas for a point histogram  
Required | [counts](variables-histogram_point_uncertain-string-counts.html) | Counts for string-valued point histogram  
Optional | [initial_point](variables-histogram_point_uncertain-string-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-histogram_point_uncertain-string-descriptors.html) | Labels for the variables  
  
**Description**

This probability mass function is string-valued; the abscissa values must all be strings. The `n` abscissa values are paired with `n` `counts` which indicate the relative frequency (mass) of each string relative to the other specified strings.

**Examples**

    histogram_point_uncertain
      string = 2
      pairs_per_variable = 2            3
      abscissas          = 'no' 'yes'   'function1' 'function2' 'function3'
      counts             = 1     1      1           2           1
      descriptors        = 'vote'       'which_function'

Here there are two variables, the first one (‘vote’) has two possible string values ‘yes’ and ‘no’ which are equally probable. The second one has three options for ‘which_function’, and ‘function2’ is twice as probable as ‘function1’ or ‘function3’.


---

##### variables → histogram_point_uncertain → string → abscissas

# abscissas

String abscissas for a point histogram

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Description**

A list of string abscissa (“x” coordinate) values characterizing the probability density function for each of the string `histogram_point_uncertain` variables. These must be listed in (lexicographically) increasing order for each variable, and are paired with `[counts](../../usingdakota/reference/variables-histogram_point_uncertain-string-counts.html)`. See `[histogram_point_uncertain](../../usingdakota/reference/variables-histogram_point_uncertain.html)` for details and examples.


---

##### variables → histogram_point_uncertain → string → counts

# counts

Counts for string-valued point histogram

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Count or frequency for each of `[abscissas](../../usingdakota/reference/variables-histogram_point_uncertain-string-abscissas.html)`. See `[histogram_point_uncertain](../../usingdakota/reference/variables-histogram_point_uncertain.html)` for details and examples.


---

##### variables → histogram_point_uncertain → string → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ hpsv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

##### variables → histogram_point_uncertain → string → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

##### variables → histogram_point_uncertain → string → pairs_per_variable

# pairs_per_variable

Number of pairs defining each histogram point string variable

**Specification**

  * _Alias:_ num_pairs

  * _Arguments:_ INTEGERLIST

  * _Default:_ equal distribution

**Description**

By default, the list of `abscissas` and `counts` will be evenly divided among the histogram point string variables. The number of `pairs_per_variable` specifies the apportionment of abscissa/count pairs among the histogram point string variables. It must specify one integer >=1 per variable that indicates how many of the (abscissa, count) = (x,c) pairs to associate with that variable.


---

### variables → hypergeometric_uncertain

# hypergeometric_uncertain

Aleatory uncertain discrete variable - hypergeometric

**Topics**

discrete_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no hypergeometric uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [total_population](variables-hypergeometric_uncertain-total_population.html) | Parameter for the hypergeometric probability distribution describing the size of the total population  
Required | [selected_population](variables-hypergeometric_uncertain-selected_population.html) | Distribution parameter for the hypergeometric distribution describing the size of the population subset of interest  
Required | [num_drawn](variables-hypergeometric_uncertain-num_drawn.html) | Distribution parameter for the hypergeometric distribution describing the number of draws from a combined population  
Optional | [initial_point](variables-hypergeometric_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-hypergeometric_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The hypergeometric probability density is used when sampling without replacement from a total population of elements where

  * The resulting element of each sample can be separated into one of two non-overlapping sets

  * The probability of success changes with each sample.

The density function for the hypergeometric distribution is given by:

\\[\begin{split}f(x) = \frac{\left(\begin{array}{c}m\\\x\end{array}\right)\left(\begin{array}{c}{N-m}\\\\{n-x}\end{array}\right)}{\left(\begin{array}{c}N\\\n\end{array}\right)},\end{split}\\]

where the three distribution parameters are:

  * \\(N\\): the total population

  * \\(m\\): the number of items in the selected population (e.g. the number of white balls in the full urn of \\(N\\) items)

  * \\(n\\) the size of the sample drawn (e.g. number of balls drawn)

In addition,

  * \\(x\\), the abscissa of the density function, indicates the number of successes (e.g. drawing a white ball)

  * \\(\left(\begin{array}{c}a\\\b\end{array}\right)\\) indicates a binomial coefficient (“a choose b”)

**Theory**

The hypergeometric is often described using an urn model. For example, say we have a total population containing \\(N\\) balls, and we know that \\(m\\) of the balls are white and the remaining balls are green. If we draw \\(n\\) balls from the urn without replacement, the hypergeometric distribution describes the probability of drawing \\(x\\) white balls.


---

#### variables → hypergeometric_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ hguv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → hypergeometric_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → hypergeometric_uncertain → num_drawn

# num_drawn

Distribution parameter for the hypergeometric distribution describing the number of draws from a combined population

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The density function for the hypergeometric distribution is given by:

\\[\begin{split}f(x) = \frac{\left(\begin{array}{c}m\\\x\end{array}\right)\left(\begin{array}{c}{N-m}\\\\{n-x}\end{array}\right)}{\left(\begin{array}{c}N\\\n\end{array}\right)}\end{split}\\]

where the three distribution parameters are:

  * N is the total population

  * m is the number of items in the selected population (e.g. the number of white balls in the full urn of N items)

  * _n is the size of the sample drawn_ (e.g. number of balls drawn)

In addition,

  * x, the abscissa of the density function, indicates the number of successes (e.g. drawing a white ball)

  * \\(\left(\begin{array}{c}a\\\b\end{array}\right)\\) indicates a binomial coefficient (“a choose b”)


---

#### variables → hypergeometric_uncertain → selected_population

# selected_population

Distribution parameter for the hypergeometric distribution describing the size of the population subset of interest

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The density function for the hypergeometric distribution is given by:

\\[\begin{split}f(x) = \frac{\left(\begin{array}{c}m\\\x\end{array}\right)\left(\begin{array}{c}{N-m}\\\\{n-x}\end{array}\right)}{\left(\begin{array}{c}N\\\n\end{array}\right)}\end{split}\\]

where the three distribution parameters are:

  * N is the total population

  * _m is the number of items in the selected population_ (e.g. the number of white balls in the full urn of N items)

  * n is the size of the sample drawn (e.g. number of balls drawn)

In addition,

  * x, the abscissa of the density function, indicates the number of successes (e.g. drawing a white ball)

  * \\(\left(\begin{array}{c}a\\\b\end{array}\right)\\) indicates a binomial coefficient (“a choose b”)


---

#### variables → hypergeometric_uncertain → total_population

# total_population

Parameter for the hypergeometric probability distribution describing the size of the total population

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The density function for the hypergeometric distribution is given by:

\\[\begin{split}f(x) = \frac{\left(\begin{array}{c}m\\\x\end{array}\right)\left(\begin{array}{c}{N-m}\\\\{n-x}\end{array}\right)}{\left(\begin{array}{c}N\\\n\end{array}\right)}\end{split}\\]

where the three distribution parameters are:

  * _N is the total population_

  * m is the number of items in the selected population (e.g. the number of white balls in the full urn of N items)

  * n is the size of the sample drawn (e.g. number of balls drawn)

In addition,

  * x, the abscissa of the density function, indicates the number of successes (e.g. drawing a white ball)

  * \\(\left(\begin{array}{c}a\\\b\end{array}\right)\\) indicates a binomial coefficient (“a choose b”)


---

### variables → id_variables

# id_variables

Name the variables block; helpful when there are multiple

**Topics**

block_identifier

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ use of last variables parsed

**Description**

The optional `id_variables` keyword accepts a string that uniquely identifies this variables block. A model can then use these variables by specifying the same string in its `variables_pointer` specification.

_Default Behavior_

If the `id_variables` specification is omitted, a particular variables specification will be used by a model only if that model does not include an `variables_pointer` and the variables block was the last (or only) one parsed.

_Usage Tips_

  * It is a best practice to always use explicit variables IDs and pointers to avoid confusion.

  * If only one variables block exists, then `id_variables` can be safely omitted from the variables block (and `variables_pointer` omitted from the model specification(s)), since there is no ambiguity.

**Examples**

For example, a model specification including

    model
      variables_pointer = 'V1'

will link to a response set with

    id_variables = 'V1'


---

### variables → linear_equality_constraint_matrix

# linear_equality_constraint_matrix

Define coefficients of the linear equalities

**Topics**

linear_constraints

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ no linear equality constraints

**Description**

In the equality case, the constraint matrix \\(A\\) provides coefficients for the variables on the left hand side of:

\\[Ax = a_t\\]

The linear_constraints topics page (linked above) outlines a few additional things to consider when using linear constraints.

**Examples**

An optimization involving three variables, `x1`, `x2`, and `x3`, is to be performed. These variables must satisfy a pair of linear equality constraints:

\\[1.5 \cdot x1 + 1.0 \cdot x2 = 5.0\\]

\\[3.0 \cdot x1 - 4.0 \cdot x3 = 0.0\\]

The pair of constraints can be written in matrix form as:

\\[\begin{split}\begin{bmatrix} 1.5 & 1.0 & 0.0 \\\ 3.0 & 0.0 & -4.0 \end{bmatrix} \begin{bmatrix} x1 \\\ x2 \\\ x3 \end{bmatrix} = \begin{bmatrix} 5.0 \\\ 0.0 \end{bmatrix}\end{split}\\]

The coefficient matrix and right hand side are expressed to Dakota in the `[variables](../../usingdakota/reference/variables.html)` section of the input file:

    variables
      continuous_design 2
        descriptors 'x1' 'x2'
    
      linear_equality_constraint_matrix = 1.5  1.0  0.0
                                          3.0  0.0 -4.0
    
      linear_equality_targets = 5.0
                                0.0

For related examples, see the `[linear_inequality_constraint_matrix](../../usingdakota/reference/variables-linear_inequality_constraint_matrix.html)` keyword page.


---

### variables → linear_equality_scale_types

# linear_equality_scale_types

How to scale each linear equality constraint

**Topics**

linear_constraints

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ vector values = “none”

**Description**

Each string in `linear_equality_scale_types` indicates the scaling type for each linear equality constraint. They only have effect when the associated method specifies `scaling`.

The options are:

  * ‘value’ - characteristic value. If this is chosen, then `[linear_equality_scales](../../usingdakota/reference/variables-linear_equality_scales.html)` must be specified; ‘value’ is assumed if scales are given without a `scale_types`

  * ‘auto’ - automatic scaling.

If a single string is specified it will apply to all linear equality constraints. Otherwise the number of strings specified should be equal to the number of linear equalities.

Scaling for linear constraints is applied _after_ any continuous variable scaling.

For example, for variable scaling on continuous design variables x:

\\[\tilde{x}^j = \frac{x^j - x^j_O}{x^j_M}\\]

we have the following system for linear equality constraints

\\[a_L \leq A_i x \leq a_U\\]

\\[a_L \leq A_i \left( \mathrm{diag}(x_M) \tilde{x} + x_O \right) \leq a_U\\]

\\[a_L - A_i x_O \leq A_i \mathrm{diag}(x_M) \tilde{x} \leq a_U - A_i x_O\\]

\\[\tilde{a}_L \leq \tilde{A}_i \tilde{x} \leq \tilde{a}_U\\]

and user-specified or automatically computed scaling multipliers are appplied to this final transformed system, which accounts for continuous design variable scaling. When automatic scaling is in use for linear constraints they are linearly scaled by a computed characteristic value, but not affinely to [0,1].

See the scaling information under specific methods, e.g., `method-*-scaling` for details on how to use this keyword.


---

### variables → linear_equality_scales

# linear_equality_scales

Characteristic values to scale linear equalities

**Topics**

linear_constraints

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ vector values = 1 . (no scaling)

**Description**

Each real value in `linear_equality_scales` is a nonzero characteristic value to be used in scaling each constraint. They only have effect when the associated method specifies `scaling`.

This keyword is required for `[linear_equality_scale_types](../../usingdakota/reference/variables-linear_equality_scale_types.html)` of ‘value’ and optional for ‘auto’.

If a single real value is specified it will apply to all linear equality constraints. Otherwise the number of values should be equal to the number of linear equalities.

Scaling for linear constraints is applied _after_ any continuous variable scaling.

For example, for variable scaling on continuous design variables x:

\\[\tilde{x}^j = \frac{x^j - x^j_O}{x^j_M}\\]

we have the following system for linear inequality constraints

\\[a_L \leq A_i x \leq a_U\\]

\\[a_L \leq A_i \left( \mathrm{diag}(x_M) \tilde{x} + x_O \right) \leq a_U\\]

\\[a_L - A_i x_O \leq A_i \mathrm{diag}(x_M) \tilde{x} \leq a_U - A_i x_O\\]

\\[\tilde{a}_L \leq \tilde{A}_i \tilde{x} \leq \tilde{a}_U\\]

and user-specified or automatically computed scaling multipliers are appplied to this final transformed system, which accounts for continuous design variable scaling. When automatic scaling is in use for linear constraints they are linearly scaled by a computed characteristic value, but not affinely to [0,1].

See the scaling information under specific methods, e.g., `method-*-scaling` for details on how to use this keyword.


---

### variables → linear_equality_targets

# linear_equality_targets

Define target values for the linear equality constraints

**Topics**

linear_constraints

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ vector values = 0 .

**Description**

In the equality case, the targets \\(a_t\\) provide the equality constraint right hand sides:

\\[Ax = a_t\\]

If this is not specified, the defaults for the equality constraint targets enforce a value of `0`. for each constraint:

\\[Ax = 0.0\\]

**Examples**

Examples of specifying linear equality constraints to Dakota are provided on the `[linear_equality_constraint_matrix](../../usingdakota/reference/variables-linear_equality_constraint_matrix.html)` page.


---

### variables → linear_inequality_constraint_matrix

# linear_inequality_constraint_matrix

Define coefficients of the linear inequality constraints

**Topics**

linear_constraints

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ no linear inequality constraints

**Description**

In the inequality case, the constraint matrix \\(A\\) provides coefficients for the variables in the two-sided formulation:

\\[a_l \leq Ax \leq a_u\\]

Where the bounds are optionally specified by `linear_inequality_lower_bounds`, and `linear_inequality_upper_bounds`. The bounds, if not specified, will default to -infinity, and 0, respectively, resulting in one-sided inequalities of the form

\\[Ax \leq 0.0\\]

The linear_constraints topics page (linked above) outlines a few additional things to consider when using linear constraints.

**Examples**

In the first example, an optimization involving two variables, `x1` and `x2`, is to be performed. These variables must satisfy two constraints:

\\[1.5 \cdot x1 + 1.0 \cdot x2 \leq 5.0\\]

\\[x1 \leq x2 \Longrightarrow x1 - x2 \leq 0.0\\]

The pair of constraints can be written in matrix form as:

\\[\begin{split}\begin{bmatrix} 1.5 & 1.0 \\\ 1.0 & -1.0 \end{bmatrix} \begin{bmatrix} x1 \\\ x2 \end{bmatrix} \leq \begin{bmatrix} 5.0 \\\ 0.0 \end{bmatrix}\end{split}\\]

The coefficient matrix and right hand side of this matrix inequality are expressed to Dakota in the variables section of the input file:

    variables
      continuous_design 2
        descriptors 'x1' 'x2'
    
      linear_inequality_constraint_matrix = 1.5   1.0
                                            1.0  -1.0
    
      linear_inequality_upper_bounds = 5.0
                                       0.0

The second example is more complex in two respects. First, some, but not all, of the constraints are “two sided”, with both lower and upper bounds. Second, not all variables participate in all constraints.

There are four variables, `x1`, `x2`, `x3`, and `x4`, and four constraints.

\\[-2.0 \leq 5.0 \cdot x1 + 2.0 \cdot x2 \leq 9.0\\]

\\[0.0 \leq x1 + x3\\]

\\[-8.0 \leq x2 + 6.0 \cdot x4 \leq 8.0\\]

\\[x1 + x2 + x3 \leq 9.0\\]

Or, in matrix form,

\\[\begin{split} \begin{bmatrix} -2.0 \\\ 0.0 \\\ -8.0 \\\ -\infty \end{bmatrix} \leq \begin{bmatrix} 5.0 & 2.0 & 0.0 & 0.0 \\\ 1.0 & 0.0 & 1.0 & 0.0 \\\ 0.0 & 1.0 & 0.0 & 6.0 \\\ 1.0 & 1.0 & 1.0 & 0.0 \end{bmatrix} \begin{bmatrix} x1 \\\ x2 \\\ x3 \\\ x4 \end{bmatrix} \leq \begin{bmatrix} 9.0 \\\ \infty \\\ 8.0 \\\ 9.0 \end{bmatrix}\end{split}\\]

The Dakota specification for this matrix inequality is:

    variables
      continuous_design 4
        descriptors 'x1' 'x2' 'x3' 'x4'
    
      linear_inequality_constraint_matrix = 5.0  2.0  0.0  0.0
                                            1.0  0.0  1.0  0.0
                                            0.0  1.0  0.0  6.0
                                            1.0  1.0  1.0  0.0
    
      linear_inequality_lower_bounds =  -2.0
                                         0.0
                                        -8.0
                                        -inf
    
      linear_inequality_upper_bounds = 9.0
                                       inf
                                       8.0
                                       9.0


---

### variables → linear_inequality_lower_bounds

# linear_inequality_lower_bounds

Define lower bounds for the linear inequality constraint

**Topics**

linear_constraints

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ vector values = -infinity

**Description**

In the inequality case, the lower \\(a_l\\) and upper \\(a_u\\) bounds provide constraint limits for the two-sided formulation:

\\[a_l \leq Ax \leq a_u\\]

Where \\(A\\) is the constrain matrix of variable coefficients.

As with nonlinear inequality constraints (see `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)`), the default linear inequality constraint bounds are selected so that one-sided inequalities of the form

\\[Ax \leq 0.0\\]

result when there are no user bounds specifications (this provides backwards compatibility with previous Dakota versions).

In a user bounds specification, any upper bound values greater than `+bigRealBoundSize` (1.e+30, as defined in Minimizer) are treated as +infinity and any lower bound values less than `-bigRealBoundSize` are treated as -infinity.

This feature is commonly used to drop one of the bounds in order to specify a 1-sided constraint (just as the default lower bounds drop out since `-DBL_MAX` < `-bigRealBoundSize`).

**Examples**

Examples of specifying linear inequality constraints to Dakota are provided on the `[linear_inequality_constraint_matrix](../../usingdakota/reference/variables-linear_inequality_constraint_matrix.html)` page.


---

### variables → linear_inequality_scale_types

# linear_inequality_scale_types

How to scale each linear inequality constraint

**Topics**

linear_constraints

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ vector values = “none”

**Description**

Each string in `linear_inequality_scale_types` indicates the scaling type for each linear inequality constraint. They only have effect when the associated method specifies `scaling`.

The options are:

  * ‘value’ - characteristic value. If this is chosen, then `[linear_inequality_scales](../../usingdakota/reference/variables-linear_inequality_scales.html)` must be specified; ‘value’ is assumed if scales are given without `scale_types`

  * ‘auto’ - automatic scaling.

If a single string is specified it will apply to all linear inequality constraints. Otherwise the number of strings specified should be equal to the number of linear inequalities.

Scaling for linear constraints is applied _after_ any continuous variable scaling.

For example, for variable scaling on continuous design variables x:

\\[\tilde{x}^j = \frac{x^j - x^j_O}{x^j_M}\\]

we have the following system for linear inequality constraints

\\[a_L \leq A_i x \leq a_U\\]

\\[a_L \leq A_i \left( \mathrm{diag}(x_M) \tilde{x} + x_O \right) \leq a_U\\]

\\[a_L - A_i x_O \leq A_i \mathrm{diag}(x_M) \tilde{x} \leq a_U - A_i x_O\\]

\\[\tilde{a}_L \leq \tilde{A}_i \tilde{x} \leq \tilde{a}_U\\]

and user-specified or automatically computed scaling multipliers are appplied to this final transformed system, which accounts for continuous design variable scaling. When automatic scaling is in use for linear constraints they are linearly scaled by a computed characteristic value, but not affinely to [0,1].

See the scaling information under specific methods, e.g., `method-*-scaling` for details on how to use this keyword.


---

### variables → linear_inequality_scales

# linear_inequality_scales

Characteristic values to scale linear inequalities

**Topics**

linear_constraints

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ vector values = 1 . (no scaling)

**Description**

Each real value in `linear_inequality_scales` is a nonzero characteristic value to be used in scaling each constraint. They only have effect when the associated method specifies `scaling`.

This keyword is required for `[linear_inequality_scale_types](../../usingdakota/reference/variables-linear_inequality_scale_types.html)` of <tt>’value’</tt> and optional for ‘auto’.

If a single real value is specified it will apply to all linear inequality constraints. Otherwise the number of values should be equal to the number of linear inequalities.

Scaling for linear constraints is applied _after_ any continuous variable scaling.

For example, for variable scaling on continuous design variables x:

\\[\tilde{x}^j = \frac{x^j - x^j_O}{x^j_M}\\]

we have the following system for linear inequality constraints

\\[a_L \leq A_i x \leq a_U\\]

\\[a_L \leq A_i \left( \mathrm{diag}(x_M) \tilde{x} + x_O \right) \leq a_U\\]

\\[a_L - A_i x_O \leq A_i \mathrm{diag}(x_M) \tilde{x} \leq a_U - A_i x_O\\]

\\[\tilde{a}_L \leq \tilde{A}_i \tilde{x} \leq \tilde{a}_U\\]

and user-specified or automatically computed scaling multipliers are appplied to this final transformed system, which accounts for continuous design variable scaling. When automatic scaling is in use for linear constraints they are linearly scaled by a computed characteristic value, but not affinely to [0,1].

See the scaling information under specific methods, e.g., `method-*-scaling` for details on how to use this keyword.


---

### variables → linear_inequality_upper_bounds

# linear_inequality_upper_bounds

Define upper bounds for the linear inequality constraint

**Topics**

linear_constraints

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ vector values = 0 .

**Description**

In the inequality case, the lower \\(a_l\\) and upper \\(a_u\\) bounds provide constraint limits for the two-sided formulation:

\\[a_l \leq Ax \leq a_u\\]

Where \\(A\\) is the constrain matrix of variable coefficients.

As with nonlinear inequality constraints (see `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)`), the default linear inequality constraint bounds are selected so that one-sided inequalities of the form

\\[Ax \leq 0.0\\]

result when there are no user bounds specifications (this provides backwards compatibility with previous Dakota versions).

In a user bounds specification, any upper bound values greater than `+bigRealBoundSize` (1.e+30, as defined in Minimizer) are treated as +infinity and any lower bound values less than `-bigRealBoundSize` are treated as -infinity.

This feature is commonly used to drop one of the bounds in order to specify a 1-sided constraint (just as the default lower bounds drop out since `-DBL_MAX` < `-bigRealBoundSize`).

**Examples**

Examples of specifying linear inequality constraints to Dakota are provided on the `[linear_inequality_constraint_matrix](../../usingdakota/reference/variables-linear_inequality_constraint_matrix.html)` page.


---

### variables → lognormal_uncertain

# lognormal_uncertain

Aleatory uncertain variable - lognormal

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no lognormal uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Lognormal Characterization | [lambdas](variables-lognormal_uncertain-lambdas.html) | First parameter of the lognormal distribution (option 3)  
[means](variables-lognormal_uncertain-means.html) | First parameter of the lognormal distribution (options 1 & 2)  
Optional | [lower_bounds](variables-lognormal_uncertain-lower_bounds.html) | Specify minimum values  
Optional | [upper_bounds](variables-lognormal_uncertain-upper_bounds.html) | Specify maximium values  
Optional | [initial_point](variables-lognormal_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-lognormal_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

If the logarithm of an uncertain variable X has a normal distribution, that is \\(\log X \sim \mathcal{N}(\mu,\sigma^2)\\) , then \\(X\\) is distributed with a lognormal distribution. The lognormal is often used to model:

  * time to perform some task

  * variables which are the product of a large number of other quantities, by the Central Limit Theorem

  * quantities which cannot have negative values.

The number of lognormal uncertain variables, their means, and either standard deviations or error factors must be specified, while the distribution lower and upper bounds and variable descriptors are optional specifications. These distribution bounds can be used to truncate the tails of lognormal distributions, which as for bounded normal, can result in the mean and the standard deviation of the sample data being different from the mean and standard deviation of the underlying distribution (see “bounded lognormal” and “bounded lognormal-n” distribution types in [[WJ98](../../misc/bibliography.html#id309 "G. D. Wyss and K. H. Jorgensen. A user's guide to LHS: Sandia's Latin hypercube sampling software. Technical Report SAND98-0210, Sandia National Laboratories, Albuquerque, NM, 1998.")]).

For the lognormal variables, one may specify either the mean \\(\mu\\) and standard deviation \\(\sigma\\) of the actual lognormal distribution (option 1), the mean \\(\mu\\) and error factor \\(\epsilon\\) of the actual lognormal distribution (option 2), or the mean \\(\lambda\\) (“lambda”) and standard deviation \\(\zeta\\) (“zeta”) of the underlying normal distribution (option 3).

The conversion equations from lognormal mean \\(\mu\\) and either lognormal error factor \\(\epsilon\\) or lognormal standard deviation \\(\sigma\\) to the mean \\(\lambda\\) and standard deviation \\(\zeta\\) of the underlying normal distribution are as follows:

\\[\zeta = \frac{\ln(\epsilon)}{1.645}\\]

\\[\zeta^2 = \ln\left(\frac{\sigma^2}{\mu^2} + 1\right)\\]

\\[\lambda = \ln(\mu) - \frac{\zeta^2}{2}\\]

Conversions from \\(\lambda\\) and \\(\zeta\\) back to \\(\mu\\) and \\(\epsilon\\) or \\(\sigma\\) are as follows:

\\[\mu = \exp \left( \lambda + \frac{\zeta^2}{2} \right)\\]

\\[\sigma^2 = \exp \left( 2\lambda + \zeta^2 \right) \left( \exp \left(\zeta^2\right) - 1\right)\\]

\\[\epsilon = \exp \left( 1.645\zeta \right)\\]

The density function for the lognormal distribution is:

\\[f(x) = \frac{1}{\sqrt{2\pi}\zeta x} \exp \left( -\frac{1}{2}\left(\frac{\ln(x)-\lambda}{\zeta}\right)^2 \right)\\]

**Theory**

When used with some methods such as design of experiments and multidimensional parameter studies, distribution bounds are inferred to be [0, \\(\mu + 3 \sigma\\) ].

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means, correcting to bounds if needed.


---

#### variables → lognormal_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ lnuv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ lnuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → lognormal_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → lognormal_uncertain → lambdas

# lambdas

First parameter of the lognormal distribution (option 3)

**Specification**

  * _Alias:_ lnuv_lambdas

  * _Arguments:_ REALLIST

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [zetas](variables-lognormal_uncertain-lambdas-zetas.html) | Second parameter of the lognormal distribution (option 3)  
  
**Description**

For the lognormal variables, one may specify the mean \\(\lambda\\) (“lambda”) and standard deviation \\(\zeta\\) (“zeta”) of the underlying normal distribution.


---

##### variables → lognormal_uncertain → lambdas → zetas

# zetas

Second parameter of the lognormal distribution (option 3)

**Specification**

  * _Alias:_ lnuv_zetas

  * _Arguments:_ REALLIST

**Description**

For the lognormal variables, one may specify the mean \\(\lambda\\) (“lambda”) and standard deviation \\(\zeta\\) (“zeta”) of the underlying normal distribution.


---

#### variables → lognormal_uncertain → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ lnuv_lower_bounds

  * _Arguments:_ REALLIST

  * _Default:_ 0

**Description**

Specify minimum values


---

#### variables → lognormal_uncertain → means

# means

First parameter of the lognormal distribution (options 1 & 2)

**Specification**

  * _Alias:_ lnuv_means

  * _Arguments:_ REALLIST

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Spread | [std_deviations](variables-lognormal_uncertain-means-std_deviations.html) | Second parameter of the lognormal distribution (option 1)  
[error_factors](variables-lognormal_uncertain-means-error_factors.html) | Second parameter of the lognormal distribution (option 2)  
  
**Description**

For the lognormal variables, one may specify either the mean \\(\mu\\) and standard deviation \\(\sigma\\) of the actual lognormal distribution, the mean \\(\mu\\) and error factor \\(\epsilon\\) of the actual lognormal distribution.

This corresponds to the mean of the lognormal random variable


---

##### variables → lognormal_uncertain → means → error_factors

# error_factors

Second parameter of the lognormal distribution (option 2)

**Specification**

  * _Alias:_ lnuv_error_factors

  * _Arguments:_ REALLIST

**Description**

For the lognormal variables, one may specify the mean \\(\mu\\) and error factor \\(\epsilon\\) of the actual lognormal distribution.

This specifies the error function of the lognormal random variable.


---

##### variables → lognormal_uncertain → means → std_deviations

# std_deviations

Second parameter of the lognormal distribution (option 1)

**Specification**

  * _Alias:_ lnuv_std_deviations

  * _Arguments:_ REALLIST

**Description**

For the lognormal variables, one may specify either the mean \\(\mu\\) and standard deviation \\(\sigma\\) of the actual lognormal distribution.

This corresponds to the standard deviation of the lognormal random variable.


---

#### variables → lognormal_uncertain → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ lnuv_upper_bounds

  * _Arguments:_ REALLIST

  * _Default:_ infinity

**Description**

Specify maximium values


---

### variables → loguniform_uncertain

# loguniform_uncertain

Aleatory uncertain variable - loguniform

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no loguniform uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [lower_bounds](variables-loguniform_uncertain-lower_bounds.html) | Specify minimum values  
Required | [upper_bounds](variables-loguniform_uncertain-upper_bounds.html) | Specify maximium values  
Optional | [initial_point](variables-loguniform_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-loguniform_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

If the logarithm of an uncertain variable \\(X\\) has a uniform distribution, that is \\(\log X \sim \mathcal{U}(L, U),\\) then \\(X\\) is distributed with a loguniform distribution. The distribution lower bound is \\(L\\) and upper bound is \\(U\\) The loguniform distribution has the density function:

\\[f(x) = \frac{1}{ x \left( \ln U - \ln L) \right) }\\]

**Theory**

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means.


---

#### variables → loguniform_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ luuv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ luuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → loguniform_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → loguniform_uncertain → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ luuv_lower_bounds

  * _Arguments:_ REALLIST

**Description**

Specify minimum values


---

#### variables → loguniform_uncertain → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ luuv_upper_bounds

  * _Arguments:_ REALLIST

**Description**

Specify maximium values


---

### variables → mixed

# mixed

Maintain continuous/discrete variable distinction

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ relaxed (branch and bound), mixed (all other methods)

**Description**

The variables domain specifies how the discrete variables are treated. If the user specifies `mixed` in the variable specification block, the continuous and discrete variables are treated separately. If the user specifies `relaxed` in the variable specification block, the discrete variables are relaxed and treated as continuous variables. This may be useful in optimization problems involving both continuous and discrete variables when a user would like to use an optimization method that is designed for continuous variable optimization. All Dakota methods have a default value of mixed for the domain type except for the branch-and-bound method which has a default domain type of relaxed. Note that the branch-and-bound method is under development at this time. Finally, note that the domain selection applies to all variable types: design, aleatory uncertain, epistemic uncertain, and state.

With respect to domain type, if the user does not specify an explicit override of `mixed` or `relaxed`, Dakota infers the domain type from the method. As mentioned above, all methods currently use a mixed domain as a default, except the branch-and-bound method which is under development.


---

### variables → negative_binomial_uncertain

# negative_binomial_uncertain

Aleatory uncertain discrete variable - negative binomial

**Topics**

discrete_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no negative binomial uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [probability_per_trial](variables-negative_binomial_uncertain-probability_per_trial.html) | A negative binomial distribution parameter  
Required | [num_trials](variables-negative_binomial_uncertain-num_trials.html) | A negative binomial distribution parameter  
Optional | [initial_point](variables-negative_binomial_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-negative_binomial_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The density function for the negative binomial distribution is given by:

\\[\begin{split}f(x) = \left(\begin{array}{c}{n+x-1}\\\\{x}\end{array}\right){p^n}{(1-p)^{x}},\end{split}\\]

where

  * \\(p\\) is the probability of success per trial

  * \\(n\\) is the number of successful trials

  * \\(x\\) is the number of failures

**Theory**

The negative binomial distribution is typically used to predict the number of failures observed when repeating a test until a total of \\(n\\) successes have occurred, where each test has a probability \\(p\\) of success.


---

#### variables → negative_binomial_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ nbuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → negative_binomial_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → negative_binomial_uncertain → num_trials

# num_trials

A negative binomial distribution parameter

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The negative binomial distribution is typically used to predict the number of failures observed when repeating a test until a total of `n` successes have occurred, where each test has a probability `p` of success.

The density function for the negative binomial distribution is given by:

\\[\begin{split}f(x) = \left(\begin{array}{c}{n+x-1}\\\\{x}\end{array}\right){p^n}{(1-p)^{x}}\end{split}\\]

where

  * \\(p\\) is the probability of success per trial

  * \\(n\\) is the number of successful trials

  * \\(X\\) is the number of failures


---

#### variables → negative_binomial_uncertain → probability_per_trial

# probability_per_trial

A negative binomial distribution parameter

**Specification**

  * _Alias:_ prob_per_trial

  * _Arguments:_ REALLIST

**Description**

The negative binomial distribution is typically used to predict the number of failures observed when repeating a test until a total of `n` successes have occurred, where each test has a probability `p` of success.

The density function for the negative binomial distribution is given by:

\\[\begin{split}f(x) = \left(\begin{array}{c}{n+x-1}\\\\{x}\end{array}\right){p^n}{(1-p)^{x}}\end{split}\\]

where

  * \\(p\\) is the probability of success per trial

  * \\(n\\) is the number of successful trials

  * \\(X\\) is the number of failures


---

### variables → normal_uncertain

# normal_uncertain

Aleatory uncertain variable - normal (Gaussian)

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no normal uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [means](variables-normal_uncertain-means.html) | First parameter of the distribution  
Required | [std_deviations](variables-normal_uncertain-std_deviations.html) | Second parameter of the distribution  
Optional | [lower_bounds](variables-normal_uncertain-lower_bounds.html) | Specify minimum values  
Optional | [upper_bounds](variables-normal_uncertain-upper_bounds.html) | Specify maximium values  
Optional | [initial_point](variables-normal_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-normal_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The number of normal uncertain variables, their means, and standard deviations are required specifications, while the distribution lower and upper bounds and variable descriptors are optional specifications. The normal distribution is widely used to model uncertain variables such as population characteristics. It is also used to model the mean of a sample: as the sample size becomes very large, the Central Limit Theorem states that the distribution of the mean becomes approximately normal, regardless of the distribution of the original variables.

The density function for the normal distribution is:

\\[f(x) = \frac{1}{\sqrt{2\pi}\sigma} \exp \left(-\frac{1}{2}\left(\frac{x-\mu}{\sigma}\right)^2 \right),\\]

where \\(\mu\\) and \\(\sigma\\) are the mean and standard deviation of the normal distribution, respectively.

Note that if you specify bounds for a normal distribution, the sampling occurs from the underlying distribution with the given mean and standard deviation, but samples are not taken outside the bounds (see “bounded normal” distribution type in [[WJ98](../../misc/bibliography.html#id309 "G. D. Wyss and K. H. Jorgensen. A user's guide to LHS: Sandia's Latin hypercube sampling software. Technical Report SAND98-0210, Sandia National Laboratories, Albuquerque, NM, 1998.")]). This can result in the mean and the standard deviation of the sample data being different from the mean and standard deviation of the underlying distribution. For example, if you are sampling from a normal distribution with a mean of 5 and a standard deviation of 3, but you specify bounds of 1 and 7, the resulting mean of the samples will be around 4.3 and the resulting standard deviation will be around 1.6. This is because you have bounded the original distribution significantly, and asymetrically, since 7 is closer to the original mean than 1.

**Theory**

When used with some methods such as design of experiments and multidimensional parameter studies, distribution bounds are inferred to be [ \\(\mu - 3 \sigma\\) , \\(\mu + 3 \sigma\\) ]

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means, correcting to bounds if needed.


---

#### variables → normal_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ nuv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ nuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → normal_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → normal_uncertain → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ nuv_lower_bounds

  * _Arguments:_ REALLIST

  * _Default:_ -infinity

**Description**

Specify minimum values


---

#### variables → normal_uncertain → means

# means

First parameter of the distribution

**Specification**

  * _Alias:_ nuv_means

  * _Arguments:_ REALLIST

**Description**

Means


---

#### variables → normal_uncertain → std_deviations

# std_deviations

Second parameter of the distribution

**Specification**

  * _Alias:_ nuv_std_deviations

  * _Arguments:_ REALLIST

**Description**

Standard deviation


---

#### variables → normal_uncertain → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ nuv_upper_bounds

  * _Arguments:_ REALLIST

  * _Default:_ infinity

**Description**

Specify maximium values


---

### variables → poisson_uncertain

# poisson_uncertain

Aleatory uncertain discrete variable - Poisson

**Topics**

discrete_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no poisson uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [lambdas](variables-poisson_uncertain-lambdas.html) | The parameter for the Poisson distribution, the expected number of events in the time interval of interest  
Optional | [initial_point](variables-poisson_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-poisson_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The Poisson distribution is used to predict the number of discrete events that happen in a single time interval. The random events occur uniformly and independently. The expected number of occurences in a single time interval is \\(\lambda\\) , which must be a positive real number. For example, if events occur on average 4 times per year and we are interested in the distribution of events over six months, \\(\lambda\\) would be 2. However, if we were interested in the distribution of events occuring over 5 years, \\(\lambda\\) would be 20.

The probability mass function for the poisson distribution is given by:

\\[f(x) = \frac{\lambda^{x} e^{-\lambda}}{x!},\\]

where

  * \\(\lambda\\) is the expected number of events occuring in a single time interval

  * \\(x\\) is the number of events that occur in this time period

  * f(x) is the probability that \\(x\\) events occur in this time period

**Theory**

When used with some methods such as design of experiments and multidimensional parameter studies, distribution bounds are inferred to be [0, \\(\mu + 3 \sigma\\) ].

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means.


---

#### variables → poisson_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ puv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → poisson_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → poisson_uncertain → lambdas

# lambdas

The parameter for the Poisson distribution, the expected number of events in the time interval of interest

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

The density function for the poisson distribution is given by:

\\[f(x) = \frac{\lambda e^{-\lambda}}{x!}\\]

where \\(\lambda\\) is the frequency of events happening, and `x` is the number of events that occur.


---

### variables → relaxed

# relaxed

Allow treatment of discrete variables as continuous

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The variables domain specifies how the discrete variables are treated. If the user specifies `mixed` in the variable specification block, the continuous and discrete variables are treated separately. If the user specifies `relaxed` in the variable specification block, the discrete variables are relaxed and treated as continuous variables. This may be useful in optimization problems involving both continuous and discrete variables when a user would like to use an optimization method that is designed for continuous variable optimization. All Dakota methods have a default value of mixed for the domain type except for the branch-and-bound method which has a default domain type of relaxed. Note that the branch-and-bound method is under development at this time. Finally, note that the domain selection applies to all variable types: design, aleatory uncertain, epistemic uncertain, and state.

With respect to domain type, if the user does not specify an explicit override of `mixed` or `relaxed`, Dakota infers the domain type from the method. As mentioned above, all methods currently use a mixed domain as a default, except the branch-and-bound method which is under development.


---

### variables → triangular_uncertain

# triangular_uncertain

Aleatory uncertain variable - triangular

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no triangular uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [modes](variables-triangular_uncertain-modes.html) | Distribution parameter  
Required | [lower_bounds](variables-triangular_uncertain-lower_bounds.html) | Specify minimum values  
Required | [upper_bounds](variables-triangular_uncertain-upper_bounds.html) | Specify maximium values  
Optional | [initial_point](variables-triangular_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-triangular_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The triangular distribution is often used when one does not have much data or information, but does have an estimate of the most likely value and the lower and upper bounds. The number of triangular uncertain variables, the modes, and the distribution lower and upper bounds are required specifications, whilevariable descriptors is an optional specification.

The density function for the triangular distribution is:

\\[f(x) = \frac{2(x-L)}{(U-L)(M-L)}\\]

if \\(L\leq x \leq M\\) , and

\\[f(x) = \frac{2(U-x)}{(U-L)(U-M)}\\]

if \\(M\leq x \leq U\\) , and 0 elsewhere. In these equations, \\(L\\) is the lower bound, \\(U\\) is the upper bound, and \\(M\\) is the mode of the triangular distribution.


---

#### variables → triangular_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ tuv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ tuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → triangular_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → triangular_uncertain → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ tuv_lower_bounds

  * _Arguments:_ REALLIST

**Description**

Specify minimum values


---

#### variables → triangular_uncertain → modes

# modes

Distribution parameter

**Specification**

  * _Alias:_ tuv_modes

  * _Arguments:_ REALLIST

**Description**

Specify the modes


---

#### variables → triangular_uncertain → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ tuv_upper_bounds

  * _Arguments:_ REALLIST

**Description**

Specify maximium values


---

### variables → uncertain_correlation_matrix

# uncertain_correlation_matrix

Correlation among aleatory uncertain variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ identity matrix (uncorrelated)

**Description**

Aleatory uncertain variables may have correlations specified through use of an `uncertain_correlation_matrix` specification. This specification is generalized in the sense that its specific meaning depends on the nondeterministic method in use.

When the method is a nondeterministic sampling method (i.e., `[sampling](../../usingdakota/reference/method-sampling.html)`), then the correlation matrix specifies _rank correlations_ [[IC82](../../misc/bibliography.html#id159 "R. L. Iman and W. J. Conover. A distribution-free approach to inducing rank correlation among input variables. Communications in Statistics: Simulation and Computation, B11\(3\):311–334, 1982.")]. When the method is a reliability (i.e., `[local_reliability](../../usingdakota/reference/method-local_reliability.html)` or `[global_reliability](../../usingdakota/reference/method-global_reliability.html)`) or stochastic expansion (i.e., `[polynomial_chaos](../../usingdakota/reference/method-polynomial_chaos.html)` or `[stoch_collocation](../../usingdakota/reference/method-stoch_collocation.html)`) method, then the correlation matrix specifies _correlation coefficients_ (normalized covariance) [[HM00](../../misc/bibliography.html#id139 "A. Haldar and S. Mahadevan. Probability, Reliability, and Statistical Methods in Engineering Design. Wiley, New York, 2000.")].

In either of these cases, specifying the identity matrix results in uncorrelated uncertain variables (the default). The matrix input should be symmetric and have all \\(n^2\\) entries where _n_ is the total number of aleatory uncertain variables. Ordering of the aleatory uncertain variables is as shown in the input-specification-ordered table in `[variables](../../usingdakota/reference/variables.html)` for `normal`, `lognormal`, …, `histogram_point`.

When additional variable types are activated, they assume uniform distributions, and the ordering is as listed on `[variables](../../usingdakota/reference/variables.html)`.

**Examples**

Consider the following random variables, distributions and correlations:

  * \\(X_1\\) , normal, uncorrelated with others

  * \\(X_2\\) , normal, correlated with \\(X_3\\) , \\(X_4\\) and \\(X_5\\)

  * \\(X_3\\) , weibull , correlated with \\(X_5\\)

  * \\(X_4\\) , exponential, correlated with \\(X_3\\) , \\(X_4\\) and \\(X_5\\)

  * \\(X_5\\) , normal, correlated with \\(X_5\\) These correlations are captured by the following commands (order of the variables is respected).

    uncertain_correlation_matrix
      # ordering normal, exponential, weibull
      # X_1   X_2   X_5   X_4   X_3
        1.00  0.00  0.00  0.00  0.00
        0.00  1.00  0.50  0.24  0.78
        0.00  0.50  1.00  0.00  0.20
        0.00  0.24  0.00  1.00  0.49
        0.00  0.78  0.20  0.49  1.00


---

### variables → uniform_uncertain

# uniform_uncertain

Aleatory uncertain variable - uniform

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no uniform uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [lower_bounds](variables-uniform_uncertain-lower_bounds.html) | Specify minimum values  
Required | [upper_bounds](variables-uniform_uncertain-upper_bounds.html) | Specify maximium values  
Optional | [initial_point](variables-uniform_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-uniform_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The number of uniform uncertain variables and their distribution lower and upper bounds are required specifications, while variable descriptors is an optional specification. The uniform distribution has the density function:

\\[f(x) = \frac{1}{U-L},\\]

where

\\(U\\) and \\(L\\) are the upper and lower bounds of the uniform distribution, respectively. The mean of the uniform distribution is \\(\frac{U+L}{2}\\) and the variance is \\(\frac{(U-L)^2}{12}\\) .

**Theory**

This distribution is a special case of the more general beta distribution.

For some methods, including vector and centered parameter studies, an initial point is needed for the uncertain variables. When not given explicitly, these variables are initialized to their means.


---

#### variables → uniform_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ uuv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ uuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → uniform_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.


---

#### variables → uniform_uncertain → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ uuv_lower_bounds

  * _Arguments:_ REALLIST

**Description**

Specify minimum values


---

#### variables → uniform_uncertain → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ uuv_upper_bounds

  * _Arguments:_ REALLIST

**Description**

Specify maximium values


---

### variables → weibull_uncertain

# weibull_uncertain

Aleatory uncertain variable - Weibull

**Topics**

continuous_variables, aleatory_uncertain_variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ no weibull uncertain variables

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [alphas](variables-weibull_uncertain-alphas.html) | First parameter of the Weibull distribution  
Required | [betas](variables-weibull_uncertain-betas.html) | Second parameter of the Weibull distribution  
Optional | [initial_point](variables-weibull_uncertain-initial_point.html) | Initial values for variables  
Optional | [descriptors](variables-weibull_uncertain-descriptors.html) | Labels for the variables  
  
**Description**

The Weibull distribution is also referred to as the Type III Smallest Extreme Value distribution. The Weibull distribution is commonly used in reliability studies to predict the lifetime of a device. It is also used to model capacity variables such as material strength.

The density function for the Weibull distribution is given by:

\\[f(x) = \frac{\alpha}{\beta} \left(\frac{x}{\beta}\right)^{\alpha-1} \exp \left( -\left(\frac{x}{\beta}\right)^{\alpha} \right),\\]

where \\(\mu = \beta \Gamma\left( 1+\frac{1}{\alpha} \right),\\) and \\(\sigma = \mu \sqrt{\frac{\Gamma(1+\frac{2}{\alpha})}{\Gamma^2(1+\frac{1}{\alpha})} - 1}\\)


---

#### variables → weibull_uncertain → alphas

# alphas

First parameter of the Weibull distribution

**Specification**

  * _Alias:_ wuv_alphas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\alpha\\) parameters to define the distributions of the Weibull random variables.

Length must match the other parameters and the number of Weibull random variables.


---

#### variables → weibull_uncertain → betas

# betas

Second parameter of the Weibull distribution

**Specification**

  * _Alias:_ wuv_betas

  * _Arguments:_ REALLIST

**Description**

Specifies the list of \\(\beta\\) parameters to define the distributions of the Weibull random variables. Length must match the other parameters and the number of Weibull random variables.


---

#### variables → weibull_uncertain → descriptors

# descriptors

Labels for the variables

**Specification**

  * _Alias:_ wuv_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ wuv_{i}

**Description**

The optional variable labels specification `descriptors` is a list of strings which identify the variables. These are used in console and tabular output.

The default descriptor strings use a variable type-dependent root string plus a numeric identifier.


---

#### variables → weibull_uncertain → initial_point

# initial_point

Initial values for variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Specifies the initial or default value a variable should assume, for example when starting a method or fixing the value of an inactive variable.

_Default Behavior_

Range variables, including `[continuous_design](../../usingdakota/reference/variables-continuous_design.html)`, `[continuous_state](../../usingdakota/reference/variables-continuous_state.html)`, `[discrete_design_range](../../usingdakota/reference/variables-discrete_design_range.html)`, and `[discrete_state_range](../../usingdakota/reference/variables-discrete_state_range.html)` are initialized to:

  * Mean/midpoint (or next smaller integer value) when doubly-bounded

  * 0 when unbounded

  * 0 when semi-bounded and zero is in the half interval, otherwise to the specified lower or upper bound

Set variables, including `[discrete_design_set](../../usingdakota/reference/variables-discrete_design_set.html)` and `[discrete_state_set](../../usingdakota/reference/variables-discrete_state_set.html)` default to the middle (or next smaller) value of the ordered set of admissible values.

Uncertain variables default initialize to their mean (or nearest admissible discrete) value, truncating to any specified lower or upper bounds as needed. Epistemic uncertain variable means are calculated making a probabilistic assumption on the specified intervals or set values.

