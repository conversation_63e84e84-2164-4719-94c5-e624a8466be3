# Webkit style was loosely based on the Qt style
BasedOnStyle: WebKit

Standard: Cpp11


IndentWidth : 4
TabWidth : 4
UseTab : ForIndentation
# Indent width for line continuations.
ContinuationIndentWidth: 4

# Leave the line breaks up to the user.
# Note that this may be changed at some point in the future.
ColumnLimit: 120
# How much weight do extra characters after the line length limit have.
PenaltyExcessCharacter: 4

# Disable reflow of qdoc comments: indentation rules are different.
# Translation comments are also excluded.
CommentPragmas: "^!|^:"

# We want a space between the type and the star for pointer types.
PointerBindsToType: false

# We use template< without space.
SpaceAfterTemplateKeyword: false

# We want to break before the operators, but not before a '='.
BreakBeforeBinaryOperators: NonAssignment

# Braces are usually attached, but not after functions or class declarations.
BreakBeforeBraces: Custom
BraceWrapping:
    AfterClass: true
    AfterControlStatement: false
    AfterEnum: true
    AfterFunction: true
    AfterNamespace: true
    AfterObjCDeclaration: false
    AfterStruct: true
    AfterUnion: false
    BeforeCatch: false
    BeforeElse: false
    IndentBraces: false

# When constructor initializers do not fit on one line, put them each on a new line.
ConstructorInitializerAllOnOneLineOrOnePerLine: true
# Indent initializers by 4 spaces
ConstructorInitializerIndentWidth: 4

# No indentation for namespaces.
NamespaceIndentation: None

# Horizontally align arguments after an open bracket.
# The coding style does not specify the following, but this is what gives
# results closest to the existing code.
AlignAfterOpenBracket: true
AlwaysBreakTemplateDeclarations: true

# Ideally we should also allow less short function in a single line, but
# clang-format does not handle that.
AllowShortFunctionsOnASingleLine: false
AllowShortIfStatementsOnASingleLine: false
#
BinPackParameters: false
BinPackArguments: false
AllowAllParametersOfDeclarationOnNextLine: false
AlwaysBreakAfterReturnType: None

#
PenaltyReturnTypeOnItsOwnLine: 5000
PenaltyBreakAssignment: 5000
PenaltyBreakBeforeFirstCallParameter: 5000

# The coding style specifies some include order categories, but also tells to
# separate categories with an empty line. It does not specify the order within
# the categories. Since the SortInclude feature of clang-format does not
# re-order includes separated by empty lines, the feature is not used.
SortIncludes: false

# macros for which the opening brace stays attached.
ForEachMacros:   [ foreach, Q_FOREACH, BOOST_FOREACH, forever, Q_FOREVER, QBENCHMARK, QBENCHMARK_ONCE ]

# there are define by czy

PointerAlignment: Left
AlignOperands: true
BreakConstructorInitializersBeforeComma: true
SpacesBeforeTrailingComments: 2
AlignConsecutiveAssignments: true
AlignConsecutiveDeclarations: false
AlignTrailingComments: true
ReflowComments:  true
SpacesInSquareBrackets: true
SpacesInAngles: true

KeepEmptyLinesAtTheStartOfBlocks: true