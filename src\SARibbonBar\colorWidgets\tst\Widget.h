﻿#ifndef WIDGET_H
#define WIDGET_H

#include <QWidget>

QT_BEGIN_NAMESPACE
namespace Ui
{
class Widget;
}
QT_END_NAMESPACE

class Widget : public QWidget
{
    Q_OBJECT

public:
    Widget(QWidget* parent = nullptr);
    ~Widget();
    QColor makeRandColor() const;
    //
    void setColor(const QColor& clr);
private Q_SLOTS:
    void onGridColorWidgetClicked(const QColor& c);

private:
    Ui::Widget* ui;
};
#endif  // WIDGET_H
