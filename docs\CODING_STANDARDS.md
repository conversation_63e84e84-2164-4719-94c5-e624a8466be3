# OptimizeApplication 编码规范

## 1. 总体原则

### 1.1 代码风格
- 保持代码简洁、清晰、可读
- 遵循一致的命名约定和格式化规则
- 优先使用现代C++特性（C++17）
- 遵循RAII原则和异常安全性

### 1.2 设计原则
- 单一职责原则（SRP）
- 开闭原则（OCP）
- 依赖倒置原则（DIP）
- 接口隔离原则（ISP）
- 最小知识原则（LoD）

## 2. 命名约定

### 2.1 类名
- 使用大驼峰命名法（PascalCase）
- 接口类以`I`开头
- 抽象基类以`Base`结尾

```cpp
class ConfigManager;
class IFileParser;
class ParameterWidgetBase;
```

### 2.2 方法和函数名
- 使用小驼峰命名法（camelCase）
- 动词开头，表达清晰的动作

```cpp
void initialize();
bool loadConfiguration();
QString getFileName() const;
void setTheme(const QString& themeName);
```

### 2.3 变量名
- 使用小驼峰命名法（camelCase）
- 成员变量使用`m_`前缀
- 静态变量使用`s_`前缀
- 常量使用`k`前缀

```cpp
class Example {
private:
    QString m_fileName;          // 成员变量
    static int s_instanceCount;  // 静态变量
    static const int kMaxSize;   // 常量
};

void function() {
    int localVariable;           // 局部变量
}
```

### 2.4 宏定义
- 全部大写，使用下划线分隔

```cpp
#define MAX_BUFFER_SIZE 1024
#define HANDLE_ERROR(message) ErrorHandler::handleError(message)
```

## 3. 代码格式化

### 3.1 缩进
- 使用4个空格缩进，不使用制表符
- 访问修饰符不缩进

```cpp
class Example {
public:
    void publicMethod();
    
private:
    void privateMethod();
    int m_value;
};
```

### 3.2 大括号
- 大括号单独一行（Allman风格）
- 单行语句也使用大括号

```cpp
if (condition)
{
    doSomething();
}
else
{
    doSomethingElse();
}

for (int i = 0; i < count; ++i)
{
    processItem(i);
}
```

### 3.3 空格和空行
- 操作符前后加空格
- 逗号后加空格
- 函数间用空行分隔
- 逻辑块间用空行分隔

```cpp
int result = a + b * c;
function(param1, param2, param3);

void function1()
{
    // 实现
}

void function2()
{
    // 实现
}
```

## 4. 注释规范

### 4.1 文件头注释
```cpp
/**
 * @file ConfigManager.h
 * @brief 配置管理器类定义
 * <AUTHOR> Team
 * @date 2024-01-01
 * @version 1.0.0
 */
```

### 4.2 类注释
```cpp
/**
 * @brief 配置管理器类，负责应用程序配置的读取和保存
 * 
 * ConfigManager提供了一个统一的接口来访问应用程序配置，
 * 支持从文件加载配置和保存配置到文件。
 * 
 * @example
 * ConfigManager config;
 * config.setValue("theme", "dark");
 * QString theme = config.getValue("theme").toString();
 */
class ConfigManager
{
    // ...
};
```

### 4.3 方法注释
```cpp
/**
 * @brief 获取配置值
 * @param key 配置键
 * @param defaultValue 默认值，当键不存在时返回
 * @return 配置值
 * @throws std::invalid_argument 当键为空时抛出
 */
QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const;
```

### 4.4 行内注释
```cpp
int count = 0;  // 计数器
// TODO: 实现更高效的算法
// FIXME: 修复内存泄漏问题
// NOTE: 这里需要特别注意线程安全
```

## 5. 错误处理

### 5.1 异常处理
- 使用RAII模式管理资源
- 优先使用智能指针
- 在构造函数中抛出异常是安全的

```cpp
class ResourceManager
{
public:
    ResourceManager()
    {
        m_resource = std::make_unique<Resource>();
        if (!m_resource->initialize())
        {
            throw std::runtime_error("Failed to initialize resource");
        }
    }
    
private:
    std::unique_ptr<Resource> m_resource;
};
```

### 5.2 错误码处理
- 使用枚举类定义错误码
- 提供错误描述信息

```cpp
enum class ErrorCode
{
    Success = 0,
    FileNotFound,
    InvalidFormat,
    NetworkError
};

struct Result
{
    ErrorCode code;
    QString message;
    QVariant data;
};
```

## 6. 内存管理

### 6.1 智能指针使用
```cpp
// 优先使用智能指针
std::unique_ptr<ConfigManager> m_configManager;
std::shared_ptr<DataModel> m_sharedModel;
QScopedPointer<QFile> m_file;

// 避免裸指针
ConfigManager* manager = new ConfigManager();  // 不推荐
```

### 6.2 RAII模式
```cpp
class FileGuard
{
public:
    explicit FileGuard(QFile& file) : m_file(file) {}
    ~FileGuard()
    {
        if (m_file.isOpen())
        {
            m_file.close();
        }
    }
    
private:
    QFile& m_file;
    Q_DISABLE_COPY(FileGuard)
};
```

## 7. 性能优化

### 7.1 避免不必要的复制
```cpp
// 使用const引用传递参数
void processData(const std::vector<DataPoint>& data);

// 使用移动语义
void setData(std::vector<DataPoint>&& data)
{
    m_data = std::move(data);
}

// 预留容量
QVector<int> values;
values.reserve(expectedSize);
```

### 7.2 循环优化
```cpp
// 缓存容器大小
const int size = container.size();
for (int i = 0; i < size; ++i)
{
    // 处理
}

// 使用范围for循环
for (const auto& item : container)
{
    // 处理
}
```

## 8. Qt特定规范

### 8.1 信号和槽
```cpp
// 使用新的连接语法
connect(button, &QPushButton::clicked, this, &MainWindow::onButtonClicked);

// 避免旧的连接语法
connect(button, SIGNAL(clicked()), this, SLOT(onButtonClicked()));  // 不推荐
```

### 8.2 QObject子类
```cpp
class MyWidget : public QWidget
{
    Q_OBJECT
    
public:
    explicit MyWidget(QWidget* parent = nullptr);
    
private slots:
    void onButtonClicked();
    
private:
    Q_DISABLE_COPY(MyWidget)
};
```

## 9. 测试规范

### 9.1 单元测试
```cpp
class ConfigManagerTest : public QObject
{
    Q_OBJECT
    
private slots:
    void initTestCase();
    void cleanupTestCase();
    void testGetValue();
    void testSetValue();
    
private:
    ConfigManager* m_configManager;
};
```

### 9.2 测试命名
- 测试类名：`<ClassName>Test`
- 测试方法名：`test<MethodName>`
- 测试数据方法：`<testMethod>_data`

## 10. 文档规范

### 10.1 README文件
- 项目描述
- 构建说明
- 使用示例
- 贡献指南

### 10.2 API文档
- 使用Doxygen格式
- 包含参数说明
- 包含返回值说明
- 包含使用示例

## 11. 版本控制

### 11.1 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型：
- feat: 新功能
- fix: 修复
- docs: 文档
- style: 格式
- refactor: 重构
- test: 测试
- chore: 构建

### 11.2 分支命名
- feature/功能名称
- bugfix/问题描述
- hotfix/紧急修复
- release/版本号

## 12. 代码审查

### 12.1 审查清单
- [ ] 代码符合命名约定
- [ ] 代码格式正确
- [ ] 注释完整准确
- [ ] 错误处理适当
- [ ] 内存管理正确
- [ ] 性能考虑充分
- [ ] 测试覆盖充分

### 12.2 审查重点
- 逻辑正确性
- 边界条件处理
- 异常安全性
- 线程安全性
- 性能影响
