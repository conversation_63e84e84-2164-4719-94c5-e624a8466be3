#include "TubeWidget.h"
#include "ui_TubeWidget.h"
#include <QMessageBox>
#include <QComboBox>
#include "../utils/Logger.h"

TubeWidget::TubeWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::TubeWidget)
    , m_isUpdating(false)
{
    ui->setupUi(this);
    setupUI();
    setupConnections();
    setupComponentTypeCombo();
    LOG_INFO("TubeWidget created", "TubeWidget");
}

TubeWidget::TubeWidget(const JunctionComponent& junction, QWidget *parent)
    : TubeWidget(parent)
{
    setJunctionComponent(junction);
}

TubeWidget::~TubeWidget()
{
    delete ui;
    LOG_INFO("TubeWidget destroyed", "TubeWidget");
}

void TubeWidget::setupUI()
{
    // Additional UI setup that's not in the .ui file
    // All UI elements are already created by the .ui file
}

void TubeWidget::setupConnections()
{
    connect(ui->componentNameEdit, &QLineEdit::textChanged, this, &TubeWidget::onBasicDataChanged);
    connect(ui->fromComponentEdit, &QLineEdit::textChanged, this, &TubeWidget::onBasicDataChanged);
    connect(ui->toComponentEdit, &QLineEdit::textChanged, this, &TubeWidget::onBasicDataChanged);
    connect(ui->componentTypeCombo, QOverload<const QString &>::of(&QComboBox::currentTextChanged), this, &TubeWidget::onComponentTypeChanged);
    
    connect(ui->areaSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &TubeWidget::onGeometryDataChanged);
    connect(ui->forwardLossSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &TubeWidget::onGeometryDataChanged);
    connect(ui->reverseLossSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &TubeWidget::onGeometryDataChanged);
    connect(ui->flagsSpin, QOverload<int>::of(&QSpinBox::valueChanged), this, &TubeWidget::onGeometryDataChanged);
    
    connect(ui->junctionControlEdit, &QLineEdit::textChanged, this, &TubeWidget::onControlFlagsChanged);
    connect(ui->criticalFlow1Spin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &TubeWidget::onControlFlagsChanged);
    connect(ui->criticalFlow2Spin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &TubeWidget::onControlFlagsChanged);
    
    connect(ui->velocityFlagSpin, QOverload<int>::of(&QSpinBox::valueChanged), this, &TubeWidget::onInitialConditionChanged);
    connect(ui->velocitySpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &TubeWidget::onInitialConditionChanged);
    connect(ui->interfaceVelocitySpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &TubeWidget::onInitialConditionChanged);
    connect(ui->qualitySpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &TubeWidget::onInitialConditionChanged);
    
    connect(ui->validateBtn, &QPushButton::clicked, this, [this]() {
        if (validateData()) {
            QMessageBox::information(this, "Validation", "All data is valid!");
        }
    });
    connect(ui->generateCardsBtn, &QPushButton::clicked, this, [this]() {
        QString cards = generateRELAP5Cards();
        QMessageBox::information(this, "RELAP5 Cards", cards);
    });
    connect(ui->resetBtn, &QPushButton::clicked, this, &TubeWidget::resetToDefaults);
}

void TubeWidget::setJunctionComponent(const JunctionComponent& junction)
{
    m_junctionComponent = junction;
    updateDisplay();
}

JunctionComponent TubeWidget::getJunctionComponent() const
{
    return m_junctionComponent;
}

void TubeWidget::updateDisplay()
{
    if (m_isUpdating) return;
    m_isUpdating = true;
    
    // Update basic information
    ui->componentIdEdit->setText(m_junctionComponent.componentId);
    ui->componentNameEdit->setText(m_junctionComponent.componentName);
    ui->fromComponentEdit->setText(m_junctionComponent.fromComponent);
    ui->toComponentEdit->setText(m_junctionComponent.toComponent);
    ui->componentTypeCombo->setCurrentText(m_junctionComponent.componentType);
    
    // Update geometry data
    ui->areaSpin->setValue(m_junctionComponent.area);
    ui->forwardLossSpin->setValue(m_junctionComponent.forwardLoss);
    ui->reverseLossSpin->setValue(m_junctionComponent.reverseLoss);
    ui->flagsSpin->setValue(m_junctionComponent.flags);
    
    // Update control flags
    updateControlFlags();
    
    // Update initial conditions
    ui->velocityFlagSpin->setValue(m_junctionComponent.velocityFlag);
    ui->velocitySpin->setValue(m_junctionComponent.velocity);
    ui->interfaceVelocitySpin->setValue(m_junctionComponent.interfaceVelocity);
    ui->qualitySpin->setValue(m_junctionComponent.quality);
    
    // Update fields based on component type
    updateFieldsForComponentType();
    
    m_isUpdating = false;
}

void TubeWidget::clear()
{
    m_junctionComponent = JunctionComponent();
    updateDisplay();
}

void TubeWidget::onBasicDataChanged()
{
    m_junctionComponent.componentName = ui->componentNameEdit->text();
    m_junctionComponent.fromComponent = ui->fromComponentEdit->text();
    m_junctionComponent.toComponent = ui->toComponentEdit->text();
    
    emit dataChanged();
    emit componentModified(m_junctionComponent.componentId);
}

void TubeWidget::onGeometryDataChanged()
{
    m_junctionComponent.area = ui->areaSpin->value();
    m_junctionComponent.forwardLoss = ui->forwardLossSpin->value();
    m_junctionComponent.reverseLoss = ui->reverseLossSpin->value();
    m_junctionComponent.flags = ui->flagsSpin->value();
    
    emit dataChanged();
    emit componentModified(m_junctionComponent.componentId);
}

void TubeWidget::onInitialConditionChanged()
{
    if (m_isUpdating) return;
    
    m_junctionComponent.velocityFlag = ui->velocityFlagSpin->value();
    m_junctionComponent.velocity = ui->velocitySpin->value();
    m_junctionComponent.interfaceVelocity = ui->interfaceVelocitySpin->value();
    m_junctionComponent.quality = ui->qualitySpin->value();
    
    emit dataChanged();
    emit componentModified(m_junctionComponent.componentId);
}

void TubeWidget::onControlFlagsChanged()
{
    if (m_isUpdating) return;
    
    // 这里可以添加控制标记变化的处理逻辑
    emit dataChanged();
    emit componentModified(m_junctionComponent.componentId);
}

void TubeWidget::onComponentTypeChanged()
{
    if (m_isUpdating) return;
    
    m_junctionComponent.componentType = ui->componentTypeCombo->currentText();
    updateFieldsForComponentType();
    
    emit dataChanged();
    emit componentModified(m_junctionComponent.componentId);
}

void TubeWidget::resetToDefaults()
{
    fillWithDefaults();
    updateDisplay();
    emit dataChanged();
}

void TubeWidget::updateControlFlags()
{
    // 设置默认的控制标记
    ui->junctionControlEdit->setText("00000000");
    ui->criticalFlow1Spin->setValue(1.0);
    ui->criticalFlow2Spin->setValue(1.0);
}

void TubeWidget::setupComponentTypeCombo()
{
    // 组件类型已在UI文件中定义，这里可以添加额外的设置
}

void TubeWidget::updateFieldsForComponentType()
{
    QString type = ui->componentTypeCombo->currentText();
    
    // 根据组件类型调整界面
    if (type == "valve") {
        // 阀门特殊设置
        ui->forwardLossSpin->setEnabled(true);
        ui->reverseLossSpin->setEnabled(true);
    } else if (type == "pump") {
        // 泵特殊设置
        ui->forwardLossSpin->setEnabled(false);
        ui->reverseLossSpin->setEnabled(false);
    } else {
        // 默认设置
        ui->forwardLossSpin->setEnabled(true);
        ui->reverseLossSpin->setEnabled(true);
    }
}

bool TubeWidget::validateData()
{
    QString errorMsg;
    
    // 验证基本数据
    if (m_junctionComponent.componentId.isEmpty()) {
        emit validationError("Component ID cannot be empty");
        return false;
    }
    
    if (m_junctionComponent.fromComponent.isEmpty()) {
        emit validationError("From component cannot be empty");
        return false;
    }
    
    if (m_junctionComponent.toComponent.isEmpty()) {
        emit validationError("To component cannot be empty");
        return false;
    }
    
    // 验证几何数据
    if (!validateGeometryData(errorMsg)) {
        emit validationError(errorMsg);
        return false;
    }
    
    // 验证初始条件
    if (!validateInitialConditions(errorMsg)) {
        emit validationError(errorMsg);
        return false;
    }
    
    return true;
}

bool TubeWidget::validateGeometryData(QString& errorMsg)
{
    if (m_junctionComponent.area <= 0) {
        errorMsg = "Area must be greater than 0";
        return false;
    }
    
    if (m_junctionComponent.forwardLoss < 0) {
        errorMsg = "Forward loss coefficient cannot be negative";
        return false;
    }
    
    if (m_junctionComponent.reverseLoss < 0) {
        errorMsg = "Reverse loss coefficient cannot be negative";
        return false;
    }
    
    return true;
}

bool TubeWidget::validateInitialConditions(QString& errorMsg)
{
    // 根据velocityFlag验证初始条件
    if (m_junctionComponent.velocityFlag == 1) {
        // 质量流量模式，检查质量流量的合理性
        // 这里可以添加更多的验证逻辑
    }
    
    return true;
}

QString TubeWidget::generateRELAP5Cards() const
{
    QString cards;
    QString baseId = m_junctionComponent.componentId.left(6);
    
    // 组件定义卡
    cards += QString("%1000 \"%2\" %3\n")
             .arg(baseId + "0")
             .arg(m_junctionComponent.componentName)
             .arg(m_junctionComponent.componentType);
    
    // 几何数据卡
    cards += QString("%1101 %2 %3 %4 %5 %6 %7 %8 %9\n")
             .arg(baseId + "0")
             .arg(m_junctionComponent.fromComponent)
             .arg(m_junctionComponent.toComponent)
             .arg(m_junctionComponent.area, 0, 'g', 6)
             .arg(m_junctionComponent.forwardLoss, 0, 'g', 6)
             .arg(m_junctionComponent.reverseLoss, 0, 'g', 6)
             .arg(ui->junctionControlEdit->text())
             .arg(ui->criticalFlow1Spin->value(), 0, 'g', 4)
             .arg(ui->criticalFlow2Spin->value(), 0, 'g', 4);
    
    // 初始条件卡
    cards += QString("%1201 %2 %3 %4 %5\n")
             .arg(baseId + "0")
             .arg(m_junctionComponent.velocityFlag)
             .arg(m_junctionComponent.velocity, 0, 'g', 6)
             .arg(m_junctionComponent.interfaceVelocity, 0, 'g', 6)
             .arg(m_junctionComponent.quality, 0, 'g', 6);
    
    // 如果是阀门，添加阀门特殊卡片
    if (m_junctionComponent.componentType == "valve") {
        cards += QString("%1300 trpvlv\n").arg(baseId + "0");
        cards += QString("%1301 689\n").arg(baseId + "0"); // 默认trip卡号
    }
    
    return cards;
}

void TubeWidget::fillWithDefaults()
{
    m_junctionComponent.area = 0.00012469;
    m_junctionComponent.forwardLoss = 0.0;
    m_junctionComponent.reverseLoss = 0.0;
    m_junctionComponent.flags = 0;
    m_junctionComponent.velocityFlag = 1;
    m_junctionComponent.velocity = 0.0;
    m_junctionComponent.interfaceVelocity = 0.0;
    m_junctionComponent.quality = 0.0;
}

void TubeWidget::loadFromRELAP5Cards(const QStringList& cards)
{
    // 实现从RELAP5卡片加载数据的功能
    // 这里可以添加具体的解析逻辑
}

void TubeWidget::validateAndHighlightErrors()
{
    // 实现数据验证和错误高亮显示
    // 这里可以添加具体的验证和高亮逻辑
} 