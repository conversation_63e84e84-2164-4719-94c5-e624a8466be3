#include "InputData.h"
#include <QDebug>
#include <QCoreApplication>

/**
 * @brief InputData使用示例
 * 
 * 这个示例展示了如何使用InputData类来加载和处理input目录中的文件
 */
void demonstrateInputDataUsage()
{
    qDebug() << "=== InputData 使用示例 ===";
    
    // 创建InputData实例
    InputData inputData;
    
    // 加载一个输入文件
    QString filePath = "input/NC_Welander.i";
    bool success = inputData.loadFromFile(filePath);
    
    if (!success) {
        qDebug() << "加载文件失败:" << filePath;
        return;
    }
    
    qDebug() << "成功加载文件:" << inputData.fileName;
    qDebug() << "文件类型:" << InputData::fileTypeToString(inputData.fileType);
    qDebug() << "问题标题:" << inputData.problemTitle;
    qDebug() << "最后修改时间:" << inputData.lastModified.toString();
    
    // 显示控制卡信息
    qDebug() << "\n--- 控制卡信息 ---";
    qDebug() << "问题编号:" << inputData.controlCard.problemNumber;
    qDebug() << "问题类型:" << inputData.controlCard.problemType;
    qDebug() << "分析类型:" << inputData.controlCard.analysisType;
    qDebug() << "结束时间:" << inputData.controlCard.endTime;
    qDebug() << "最小时间步长:" << inputData.controlCard.minTimeStep;
    qDebug() << "最大时间步长:" << inputData.controlCard.maxTimeStep;
    
    // 显示单位系统信息
    qDebug() << "\n--- 单位系统 ---";
    qDebug() << "输入单位:" << inputData.unitSystem.inputUnits;
    qDebug() << "输出单位:" << inputData.unitSystem.outputUnits;
    qDebug() << "工作流体:" << inputData.unitSystem.workingFluid;
    qDebug() << "比例因子:" << inputData.unitSystem.scaleFactor;
    
    // 显示绘图变量
    qDebug() << "\n--- 绘图变量 ---";
    qDebug() << "绘图变量数量:" << inputData.plotVariables.size();
    for (const auto& var : inputData.plotVariables) {
        qDebug() << QString("ID: %1, 类型: %2, 组件: %3")
                    .arg(var.variableId)
                    .arg(var.variableType)
                    .arg(var.componentId);
    }
    
    // 显示小编辑变量
    qDebug() << "\n--- 小编辑变量 ---";
    qDebug() << "小编辑变量数量:" << inputData.minorEditVars.size();
    for (const auto& var : inputData.minorEditVars) {
        qDebug() << QString("ID: %1, 类型: %2, 组件: %3")
                    .arg(var.editId)
                    .arg(var.variableType)
                    .arg(var.componentId);
    }
    
    // 显示几何组件统计
    qDebug() << "\n--- 几何组件统计 ---";
    qDebug() << "总组件数量:" << inputData.getTotalComponents();
    qDebug() << "管道数量:" << inputData.getComponentCount("pipe");
    qDebug() << "分支数量:" << inputData.getComponentCount("branch");
    qDebug() << "单体积数量:" << inputData.getComponentCount("snglvol");
    qDebug() << "连接数量:" << inputData.getComponentCount("sngljun");
    
    // 显示组件类型
    QStringList types = inputData.getComponentTypes();
    qDebug() << "组件类型:" << types;
    
    // 显示管道组件详情
    if (!inputData.pipes.isEmpty()) {
        qDebug() << "\n--- 管道组件 ---";
        for (const auto& pipe : inputData.pipes) {
            qDebug() << QString("管道 %1 (%2): %3")
                        .arg(pipe.componentId)
                        .arg(pipe.componentName)
                        .arg(pipe.componentType);
        }
    }
    
    // 显示分支组件详情
    if (!inputData.branches.isEmpty()) {
        qDebug() << "\n--- 分支组件 ---";
        for (const auto& branch : inputData.branches) {
            qDebug() << QString("分支 %1 (%2): %3")
                        .arg(branch.componentId)
                        .arg(branch.componentName)
                        .arg(branch.componentType);
        }
    }
    
    // 显示单体积组件详情
    if (!inputData.volumes.isEmpty()) {
        qDebug() << "\n--- 单体积组件 ---";
        for (const auto& volume : inputData.volumes) {
            qDebug() << QString("单体积 %1 (%2): %3")
                        .arg(volume.componentId)
                        .arg(volume.componentName)
                        .arg(volume.componentType);
        }
    }
    
    // 显示连接组件详情
    if (!inputData.junctions.isEmpty()) {
        qDebug() << "\n--- 连接组件 ---";
        for (const auto& junction : inputData.junctions) {
            qDebug() << QString("连接 %1 (%2): %3")
                        .arg(junction.componentId)
                        .arg(junction.componentName)
                        .arg(junction.componentType);
        }
    }
    
    // 显示材料属性
    if (!inputData.materials.isEmpty()) {
        qDebug() << "\n--- 材料属性 ---";
        for (const auto& material : inputData.materials) {
            qDebug() << QString("材料 %1: %2")
                        .arg(material.materialId)
                        .arg(material.materialName);
        }
    }
    
    // 显示表格数据
    if (!inputData.tables.isEmpty()) {
        qDebug() << "\n--- 表格数据 ---";
        for (const auto& table : inputData.tables) {
            qDebug() << QString("表格 %1 (%2): %3 个数据点")
                        .arg(table.tableId)
                        .arg(table.tableType)
                        .arg(table.dataPoints.size());
        }
    }
    
    // 显示注释统计
    qDebug() << "\n--- 注释信息 ---";
    qDebug() << "注释行数:" << inputData.comments.size();
    qDebug() << "总行数:" << inputData.rawLines.size();
    
    // 验证数据
    qDebug() << "\n--- 数据验证 ---";
    qDebug() << "数据有效性:" << (inputData.isValid() ? "有效" : "无效");
    
    QStringList errors = inputData.validateData();
    if (!errors.isEmpty()) {
        qDebug() << "验证错误:";
        for (const QString& error : errors) {
            qDebug() << "  -" << error;
        }
    } else {
        qDebug() << "数据验证通过";
    }
    
    // 查找特定组件
    qDebug() << "\n--- 组件查找示例 ---";
    GeometryComponent* component = inputData.findComponent("1000000");
    if (component) {
        qDebug() << QString("找到组件 %1: %2 (%3)")
                    .arg(component->componentId)
                    .arg(component->componentName)
                    .arg(component->componentType);
    } else {
        qDebug() << "未找到组件 1000000";
    }
    
    // 按类型获取组件
    QVector<GeometryComponent*> pipes = inputData.getComponentsByType("pipe");
    qDebug() << QString("按类型查找管道组件: %1 个").arg(pipes.size());
    
    qDebug() << "\n=== 示例结束 ===";
}

/**
 * @brief 批量处理多个输入文件的示例
 */
void demonstrateBatchProcessing()
{
    qDebug() << "\n=== 批量处理示例 ===";
    
    QStringList inputFiles = {
        "input/NC_Welander.i",
        "input/Discharge_Air.i",
        "input/Discharge_Water.i",
        "input/1D_Wave.i"
    };
    
    for (const QString& filePath : inputFiles) {
        InputData inputData;
        if (inputData.loadFromFile(filePath)) {
            qDebug() << QString("文件: %1 | 类型: %2 | 组件数: %3")
                        .arg(inputData.fileName)
                        .arg(InputData::fileTypeToString(inputData.fileType))
                        .arg(inputData.getTotalComponents());
        } else {
            qDebug() << "加载失败:" << filePath;
        }
    }
    
    qDebug() << "=== 批量处理结束 ===";
}

/**
 * @brief 主函数 - 运行所有示例
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 运行基本使用示例
    demonstrateInputDataUsage();
    
    // 运行批量处理示例
    demonstrateBatchProcessing();
    
    return 0;
} 