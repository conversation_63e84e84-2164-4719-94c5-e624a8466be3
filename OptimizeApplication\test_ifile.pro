QT += core
QT -= gui

CONFIG += c++11 console
CONFIG -= app_bundle

TARGET = test_ifile
TEMPLATE = app

SOURCES += \
    src/data/InputData.cpp \
    src/data/IFileExample.cpp

HEADERS += \
    src/data/InputData.h

# 设置输出目录
DESTDIR = bin

# 包含路径
INCLUDEPATH += src/data

# 定义
DEFINES += QT_DEPRECATED_WARNINGS

# 创建输出目录
win32 {
    QMAKE_POST_LINK += if not exist \"$$PWD/output\" mkdir \"$$PWD/output\" $$escape_expand(\\n\\t)
    QMAKE_POST_LINK += if not exist \"$$PWD/output/batch\" mkdir \"$$PWD/output/batch\"
}

unix {
    QMAKE_POST_LINK += mkdir -p $$PWD/output/batch
} 