#include "PerformanceProfiler.h"
#include <QDebug>
#include <QMutexLocker>
#include <algorithm>

PerformanceProfiler& PerformanceProfiler::instance()
{
    static PerformanceProfiler profiler;
    return profiler;
}

PerformanceProfiler::PerformanceProfiler()
    : m_enabled(true)
{
}

PerformanceProfiler::~PerformanceProfiler()
{
    if (m_enabled) {
        printReport();
    }
}

void PerformanceProfiler::startProfile(const QString& name)
{
    if (!m_enabled) {
        return;
    }

    QMutexLocker locker(&m_mutex);
    m_startTimes[name] = std::chrono::high_resolution_clock::now();
}

void PerformanceProfiler::endProfile(const QString& name)
{
    if (!m_enabled) {
        return;
    }

    auto endTime = std::chrono::high_resolution_clock::now();

    QMutexLocker locker(&m_mutex);

    if (!m_startTimes.contains(name)) {
        qWarning() << "Profile" << name << "was not started";
        return;
    }

    auto startTime = m_startTimes[name];
    m_startTimes.remove(name);

    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
    qint64 elapsedMicroseconds = duration.count();

    ProfileInfo& info = m_profiles[name];
    info.name = name;
    info.totalTime += elapsedMicroseconds;
    info.callCount++;
    info.minTime = std::min(info.minTime, elapsedMicroseconds);
    info.maxTime = std::max(info.maxTime, elapsedMicroseconds);
    info.avgTime = info.totalTime / info.callCount;
}

PerformanceProfiler::ProfileInfo PerformanceProfiler::getProfileInfo(const QString& name) const
{
    QMutexLocker locker(&m_mutex);
    return m_profiles.value(name, ProfileInfo());
}

QMap<QString, PerformanceProfiler::ProfileInfo> PerformanceProfiler::getAllProfileInfo() const
{
    QMutexLocker locker(&m_mutex);
    return m_profiles;
}

void PerformanceProfiler::clearProfiles()
{
    QMutexLocker locker(&m_mutex);
    m_profiles.clear();
    m_startTimes.clear();
}

void PerformanceProfiler::printReport() const
{
    QMutexLocker locker(&m_mutex);

    if (m_profiles.isEmpty()) {
        qDebug() << "No performance data available";
        return;
    }

    qDebug() << "Performance Report:";
    qDebug() << "==================";
    qDebug() << QString("%1 %2 %3 %4 %5 %6")
                .arg("Function", -30)
                .arg("Calls", 8)
                .arg("Total(μs)", 12)
                .arg("Avg(μs)", 10)
                .arg("Min(μs)", 10)
                .arg("Max(μs)", 10);
    qDebug() << QString("-").repeated(90);

    // 按总时间排序
    QList<ProfileInfo> sortedProfiles = m_profiles.values();
    std::sort(sortedProfiles.begin(), sortedProfiles.end(),
              [](const ProfileInfo& a, const ProfileInfo& b) {
                  return a.totalTime > b.totalTime;
              });

    for (const ProfileInfo& info : sortedProfiles) {
        qDebug() << QString("%1 %2 %3 %4 %5 %6")
                    .arg(info.name, -30)
                    .arg(info.callCount, 8)
                    .arg(info.totalTime, 12)
                    .arg(info.avgTime, 10)
                    .arg(info.minTime, 10)
                    .arg(info.maxTime, 10);
    }

    qDebug() << QString("-").repeated(90);

    // 计算总统计信息
    qint64 totalTime = 0;
    int totalCalls = 0;
    for (const ProfileInfo& info : sortedProfiles) {
        totalTime += info.totalTime;
        totalCalls += info.callCount;
    }

    qDebug() << QString("Total: %1 functions, %2 calls, %3 μs")
                .arg(sortedProfiles.size())
                .arg(totalCalls)
                .arg(totalTime);
}

void PerformanceProfiler::setEnabled(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_enabled = enabled;
}

bool PerformanceProfiler::isEnabled() const
{
    QMutexLocker locker(&m_mutex);
    return m_enabled;
}

// ScopedProfiler实现
ScopedProfiler::ScopedProfiler(const QString& name)
    : m_name(name)
{
    PerformanceProfiler::instance().startProfile(m_name);
}

ScopedProfiler::~ScopedProfiler()
{
    PerformanceProfiler::instance().endProfile(m_name);
}
