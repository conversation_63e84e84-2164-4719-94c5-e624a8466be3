# OptimizeApplication

一个基于Qt的现代化优化应用程序，采用模块化架构设计，支持多种优化算法和可扩展的插件系统。

## 项目概述

OptimizeApplication是一个专业的优化工具，提供了直观的用户界面和强大的后端处理能力。该应用程序经过全面重构，采用现代C++17标准和最佳实践，具有高度的可维护性和可扩展性。

### 主要特性

- 🎨 **现代化UI**: 基于SARibbonBar的Ribbon界面，提供直观的用户体验
- 🏗️ **模块化架构**: 清晰的分层架构，支持依赖注入和服务定位
- 🔌 **插件系统**: 支持动态加载插件，轻松扩展功能
- 🎯 **多种优化算法**: 支持梯度下降、遗传算法、粒子群优化等
- 📊 **数据可视化**: 集成Qt Charts，提供丰富的图表展示
- 🌙 **主题系统**: 支持亮色、暗色和自定义主题
- 📝 **日志系统**: 完整的日志记录和错误处理机制
- 🧪 **测试覆盖**: 完善的单元测试和集成测试
- 🚀 **性能优化**: 内置性能分析器和内存管理工具

## 系统要求

### 最低要求
- **操作系统**: Windows 10, macOS 10.14, Ubuntu 18.04 或更高版本
- **Qt版本**: Qt 5.14 或更高版本
- **编译器**:
  - Windows: Visual Studio 2017 或 MinGW 8.1
  - macOS: Xcode 10 或更高版本
  - Linux: GCC 7.3 或 Clang 6.0
- **CMake**: 3.14 或更高版本（可选）
- **内存**: 最少 4GB RAM
- **存储**: 最少 500MB 可用空间

### 推荐配置
- **内存**: 8GB RAM 或更多
- **处理器**: 多核处理器
- **显卡**: 支持OpenGL 3.3的显卡

## 快速开始

### 1. 克隆仓库

```bash
git clone https://github.com/your-org/OptimizeApplication.git
cd OptimizeApplication
```

### 2. 构建项目

#### 使用CMake（推荐）

```bash
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

#### 使用qmake

```bash
mkdir build
cd build
qmake ../NewOptimizeApplication.pro CONFIG+=release
make  # 或在Windows上使用 nmake/mingw32-make
```

#### 使用构建脚本（Windows）

```batch
scripts\build.bat --release --cmake
```

### 3. 运行应用程序

```bash
# CMake构建
./bin/OptimizeApplication

# qmake构建
./bin/release/OptimizeApplication
```

## 项目结构

```
OptimizeApplication/
├── 3rdparty/                    # 第三方库
│   └── SARibbonBar/             # SARibbonBar库
├── src/                         # 源代码
│   ├── app/                     # 应用程序入口
│   ├── core/                    # 核心功能
│   │   ├── application/         # 应用程序核心
│   │   ├── config/              # 配置管理
│   │   └── theme/               # 主题管理
│   ├── data/                    # 数据层
│   │   ├── models/              # 数据模型
│   │   ├── parsers/             # 数据解析器
│   │   └── repositories/        # 数据仓库
│   ├── ui/                      # 用户界面
│   │   ├── dialogs/             # 对话框
│   │   ├── mainwindow/          # 主窗口
│   │   └── widgets/             # 自定义控件
│   └── utils/                   # 工具类
│       ├── logging/             # 日志系统
│       └── common/              # 通用工具
├── include/                     # 公共头文件
├── resources/                   # 资源文件
├── tests/                       # 测试代码
│   ├── unit/                    # 单元测试
│   └── integration/             # 集成测试
├── docs/                        # 文档
├── scripts/                     # 构建和部署脚本
├── CMakeLists.txt               # CMake配置文件
├── NewOptimizeApplication.pro   # qmake配置文件
└── README.md                    # 项目说明
```

## 重构成果

本项目已完成全面重构，主要改进包括：

### ✅ 已完成的重构任务

1. **代码重构规划和分析** - 制定了详细的重构计划和架构设计
2. **项目结构重组** - 重新组织了项目目录结构，分离了第三方库、核心业务逻辑、UI组件等
3. **核心架构重构** - 实现了ApplicationCore、ServiceLocator、EventBus、PluginManager等核心组件
4. **UI组件模块化** - 重构了UI组件，实现了RibbonManager、WorkspaceManager等管理器
5. **数据层重构** - 实现了BaseModel、IRepository、FileRepository等数据访问层
6. **构建系统优化** - 优化了CMake和qmake配置，提供了自动化构建脚本
7. **代码质量改进** - 实现了ErrorHandler、MemoryUtils、PerformanceProfiler等工具
8. **文档和测试完善** - 添加了完整的单元测试、集成测试和项目文档

### 🏗️ 核心架构特性

- **模块化设计**: 清晰的分层架构，每个模块职责单一
- **依赖注入**: 使用ServiceLocator管理服务依赖
- **事件驱动**: 基于EventBus的松耦合通信机制
- **插件支持**: 动态加载插件，支持功能扩展
- **配置管理**: 统一的配置管理和持久化
- **主题系统**: 支持多种主题和自定义样式
- **错误处理**: 统一的错误处理和异常管理
- **性能监控**: 内置性能分析和内存管理工具

## 开发指南

### 编码规范

请参阅 [编码规范文档](docs/CODING_STANDARDS.md) 了解详细的编码标准和最佳实践。

### 核心组件使用

#### ApplicationCore
```cpp
ApplicationCore* core = ApplicationCore::instance();
core->initialize();

// 访问服务
ConfigManager* config = core->configManager();
ThemeManager* theme = core->themeManager();
LogManager* logger = core->logManager();
```

#### 配置管理
```cpp
ConfigManager* config = ApplicationCore::instance()->configManager();
config->setValue("theme", "dark");
QString theme = config->getValue("theme", "light").toString();
config->save();
```

#### 事件系统
```cpp
EventBus* eventBus = ApplicationCore::instance()->eventBus();
eventBus->subscribe<MyEvent>(this, &MyClass::handleMyEvent);
MyEvent event("data");
eventBus->publish(event);
```

#### 数据模型
```cpp
OptimizationParameters params;
params.setMethod(OptimizationParameters::Method::GradientDescent);
params.setObjective("minimize(x^2 + y^2)");
QVariant data = params.toVariant();
```

## 测试

### 运行测试

```bash
# 运行所有测试
ctest

# 运行特定测试
./tests/unit/ConfigManagerTest
./tests/integration/ApplicationIntegrationTest
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页: https://github.com/your-org/OptimizeApplication
- 问题反馈: https://github.com/your-org/OptimizeApplication/issues

## 致谢

- [Qt Framework](https://www.qt.io/) - 跨平台应用程序框架
- [SARibbonBar](https://github.com/czyt1988/SARibbon) - Ribbon界面库
- 所有贡献者和测试人员