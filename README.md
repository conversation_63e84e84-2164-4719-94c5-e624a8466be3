# Optimize Application

A comprehensive application for optimization, sensitivity analysis, and uncertainty quantification.

## Features

### Core Architecture
- **ThemeManager**: Handles application theming with light/dark themes
- **ApplicationCore**: Manages core application functionality
- **ConfigManager**: Handles application configuration
- **Logger**: Provides logging capabilities

### Data Handling
- **InputData**: Handles complex data structures and file operations
- **IFileExample**: Demonstrates file processing examples

### UI Components
- **SARibbonBar**: Implemented modern ribbon interface for all functionality
- **CustomTreeWidget**: Hierarchical data display with drag-drop and context menus
- **ChartWidget**: Data visualization with multiple chart types
- **RibbonOptimizeWidget**: Specialized ribbon interface for optimization tasks
- **SensitivityWidget**: Interface for sensitivity analysis

## Recent Improvements
- Added SARibbonBar integration for a modern Office-like interface
- Implemented RibbonOptimizeWidget for optimization tasks
- Added SensitivityWidget for sensitivity analysis
- Fixed missing implementation files (Common, Utils, Logger, etc.)
- Updated build script to properly set up Visual Studio environment
- Added theme switching capability with light and dark themes

## Optimization Methods
- Gradient Descent
- Newton Method
- Quasi-Newton Method
- Genetic Algorithm
- Particle Swarm Optimization

## Sensitivity Analysis Methods
- Local Sensitivity Analysis
- Global Sensitivity Analysis
- Sobol Indices
- Morris Method

## Uncertainty Quantification Methods
- Monte Carlo Simulation
- Latin Hypercube Sampling
- Quasi-Monte Carlo
- Polynomial Chaos Expansion

## Building the Application

### Prerequisites
- Qt 5.14.2 or later
- Visual Studio 2017 or later (on Windows)
- C++17 compatible compiler

### Build Steps
1. Clone the repository
2. Open the project in Qt Creator or Visual Studio with Qt tools
3. Build the project

### Manual Build (Windows)
```
cd path/to/project
qmake
nmake (or jom)
```

### Manual Build (Linux/macOS)
```
cd path/to/project
qmake
make
```

## Project Structure

```
OptimizeApplication/
├── bin/                  # Output directory for binaries
├── build/                # Build files
├── resources/            # Application resources
│   └── icons/            # Application icons
├── src/                  # Source code
│   ├── core/             # Core application classes
│   ├── data/             # Data handling classes
│   ├── SARibbonBar/      # Ribbon interface implementation
│   ├── ui/               # UI files
│   ├── utils/            # Utility classes
│   └── widgets/          # Custom widgets
└── OptimizeApplication.pro  # Project file
```

## License
Copyright © 2024 Optimize Solutions. All rights reserved. 