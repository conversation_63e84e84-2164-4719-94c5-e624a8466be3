#ifndef LOGGER_H
#define LOGGER_H

#include <QString>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QMutex>
#include <QDebug>

/**
 * @brief The Logger class provides logging functionality for the application
 */
class Logger
{
public:
    /**
     * @brief LogLevel defines the severity of log messages
     */
    enum LogLevel {
        Debug,
        Info,
        Warning,
        Error,
        Critical
    };

    /**
     * @brief Initializes the logger
     * @param logFilePath Path to the log file
     * @return True if initialization was successful, false otherwise
     */
    static bool initialize(const QString &logFilePath = "application.log");

    /**
     * @brief Logs a debug message
     * @param message The message to log
     * @param source The source of the message (e.g., class name, function)
     */
    static void debug(const QString &message, const QString &source = QString());

    /**
     * @brief Logs an informational message
     * @param message The message to log
     * @param source The source of the message (e.g., class name, function)
     */
    static void info(const QString &message, const QString &source = QString());

    /**
     * @brief Logs a warning message
     * @param message The message to log
     * @param source The source of the message (e.g., class name, function)
     */
    static void warning(const QString &message, const QString &source = QString());

    /**
     * @brief Logs an error message
     * @param message The message to log
     * @param source The source of the message (e.g., class name, function)
     */
    static void error(const QString &message, const QString &source = QString());

    /**
     * @brief Logs a critical error message
     * @param message The message to log
     * @param source The source of the message (e.g., class name, function)
     */
    static void critical(const QString &message, const QString &source = QString());

private:
    /**
     * @brief Private constructor (singleton pattern)
     */
    Logger();

    /**
     * @brief Logs a message with the specified log level
     * @param level The log level
     * @param message The message to log
     * @param source The source of the message
     */
    static void log(LogLevel level, const QString &message, const QString &source);

    /**
     * @brief Gets the string representation of a log level
     * @param level The log level
     * @return String representation of the log level
     */
    static QString levelToString(LogLevel level);

    static QFile logFile;        ///< Log file
    static QTextStream logStream; ///< Text stream for writing to the log file
    static QMutex mutex;         ///< Mutex for thread safety
    static bool initialized;     ///< Flag indicating if the logger is initialized
};

// Convenience macros for logging
#define LOG_DEBUG(message, source) Logger::debug(message, source)
#define LOG_INFO(message, source) Logger::info(message, source)
#define LOG_WARNING(message, source) Logger::warning(message, source)
#define LOG_ERROR(message, source) Logger::error(message, source)
#define LOG_CRITICAL(message, source) Logger::critical(message, source)

#endif // LOGGER_H 