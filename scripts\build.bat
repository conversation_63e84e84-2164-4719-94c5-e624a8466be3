@echo off
setlocal enabledelayedexpansion

:: 构建脚本 - OptimizeApplication
:: 支持多种构建配置和编译器

echo ========================================
echo OptimizeApplication 构建脚本
echo ========================================

:: 设置默认值
set BUILD_TYPE=Release
set GENERATOR=
set CLEAN=0
set VERBOSE=0
set PARALLEL_JOBS=4
set QT_DIR=
set CMAKE_BUILD=0
set QMAKE_BUILD=1

:: 解析命令行参数
:parse_args
if "%1"=="" goto :done_args
if /i "%1"=="--debug" set BUILD_TYPE=Debug
if /i "%1"=="--release" set BUILD_TYPE=Release
if /i "%1"=="--clean" set CLEAN=1
if /i "%1"=="--verbose" set VERBOSE=1
if /i "%1"=="--cmake" (
    set CMAKE_BUILD=1
    set QMAKE_BUILD=0
)
if /i "%1"=="--qmake" (
    set CMAKE_BUILD=0
    set QMAKE_BUILD=1
)
if /i "%1"=="--jobs" (
    set PARALLEL_JOBS=%2
    shift
)
if /i "%1"=="--qt-dir" (
    set QT_DIR=%2
    shift
)
if /i "%1"=="--generator" (
    set GENERATOR=%2
    shift
)
if /i "%1"=="--help" goto :show_help
shift
goto :parse_args
:done_args

:: 显示帮助信息
:show_help
echo 用法: build.bat [选项]
echo.
echo 选项:
echo   --debug          构建调试版本
echo   --release        构建发布版本 (默认)
echo   --clean          清理构建目录
echo   --verbose        显示详细输出
echo   --cmake          使用CMake构建
echo   --qmake          使用qmake构建 (默认)
echo   --jobs N         并行构建任务数 (默认: 4)
echo   --qt-dir PATH    Qt安装目录
echo   --generator GEN  CMake生成器
echo   --help           显示此帮助信息
echo.
echo 示例:
echo   build.bat --debug --clean
echo   build.bat --release --cmake --jobs 8
echo   build.bat --qt-dir "C:\Qt\5.14.2\msvc2017_64"
exit /b 0

:: 检测Qt环境
if "%QT_DIR%"=="" (
    echo 检测Qt环境...
    where qmake >nul 2>nul
    if !ERRORLEVEL! neq 0 (
        echo 错误: 未找到qmake，请设置Qt环境或使用--qt-dir参数
        exit /b 1
    )
    for /f "tokens=*" %%i in ('where qmake') do set QMAKE_PATH=%%i
    for %%i in ("!QMAKE_PATH!") do set QT_DIR=%%~dpi
    set QT_DIR=!QT_DIR:~0,-5!
    echo Qt目录: !QT_DIR!
) else (
    set PATH=!QT_DIR!\bin;!PATH!
    echo 使用指定的Qt目录: !QT_DIR!
)

:: 检测编译器
if "%CMAKE_BUILD%"=="1" (
    echo 检测CMake...
    where cmake >nul 2>nul
    if !ERRORLEVEL! neq 0 (
        echo 错误: 未找到cmake
        exit /b 1
    )
    
    if "%GENERATOR%"=="" (
        where cl.exe >nul 2>nul
        if !ERRORLEVEL! equ 0 (
            echo 检测到Visual Studio编译器
            set GENERATOR=Visual Studio 16 2019
        ) else (
            where mingw32-make.exe >nul 2>nul
            if !ERRORLEVEL! equ 0 (
                echo 检测到MinGW编译器
                set GENERATOR=MinGW Makefiles
            ) else (
                echo 错误: 未找到支持的编译器
                exit /b 1
            )
        )
    )
    echo 使用生成器: !GENERATOR!
)

:: 设置构建目录
set BUILD_DIR=build
if "%CMAKE_BUILD%"=="1" (
    set BUILD_DIR=build-cmake
)

:: 清理构建目录
if "%CLEAN%"=="1" (
    echo 清理构建目录...
    if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
    if exist bin rmdir /s /q bin
)

:: 创建构建目录
if not exist %BUILD_DIR% mkdir %BUILD_DIR%

:: 开始构建
echo 开始构建...
echo 构建类型: %BUILD_TYPE%
echo 构建目录: %BUILD_DIR%

if "%CMAKE_BUILD%"=="1" (
    goto :cmake_build
) else (
    goto :qmake_build
)

:cmake_build
echo ========================================
echo 使用CMake构建
echo ========================================

cd %BUILD_DIR%

:: 配置CMake
echo 配置CMake...
if "%VERBOSE%"=="1" (
    cmake .. -G "%GENERATOR%" -DCMAKE_BUILD_TYPE=%BUILD_TYPE% -DCMAKE_PREFIX_PATH="%QT_DIR%"
) else (
    cmake .. -G "%GENERATOR%" -DCMAKE_BUILD_TYPE=%BUILD_TYPE% -DCMAKE_PREFIX_PATH="%QT_DIR%" >nul
)

if !ERRORLEVEL! neq 0 (
    echo 错误: CMake配置失败
    cd ..
    exit /b 1
)

:: 构建项目
echo 构建项目...
if "%VERBOSE%"=="1" (
    cmake --build . --config %BUILD_TYPE% --parallel %PARALLEL_JOBS%
) else (
    cmake --build . --config %BUILD_TYPE% --parallel %PARALLEL_JOBS% >nul
)

if !ERRORLEVEL! neq 0 (
    echo 错误: 构建失败
    cd ..
    exit /b 1
)

cd ..
goto :build_success

:qmake_build
echo ========================================
echo 使用qmake构建
echo ========================================

cd %BUILD_DIR%

:: 生成Makefile
echo 生成Makefile...
if "%VERBOSE%"=="1" (
    qmake ../NewOptimizeApplication.pro CONFIG+=%BUILD_TYPE%
) else (
    qmake ../NewOptimizeApplication.pro CONFIG+=%BUILD_TYPE% >nul
)

if !ERRORLEVEL! neq 0 (
    echo 错误: qmake失败
    cd ..
    exit /b 1
)

:: 构建项目
echo 构建项目...
where nmake >nul 2>nul
if !ERRORLEVEL! equ 0 (
    if "%VERBOSE%"=="1" (
        nmake
    ) else (
        nmake >nul
    )
) else (
    where mingw32-make >nul 2>nul
    if !ERRORLEVEL! equ 0 (
        if "%VERBOSE%"=="1" (
            mingw32-make -j%PARALLEL_JOBS%
        ) else (
            mingw32-make -j%PARALLEL_JOBS% >nul
        )
    ) else (
        if "%VERBOSE%"=="1" (
            make -j%PARALLEL_JOBS%
        ) else (
            make -j%PARALLEL_JOBS% >nul
        )
    )
)

if !ERRORLEVEL! neq 0 (
    echo 错误: 构建失败
    cd ..
    exit /b 1
)

cd ..
goto :build_success

:build_success
echo ========================================
echo 构建成功完成!
echo ========================================

:: 显示输出文件信息
if exist bin\debug\OptimizeApplication_debug.exe (
    echo 调试版本: bin\debug\OptimizeApplication_debug.exe
)
if exist bin\release\OptimizeApplication.exe (
    echo 发布版本: bin\release\OptimizeApplication.exe
)

echo.
echo 构建完成时间: %date% %time%
exit /b 0
