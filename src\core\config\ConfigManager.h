#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QSettings>
#include <QVariant>
#include <QString>
#include <QMap>
#include <QMutex>

/**
 * @brief 配置管理器类，负责应用程序配置的读取和保存
 * 
 * ConfigManager提供了一个统一的接口来访问应用程序配置，
 * 支持从文件加载配置和保存配置到文件。
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param configPath 配置文件路径，默认为空（使用默认路径）
     */
    explicit ConfigManager(const QString& configPath = QString());

    /**
     * @brief 析构函数
     */
    ~ConfigManager();

    /**
     * @brief 加载配置
     * @return 加载是否成功
     */
    bool load();

    /**
     * @brief 保存配置
     * @return 保存是否成功
     */
    bool save();

    /**
     * @brief 获取配置值
     * @param key 配置键
     * @param defaultValue 默认值，当键不存在时返回
     * @return 配置值
     */
    QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const;

    /**
     * @brief 设置配置值
     * @param key 配置键
     * @param value 配置值
     */
    void setValue(const QString& key, const QVariant& value);

    /**
     * @brief 检查配置键是否存在
     * @param key 配置键
     * @return 是否存在
     */
    bool containsKey(const QString& key) const;

    /**
     * @brief 移除配置键
     * @param key 配置键
     */
    void removeKey(const QString& key);

    /**
     * @brief 清空所有配置
     */
    void clear();

    /**
     * @brief 获取所有配置键
     * @return 配置键列表
     */
    QStringList keys() const;

    /**
     * @brief 获取配置文件路径
     * @return 配置文件路径
     */
    QString configPath() const;

signals:
    /**
     * @brief 配置值变更信号
     * @param key 配置键
     * @param value 新的配置值
     */
    void valueChanged(const QString& key, const QVariant& value);

private:
    QString m_configPath;
    QSettings* m_settings;
    QMap<QString, QVariant> m_cache;
    mutable QMutex m_mutex;
};

#endif // CONFIGMANAGER_H
