<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OptimizeWidget</class>
 <widget class="QWidget" name="OptimizeWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>350</width>
    <height>650</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Optimization Parameters</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>5</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <item>
    <widget class="QGroupBox" name="algorithmGroup">
     <property name="title">
      <string>Dakota Optimization Method</string>
     </property>
     <layout class="QVBoxLayout" name="algorithmLayout">
      <item>
       <layout class="QFormLayout" name="methodFormLayout">
        <item row="0" column="0">
         <widget class="QLabel" name="methodLabel">
          <property name="text">
           <string>Method:</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QComboBox" name="methodCombo">
          <item>
           <property name="text">
            <string>pareto_set</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>conmin_frcg</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>conmin_mfd</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>soga</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLabel" name="methodDescription">
        <property name="text">
         <string>Pareto Set: Multi-objective optimization using Pareto frontier analysis</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="styleSheet">
         <string>color: #666; font-style: italic; padding: 5px;</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="convergenceGroup">
     <property name="title">
      <string>Convergence Settings</string>
     </property>
     <layout class="QFormLayout" name="convergenceFormLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="maxIterationsLabel">
        <property name="text">
         <string>Max Iterations:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QSpinBox" name="maxIterationsSpin">
        <property name="minimum">
         <number>10</number>
        </property>
        <property name="maximum">
         <number>100000</number>
        </property>
        <property name="value">
         <number>1000</number>
        </property>
        <property name="suffix">
         <string> iterations</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="maxFunctionEvalsLabel">
        <property name="text">
         <string>Max Function Evaluations:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QSpinBox" name="maxFunctionEvalsSpin">
        <property name="minimum">
         <number>100</number>
        </property>
        <property name="maximum">
         <number>1000000</number>
        </property>
        <property name="value">
         <number>10000</number>
        </property>
        <property name="suffix">
         <string> evaluations</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="convergenceToleranceLabel">
        <property name="text">
         <string>Convergence Tolerance:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QDoubleSpinBox" name="convergenceToleranceSpin">
        <property name="decimals">
         <number>8</number>
        </property>
        <property name="minimum">
         <double>1e-12</double>
        </property>
        <property name="maximum">
         <double>0.01</double>
        </property>
        <property name="value">
         <double>1e-06</double>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="sogaGroup">
     <property name="title">
      <string>SOGA Parameters (Single-Objective GA)</string>
     </property>
     <layout class="QFormLayout" name="sogaFormLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="populationSizeLabel">
        <property name="text">
         <string>Population Size:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QSpinBox" name="populationSizeSpin">
        <property name="minimum">
         <number>10</number>
        </property>
        <property name="maximum">
         <number>10000</number>
        </property>
        <property name="value">
         <number>50</number>
        </property>
        <property name="suffix">
         <string> individuals</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="mutationRateLabel">
        <property name="text">
         <string>Mutation Rate:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="mutationRateSpin">
        <property name="decimals">
         <number>4</number>
        </property>
        <property name="minimum">
         <double>0.0001</double>
        </property>
        <property name="maximum">
         <double>1.0</double>
        </property>
        <property name="value">
         <double>0.08</double>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="crossoverRateLabel">
        <property name="text">
         <string>Crossover Rate:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QDoubleSpinBox" name="crossoverRateSpin">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>0.1</double>
        </property>
        <property name="maximum">
         <double>1.0</double>
        </property>
        <property name="value">
         <double>0.8</double>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="replacementTypeLabel">
        <property name="text">
         <string>Replacement Type:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QComboBox" name="replacementTypeCombo">
        <item>
         <property name="text">
          <string>elitist</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>roulette_wheel</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>unique_roulette_wheel</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="paretoGroup">
     <property name="title">
      <string>Pareto Set Parameters (Multi-Objective)</string>
     </property>
     <layout class="QFormLayout" name="paretoFormLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="multiObjectiveWeightLabel">
        <property name="text">
         <string>Weight Sets:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QSpinBox" name="multiObjectiveWeightSpin">
        <property name="minimum">
         <number>2</number>
        </property>
        <property name="maximum">
         <number>100</number>
        </property>
        <property name="value">
         <number>10</number>
        </property>
        <property name="suffix">
         <string> sets</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0" colspan="2">
       <widget class="QCheckBox" name="randomWeightSetsCheck">
        <property name="text">
         <string>Generate Random Weight Sets</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="conminGroup">
     <property name="title">
      <string>CONMIN Parameters (Gradient-Based)</string>
     </property>
     <layout class="QFormLayout" name="conminFormLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="gradientToleranceLabel">
        <property name="text">
         <string>Gradient Tolerance:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QDoubleSpinBox" name="gradientToleranceSpin">
        <property name="decimals">
         <number>8</number>
        </property>
        <property name="minimum">
         <double>1e-12</double>
        </property>
        <property name="maximum">
         <double>0.1</double>
        </property>
        <property name="value">
         <double>1e-04</double>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="maxStepLabel">
        <property name="text">
         <string>Max Step Size:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="maxStepSpin">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>0.001</double>
        </property>
        <property name="maximum">
         <double>100.0</double>
        </property>
        <property name="value">
         <double>1.0</double>
        </property>
       </widget>
      </item>
      <item row="2" column="0" colspan="2">
       <widget class="QCheckBox" name="speculativeGradientCheck">
        <property name="text">
         <string>Use Speculative Gradient</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="outputGroup">
     <property name="title">
      <string>Output Settings</string>
     </property>
     <layout class="QVBoxLayout" name="outputLayout">
      <item>
       <widget class="QCheckBox" name="verboseOutputCheck">
        <property name="text">
         <string>Verbose Output</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="debugOutputCheck">
        <property name="text">
         <string>Debug Output</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="quietOutputCheck">
        <property name="text">
         <string>Quiet Mode</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 