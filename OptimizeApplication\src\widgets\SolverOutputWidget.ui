<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SolverOutputWidget</class>
 <widget class="QWidget" name="SolverOutputWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>450</width>
    <height>900</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Solver Output Configuration</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_main">
   <item>
    <widget class="QGroupBox" name="outputPathGroup">
     <property name="title">
      <string>Output Directory</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_path">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_dir">
        <item>
         <widget class="QLabel" name="outputDirLabel">
          <property name="text">
           <string>Directory:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="outputDirEdit">
          <property name="placeholderText">
           <string>Select output directory...</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="browseDirBtn">
          <property name="text">
           <string>Browse</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_pattern">
        <item>
         <widget class="QLabel" name="filePatternLabel">
          <property name="text">
           <string>File Pattern:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="filePatternEdit">
          <property name="text">
           <string>result_%1</string>
          </property>
          <property name="placeholderText">
           <string>Enter file name pattern...</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_format">
        <item>
         <widget class="QLabel" name="formatLabel">
          <property name="text">
           <string>Format:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="formatCombo">
          <item>
           <property name="text">
            <string>Dakota (.dat)</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>CSV (.csv)</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>JSON (.json)</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>XML (.xml)</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_format">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="autoSaveGroup">
     <property name="title">
      <string>Auto-Save Settings</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_autosave">
      <item>
       <widget class="QCheckBox" name="autoSaveCheck">
        <property name="text">
         <string>Enable Auto-Save</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_interval">
        <item>
         <widget class="QLabel" name="intervalLabel">
          <property name="text">
           <string>Interval (seconds):</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QSpinBox" name="intervalSpinBox">
          <property name="minimum">
           <number>10</number>
          </property>
          <property name="maximum">
           <number>3600</number>
          </property>
          <property name="value">
           <number>60</number>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_interval">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="compressionGroup">
     <property name="title">
      <string>Compression Settings</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_compression">
      <item>
       <widget class="QCheckBox" name="compressionCheck">
        <property name="text">
         <string>Enable Compression</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_compression">
        <item>
         <widget class="QLabel" name="compressionLevelLabel">
          <property name="text">
           <string>Level:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="compressionLevelCombo">
          <item>
           <property name="text">
            <string>Low</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Medium</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>High</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_compression">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="reportGroup">
     <property name="title">
      <string>Report Settings</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_report">
      <item>
       <widget class="QCheckBox" name="reportCheck">
        <property name="text">
         <string>Generate Report</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_reportformat">
        <item>
         <widget class="QLabel" name="reportFormatLabel">
          <property name="text">
           <string>Format:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="reportFormatCombo">
          <item>
           <property name="text">
            <string>HTML</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>PDF</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Text</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_reportformat">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_reportoptions">
        <item>
         <widget class="QCheckBox" name="includeChartsCheck">
          <property name="text">
           <string>Include Charts</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="includeStatsCheck">
          <property name="text">
           <string>Include Statistics</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_reportoptions">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="outputVariablesGroup">
     <property name="title">
      <string>Output Variables</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_variables">
      <item>
       <widget class="QLabel" name="variablesLabel">
        <property name="text">
         <string>Define output variables to be monitored:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QTableWidget" name="variablesTable"/>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_buttons">
        <item>
         <widget class="QPushButton" name="addVariableBtn">
          <property name="styleSheet">
           <string>QPushButton { background-color: #4CAF50; color: white; }</string>
          </property>
          <property name="text">
           <string>Add Variable</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="updateVariableBtn">
          <property name="styleSheet">
           <string>QPushButton { background-color: #2196F3; color: white; }</string>
          </property>
          <property name="text">
           <string>Update</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="deleteVariableBtn">
          <property name="styleSheet">
           <string>QPushButton { background-color: #f44336; color: white; }</string>
          </property>
          <property name="text">
           <string>Delete</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_buttons">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 