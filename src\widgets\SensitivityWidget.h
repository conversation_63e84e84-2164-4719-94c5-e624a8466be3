#ifndef SENSITIVITYWIDGET_H
#define SENSITIVITYWIDGET_H

#include <QWidget>
#include <QTableView>
#include <QStandardItemModel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QComboBox>
#include <QLabel>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include "../SARibbonBar/SARibbonCategory.h"
#include "../SARibbonBar/SARibbonBar.h"

/**
 * @brief The SensitivityWidget class provides UI for sensitivity analysis
 */
class SensitivityWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent widget
     */
    explicit SensitivityWidget(QWidget *parent = nullptr);
    
    /**
     * @brief Destructor
     */
    ~SensitivityWidget();
    
    /**
     * @brief Create a sensitivity category in the ribbon
     * @param ribbon The ribbon where the category will be added
     * @return The created category
     */
    SARibbonCategory* createRibbonCategory(SARibbonBar* ribbon);

private Q_SLOTS:
    /**
     * @brief Add a parameter to the sensitivity analysis
     */
    void addParameter();
    
    /**
     * @brief Remove a parameter from the sensitivity analysis
     */
    void removeParameter();
    
    /**
     * @brief Run sensitivity analysis
     */
    void runAnalysis();
    
    /**
     * @brief Method selection changed
     * @param index New selected index
     */
    void onMethodChanged(int index);

private:
    /**
     * @brief Initialize UI components
     */
    void initUI();
    
    /**
     * @brief Set up connections
     */
    void setupConnections();
    
    // UI components
    QVBoxLayout* mainLayout;
    QHBoxLayout* controlLayout;
    QTableView* parametersTable;
    QStandardItemModel* tableModel;
    QPushButton* addButton;
    QPushButton* removeButton;
    QPushButton* runButton;
    QComboBox* methodCombo;
    QLabel* samplesLabel;
    QSpinBox* samplesSpinBox;
    QLabel* toleranceLabel;
    QDoubleSpinBox* toleranceSpinBox;
};

#endif // SENSITIVITYWIDGET_H 