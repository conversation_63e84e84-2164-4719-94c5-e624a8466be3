#ifndef INPUTDATA_H
#define INPUTDATA_H

#include <QString>
#include <QStringList>
#include <QVector>
#include <QMap>
#include <QVariant>
#include <QDateTime>

/**
 * @brief 输入文件类型枚举
 */
enum class InputFileType {
    NC_WELANDER,        // NC Welander问题
    DISCHARGE_AIR,      // 排放空气
    DISCHARGE_WATER,    // 排放水
    CRITICAL_FLOW_AIR,  // 临界流空气
    CRITICAL_FLOW_WATER,// 临界流水
    WAVE_1D,           // 一维波
    NC_2PHASE,         // NC两相流
    UNKNOWN            // 未知类型
};

/**
 * @brief 控制卡数据结构
 */
struct ControlCard {
    int problemNumber;      // 问题编号
    QString problemType;    // 问题类型 (new, restart, etc.)
    QString analysisType;   // 分析类型 (transnt, steady, etc.)
    double endTime;         // 结束时间
    double minTimeStep;     // 最小时间步长
    double maxTimeStep;     // 最大时间步长
    int controlOption;      // 控制选项
    int minorEdit;          // 小编辑频率
    int majorEdit;          // 大编辑频率
    int restartFreq;        // 重启频率
};

/**
 * @brief 单位系统数据结构
 */
struct UnitSystem {
    QString inputUnits;     // 输入单位系统 (si, british, etc.)
    QString outputUnits;    // 输出单位系统
    QString workingFluid;   // 工作流体 (water, air, etc.)
    double scaleFactor;     // 比例因子
};

/**
 * @brief 绘图变量数据结构
 */
struct PlotVariable {
    int variableId;         // 变量ID
    QString variableType;   // 变量类型 (mflowj, tempf, p, etc.)
    QString componentId;    // 组件ID
    int plotFlag;           // 绘图标志
};

/**
 * @brief 小编辑变量数据结构
 */
struct MinorEditVariable {
    int editId;             // 编辑ID
    QString variableType;   // 变量类型
    QString componentId;    // 组件ID
};

/**
 * @brief 几何组件基类
 */
struct GeometryComponent {
    QString componentId;    // 组件ID
    QString componentName;  // 组件名称
    QString componentType;  // 组件类型 (pipe, branch, snglvol, etc.)
    QMap<QString, QVariant> properties; // 组件属性
};

/**
 * @brief 管道组件数据结构
 */
struct PipeComponent : public GeometryComponent {
    int numberOfVolumes;    // 体积数量
    QVector<double> volumes; // 体积数组
    QVector<double> lengths; // 长度数组
    QVector<double> elevations; // 高程数组
    QVector<double> roughness; // 粗糙度数组
    QVector<double> hydraulicDiameters; // 水力直径数组
    QVector<double> angles; // 角度数组
    QVector<int> flags;     // 标志数组
    
    // 初始条件
    struct InitialCondition {
        int thermodynamicState; // 热力学状态
        double pressure;        // 压力
        double temperature;     // 温度
        double quality;         // 干度
        double velocity;        // 速度
        double boronDensity;    // 硼密度
    };
    QVector<InitialCondition> initialConditions;
};

/**
 * @brief 分支组件数据结构
 */
struct BranchComponent : public GeometryComponent {
    int numberOfJunctions;  // 连接数量
    int numberOfVolumes;    // 体积数量
    double volume;          // 体积
    double length;          // 长度
    double elevation;       // 高程
    double angle;           // 角度
    
    struct JunctionData {
        QString fromComponent;  // 来源组件
        QString toComponent;    // 目标组件
        double area;           // 面积
        double forwardLoss;    // 正向损失
        double reverseLoss;    // 反向损失
        int flags;             // 标志
    };
    QVector<JunctionData> junctions;
};

/**
 * @brief 单体积组件数据结构
 */
struct SingleVolumeComponent : public GeometryComponent {
    double volume;          // 体积
    double length;          // 长度
    double elevation;       // 高程
    double angle;           // 角度
    double roughness;       // 粗糙度
    double hydraulicDiameter; // 水力直径
    
    // 初始条件
    int thermodynamicState; // 热力学状态
    double pressure;        // 压力
    double temperature;     // 温度
    double quality;         // 干度
};

/**
 * @brief 连接组件数据结构
 */
struct JunctionComponent : public GeometryComponent {
    QString fromComponent;  // 来源组件
    QString toComponent;    // 目标组件
    double area;           // 面积
    double forwardLoss;    // 正向损失
    double reverseLoss;    // 反向损失
    int flags;             // 标志
    
    // 初始条件
    int velocityFlag;       // 速度标志
    double velocity;        // 速度
    double interfaceVelocity; // 界面速度
    double quality;         // 干度
};

/**
 * @brief 热结构数据结构
 */
struct HeatStructure {
    QString heatStructureId; // 热结构ID
    int numberOfAxialNodes; // 轴向节点数
    int numberOfRadialNodes; // 径向节点数
    int geometryType;       // 几何类型
    int steadyStateFlag;    // 稳态标志
    double leftBoundary;    // 左边界
    double rightBoundary;   // 右边界
    
    QVector<double> meshPoints; // 网格点
    QVector<int> materialIds;   // 材料ID
    QVector<double> temperatures; // 温度分布
    
    struct BoundaryCondition {
        QString componentId;    // 组件ID
        int heatTransferCorrelation; // 传热关联式
        int surfaceType;       // 表面类型
        int axialIncrement;    // 轴向增量
        double area;           // 面积
        double length;         // 长度
    };
    QVector<BoundaryCondition> boundaryConditions;
};

/**
 * @brief 材料属性数据结构
 */
struct MaterialProperty {
    QString materialId;     // 材料ID
    QString materialName;   // 材料名称
    QMap<QString, QVariant> properties; // 材料属性
};

/**
 * @brief 表格数据结构
 */
struct TableData {
    QString tableId;        // 表格ID
    QString tableType;      // 表格类型
    QVector<QPair<double, double>> dataPoints; // 数据点对
    QString description;    // 描述
};

/**
 * @brief 主要的InputData数据结构
 */
class InputData
{
public:
    InputData();
    ~InputData();
    
    // 基本信息
    QString fileName;           // 文件名
    QString filePath;           // 文件路径
    InputFileType fileType;     // 文件类型
    QDateTime lastModified;     // 最后修改时间
    QString problemTitle;       // 问题标题
    
    // 控制信息
    ControlCard controlCard;    // 控制卡
    UnitSystem unitSystem;      // 单位系统
    
    // 输出控制
    QVector<PlotVariable> plotVariables;        // 绘图变量
    QVector<MinorEditVariable> minorEditVars;   // 小编辑变量
    
    // 几何组件
    QVector<PipeComponent> pipes;               // 管道组件
    QVector<BranchComponent> branches;          // 分支组件
    QVector<SingleVolumeComponent> volumes;     // 单体积组件
    QVector<JunctionComponent> junctions;       // 连接组件
    
    // 热结构
    QVector<HeatStructure> heatStructures;      // 热结构
    QVector<MaterialProperty> materials;        // 材料属性
    
    // 表格数据
    QVector<TableData> tables;                  // 表格数据
    
    // 注释和原始数据
    QStringList comments;       // 注释行
    QStringList rawLines;       // 原始文件行
    
    // 方法
    bool loadFromFile(const QString& filePath);
    bool saveToFile(const QString& filePath) const;
    void clear();
    bool isValid() const;
    
    // .i文件专用读写功能
    bool loadFromIFile(const QString& filePath);
    bool saveToIFile(const QString& filePath) const;
    bool exportToIFile(const QString& filePath, bool preserveComments = true) const;
    QString generateIFileContent() const;
    bool validateIFileFormat() const;
    
    // .i文件格式化选项
    struct IFileFormatOptions {
        bool preserveComments = true;
        bool preserveOriginalSpacing = false;
        bool addSectionHeaders = true;
        bool sortComponents = false;
        int indentSize = 4;
        QString lineEnding = "\n";
    };
    
    bool saveToIFileWithOptions(const QString& filePath, const IFileFormatOptions& options) const;
    
    // 查询方法
    GeometryComponent* findComponent(const QString& componentId);
    const GeometryComponent* findComponent(const QString& componentId) const;
    QVector<GeometryComponent*> getComponentsByType(const QString& type);
    TableData* findTable(const QString& tableId);
    
    // 统计信息
    int getTotalComponents() const;
    int getComponentCount(const QString& type) const;
    QStringList getComponentTypes() const;
    
    // 验证方法
    QStringList validateData() const;
    bool hasErrors() const;
    
    // 转换方法
    static InputFileType detectFileType(const QString& filePath);
    static QString fileTypeToString(InputFileType type);
    
private:
    bool parseControlCard(const QStringList& lines);
    bool parseUnitSystem(const QStringList& lines);
    bool parsePlotVariables(const QStringList& lines);
    bool parseMinorEditVariables(const QStringList& lines);
    bool parseGeometryComponents(const QStringList& lines);
    bool parseHeatStructures(const QStringList& lines);
    bool parseMaterials(const QStringList& lines);
    bool parseTables(const QStringList& lines);
    
    void parseComments(const QStringList& lines);
    QString extractComponentId(const QString& line) const;
    QString extractComponentType(const QString& line) const;
    QStringList tokenizeLine(const QString& line) const;
    
    // .i文件专用解析方法
    bool parseIFileHeader(const QStringList& lines);
    bool parseIFileControlCards(const QStringList& lines);
    bool parseIFileComponents(const QStringList& lines);
    bool parseIFileHeatStructures(const QStringList& lines);
    bool parseIFileTables(const QStringList& lines);
    
    // .i文件生成方法
    QString generateIFileHeader() const;
    QString generateIFileControlCards() const;
    QString generateIFileOutputControl() const;
    QString generateIFileComponents() const;
    QString generateIFileHeatStructures() const;
    QString generateIFileMaterials() const;
    QString generateIFileTables() const;
    QString generateIFileFooter() const;
    
    // .i文件格式化辅助方法
    QString formatIFileLine(const QString& cardId, const QStringList& values, const QString& comment = "") const;
    QString formatComponentCard(const GeometryComponent& component) const;
    QString formatPipeComponent(const PipeComponent& pipe) const;
    QString formatBranchComponent(const BranchComponent& branch) const;
    QString formatSingleVolumeComponent(const SingleVolumeComponent& volume) const;
    QString formatJunctionComponent(const JunctionComponent& junction) const;
    QString formatHeatStructureCard(const HeatStructure& heatStructure) const;
    QString formatTableCard(const TableData& table) const;
    
    // .i文件验证方法
    bool validateIFileStructure() const;
    bool validateComponentReferences() const;
    bool validateHeatStructureReferences() const;
    QStringList checkIFileConsistency() const;
    
    bool m_isValid;
    QStringList m_errors;
};

// 注册自定义类型到Qt元对象系统
Q_DECLARE_METATYPE(GeometryComponent)
Q_DECLARE_METATYPE(PipeComponent)
Q_DECLARE_METATYPE(BranchComponent)
Q_DECLARE_METATYPE(SingleVolumeComponent)
Q_DECLARE_METATYPE(JunctionComponent)
Q_DECLARE_METATYPE(HeatStructure)
Q_DECLARE_METATYPE(MaterialProperty)
Q_DECLARE_METATYPE(TableData)
Q_DECLARE_METATYPE(ControlCard)
Q_DECLARE_METATYPE(UnitSystem)
Q_DECLARE_METATYPE(PlotVariable)
Q_DECLARE_METATYPE(MinorEditVariable)

#endif // INPUTDATA_H 