#ifndef INPUTDATA_H
#define INPUTDATA_H
#include <QString>
#include <QStringList>
#include <QVector>
#include <QMap>
#include <QVariant>
#include <QDateTime>
#include <QPair>

/**
 * @brief 输入文件类型枚举
 */
enum class InputFileType {
    NC_WELANDER,        // NC Welander问题
    DISCHARGE_AIR,      // 排放空气
    DISCHARGE_WATER,    // 排放水
    CRITICAL_FLOW_AIR,  // 临界流空气
    CRITICAL_FLOW_WATER,// 临界流水
    WAVE_1D,           // 一维波
    NC_2PHASE,         // NC两相流
    UNKNOWN            // 未知类型
};

/**
 * @brief 控制卡数据结构
 */
struct ControlCard {
    int problemNumber;      // 问题编号
    QString problemType;    // 问题类型 (new, restart, etc.)
    QString analysisType;   // 分析类型 (transnt, steady, etc.)
    double endTime;         // 结束时间
    double minTimeStep;     // 最小时间步长
    double maxTimeStep;     // 最大时间步长
    int controlOption;      // 控制选项
    int minorEdit;          // 小编辑频率
    int majorEdit;          // 大编辑频率
    int restartFreq;        // 重启频率
};

/**
 * @brief 单位系统数据结构
 */
struct UnitSystem {
    QString inputUnits;     // 输入单位系统 (si, british, etc.)
    QString outputUnits;    // 输出单位系统
    QString workingFluid;   // 工作流体 (water, air, etc.)
    double scaleFactor;     // 比例因子
};

/**
 * @brief 绘图变量数据结构
 */
struct PlotVariable {
    int variableId;         // 变量ID
    QString variableType;   // 变量类型 (mflowj, tempf, p, etc.)
    QString componentId;    // 组件ID
    int plotFlag;           // 绘图标志
};

/**
 * @brief 小编辑变量数据结构
 */
struct MinorEditVariable {
    int editId;             // 编辑ID
    QString variableType;   // 变量类型
    QString componentId;    // 组件ID
};

/**
 * @brief 几何组件基类
 */
struct GeometryComponent {
    QString componentId;    // 组件ID
    QString componentName;  // 组件名称
    QString componentType;  // 组件类型 (pipe, branch, snglvol, etc.)
    QMap<QString, QVariant> properties; // 组件属性
};

/**
 * @brief 管道组件数据结构
 */
struct PipeComponent : public GeometryComponent {
    int numberOfVolumes;    // 体积数量
    QVector<double> volumes; // 体积数组
    QVector<double> lengths; // 长度数组
    QVector<double> elevations; // 高程数组
    QVector<double> roughness; // 粗糙度数组
    QVector<double> hydraulicDiameters; // 水力直径数组
    QVector<double> angles; // 角度数组
    QVector<int> flags;     // 标志数组

    // 初始条件
    struct InitialCondition {
        int thermodynamicState; // 热力学状态
        double pressure;        // 压力
        double temperature;     // 温度
        double quality;         // 干度
        double velocity;        // 速度
        double boronDensity;    // 硼密度
    };
    QVector<InitialCondition> initialConditions;
};

/**
 * @brief 分支组件数据结构
 */
struct BranchComponent : public GeometryComponent {
    int numberOfJunctions;  // 连接数量
    int numberOfVolumes;    // 体积数量
    double volume;          // 体积
    double length;          // 长度
    double elevation;       // 高程
    double angle;           // 角度

    struct JunctionData {
        QString fromComponent;  // 来源组件
        QString toComponent;    // 目标组件
        double area;           // 面积
        double forwardLoss;    // 正向损失
        double reverseLoss;    // 反向损失
        int flags;             // 标志
    };
    QVector<JunctionData> junctions;
};

/**
 * @brief 单体积组件数据结构
 */
struct SingleVolumeComponent : public GeometryComponent {
    double volume;          // 体积
    double length;          // 长度
    double elevation;       // 高程
    double angle;           // 角度
    double roughness;       // 粗糙度
    double hydraulicDiameter; // 水力直径

    // 初始条件
    int thermodynamicState; // 热力学状态
    double pressure;        // 压力
    double temperature;     // 温度
    double quality;         // 干度
};

/**
 * @brief 连接组件数据结构
 */
struct JunctionComponent : public GeometryComponent {
    QString fromComponent;  // 来源组件
    QString toComponent;    // 目标组件
    double area;           // 面积
    double forwardLoss;    // 正向损失
    double reverseLoss;    // 反向损失
    int flags;             // 标志

    // 初始条件
    int velocityFlag;       // 速度标志
    double velocity;        // 速度
    double interfaceVelocity; // 界面速度
    double quality;         // 干度
};

/**
 * @brief 热结构边界条件
 */
struct HeatStructureBoundaryCondition {
    int boundaryType;       // 边界类型
    double surfaceArea;     // 表面积
    QString componentId;    // 连接组件ID
    int volumeNumber;       // 体积编号
    double incrementNumber; // 增量编号
};

/**
 * @brief 热结构数据
 */
struct HeatStructure {
    QString heatStructureId;     // 热结构ID
    int numberOfAxialNodes;      // 轴向节点数
    int numberOfRadialNodes;     // 径向节点数
    int geometryType;           // 几何类型
    int steadyStateFlag;        // 稳态标志
    double leftBoundary;        // 左边界
    double rightBoundary;       // 右边界
    QVector<HeatStructureBoundaryCondition> boundaryConditions; // 边界条件
};

/**
 * @brief 材料数据
 */
struct MaterialData {
    QString materialId;     // 材料ID
    QString materialName;   // 材料名称
    QVector<QPair<double, double>> thermalProps; // 热物性数据
};

/**
 * @brief 表格数据
 */
struct TableData {
    QString tableId;        // 表格ID
    QString tableType;      // 表格类型
    QVector<QPair<double, double>> dataPoints; // 数据点
};

/**
 * @brief InputData类用于处理输入数据文件
 */
class InputData
{
public:
    /**
     * @brief 构造函数
     */
    InputData();

    /**
     * @brief 从文件加载数据
     * @param filePath 文件路径
     * @return 是否加载成功
     */
    bool loadFromFile(const QString& filePath);

    /**
     * @brief 保存到文件
     * @param filePath 文件路径
     * @return 是否保存成功
     */
    bool saveToFile(const QString& filePath) const;

    /**
     * @brief 清除所有数据
     */
    void clear();

    /**
     * @brief 检查数据是否有效
     * @return 是否有效
     */
    bool isValid() const;

    /**
     * @brief 获取错误消息
     * @return 错误消息列表
     */
    QStringList getErrors() const { return m_errors; }

    /**
     * @brief 获取所有组件数量
     * @return 组件总数
     */
    int getTotalComponents() const { 
        return pipes.size() + branches.size() + volumes.size() + junctions.size(); 
    }

    /**
     * @brief 查找组件
     * @param componentId 组件ID
     * @return 组件指针，如果未找到返回nullptr
     */
    GeometryComponent* findComponent(const QString& componentId);
    
    /**
     * @brief 查找组件 (const版本)
     * @param componentId 组件ID
     * @return 组件指针，如果未找到返回nullptr
     */
    const GeometryComponent* findComponent(const QString& componentId) const;

    // 解析各部分数据
    bool parseIFileHeader(const QStringList& lines);
    bool parseIFileControlCards(const QStringList& lines);
    bool parseIFileComponents(const QStringList& lines);
    bool parseIFileHeatStructures(const QStringList& lines);
    bool parseIFileTables(const QStringList& lines);

    // 生成各部分数据
    QString generateIFileHeader() const;
    QString generateIFileControlCards() const;
    QString generateIFileOutputControl() const;
    QString generateIFileComponents() const;
    QString generateIFileHeatStructures() const;
    QString generateIFileMaterials() const;
    QString generateIFileTables() const;
    QString generateIFileFooter() const;

    // 格式化各部分数据
    QString formatIFileLine(const QString& cardId, const QStringList& values, const QString& comment = QString()) const;
    QString formatComponentCard(const GeometryComponent& component) const;
    QString formatPipeComponent(const PipeComponent& pipe) const;
    QString formatBranchComponent(const BranchComponent& branch) const;
    QString formatSingleVolumeComponent(const SingleVolumeComponent& volume) const;
    QString formatJunctionComponent(const JunctionComponent& junction) const;
    QString formatHeatStructureCard(const HeatStructure& heatStructure) const;
    QString formatTableCard(const TableData& table) const;

    // 验证数据
    bool validateIFileStructure() const;
    bool validateComponentReferences() const;
    bool validateHeatStructureReferences() const;
    QStringList checkIFileConsistency() const;

private:
    // 辅助方法
    InputFileType detectFileType(const QString& filePath) const;
    QStringList tokenizeLine(const QString& line) const;
    void parseComments(const QStringList& lines);
    bool parseControlCard(const QStringList& lines);
    bool parseUnitSystem(const QStringList& lines);
    bool parsePlotVariables(const QStringList& lines);
    bool parseMinorEditVariables(const QStringList& lines);
    bool parseGeometryComponents(const QStringList& lines);
    bool parseHeatStructures(const QStringList& lines);
    bool parseMaterials(const QStringList& lines);
    bool parseTables(const QStringList& lines);

public:
    // 文件信息
    QString filePath;            // 文件路径
    QString fileName;            // 文件名
    QDateTime lastModified;      // 最后修改时间
    InputFileType fileType;      // 文件类型
    QString problemTitle;        // 问题标题

    // 控制卡
    ControlCard controlCard;     // 控制卡
    UnitSystem unitSystem;       // 单位系统

    // 输出控制
    QVector<PlotVariable> plotVariables;      // 绘图变量
    QVector<MinorEditVariable> minorEditVars; // 小编辑变量

    // 几何组件
    QVector<PipeComponent> pipes;             // 管道组件
    QVector<BranchComponent> branches;        // 分支组件
    QVector<SingleVolumeComponent> volumes;   // 单体积组件
    QVector<JunctionComponent> junctions;     // 连接组件

    // 热结构和材料
    QVector<HeatStructure> heatStructures;    // 热结构
    QVector<MaterialData> materials;          // 材料

    // 表格
    QVector<TableData> tables;                // 表格

    // 注释和原始数据
    QStringList comments;                     // 注释
    QStringList rawLines;                     // 原始行

private:
    bool m_isValid;                          // 数据是否有效
    QStringList m_errors;                     // 错误消息
};

#endif // INPUTDATA_H 