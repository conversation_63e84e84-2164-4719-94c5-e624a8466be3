#include "InputWidget.h"
#include "ui_InputWidget.h"
#include <QMessageBox>
#include <QHeaderView>
#include <QTableWidgetItem>
#include <QComboBox>
#include <QLineEdit>
#include <QPushButton>
#include <QTableWidget>
#include <QGroupBox>
#include <QLabel>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QSpacerItem>
#include <QStringList>
#include <QAbstractItemView>
#include <algorithm>

InputWidget::InputWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::InputWidget)
    , m_currentParamIndex(-1)
    , m_updatingUI(false)
{
    ui->setupUi(this);
    initializeUI();
    setupConnections();
    setupTableWidget();
}

InputWidget::~InputWidget()
{
    delete ui;
}

void InputWidget::initializeUI()
{
    // Initialize parameter combo box
    ui->ParamName->clear();
    
    // Add placeholder item for ComboBox (Qt 5.14.2 compatible)
    ui->ParamName->addItem("Enter or select parameter name...");
    ui->ParamName->setCurrentIndex(0);
    
    // Clear parameter input fields
    ui->editParamInit->clear();
    ui->editParamUpper->clear();
    ui->editParamLower->clear();
    
    // Set placeholder text for input fields
    ui->editParamInit->setPlaceholderText("Enter initial value");
    ui->editParamUpper->setPlaceholderText("Enter upper bound");
    ui->editParamLower->setPlaceholderText("Enter lower bound");
}

void InputWidget::setupConnections()
{
    // Parameter name combo box
    connect(ui->ParamName, QOverload<const QString &>::of(&QComboBox::currentTextChanged),
            this, &InputWidget::onParamNameChanged);
    
    // Parameter value line edits
    connect(ui->editParamInit, &QLineEdit::textChanged,
            this, &InputWidget::onParamInitChanged);
    connect(ui->editParamUpper, &QLineEdit::textChanged,
            this, &InputWidget::onParamUpperChanged);
    connect(ui->editParamLower, &QLineEdit::textChanged,
            this, &InputWidget::onParamLowerChanged);
    
    // Buttons
    connect(ui->btnAddParam, &QPushButton::clicked,
            this, &InputWidget::onAddParamClicked);
    connect(ui->btnDeleteParam, &QPushButton::clicked,
            this, &InputWidget::onDeleteParamClicked);
    connect(ui->btnUpdateParam, &QPushButton::clicked,
            this, &InputWidget::onUpdateParamClicked);
    connect(ui->btnLoadSample, &QPushButton::clicked,
            this, &InputWidget::loadSampleData);
    connect(ui->btnClearAll, &QPushButton::clicked,
            this, &InputWidget::clearAllData);
    
    // Check if new buttons exist before connecting
    if (ui->btnValidate) {
        connect(ui->btnValidate, SIGNAL(clicked()), this, SLOT(validateParameters()));
    }
    if (ui->btnExport) {
        connect(ui->btnExport, SIGNAL(clicked()), this, SLOT(exportParameters()));
    }
    
    // Table widget
    connect(ui->tableWidget, &QTableWidget::itemSelectionChanged,
            this, &InputWidget::onTableSelectionChanged);
}

void InputWidget::setupTableWidget()
{
    // Set table headers
    ui->tableWidget->setHorizontalHeaderLabels(QStringList() << "Name" << "Init" << "Upper" << "Lower");
    
    // Configure table properties
    ui->tableWidget->horizontalHeader()->setStretchLastSection(true);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableWidget->setAlternatingRowColors(true);
    
    // Set column widths
    ui->tableWidget->setColumnWidth(0, 100); // Name
    ui->tableWidget->setColumnWidth(1, 80);  // Init
    ui->tableWidget->setColumnWidth(2, 80);  // Upper
    ui->tableWidget->setColumnWidth(3, 80);  // Lower
}

// Parameter management methods
QList<ParamInfo> InputWidget::getParamsList() const
{
    return m_paramsList;
}

void InputWidget::setParamsList(const QList<ParamInfo> &paramsList)
{
    m_paramsList = paramsList;
    updateParamCombo();
    updateTableFromParams();
    
    // Reset current selection
    m_currentParamIndex = -1;
    clearParamFields();
    
    emit parametersChanged();
}

void InputWidget::addParam(const ParamInfo &param)
{
    m_paramsList.append(param);
    updateParamCombo();
    updateTableFromParams();
    
    // Select the newly added parameter
    m_currentParamIndex = m_paramsList.size() - 1;
    ui->ParamName->setCurrentText(param.paramName);
    updateCurrentParamDisplay();
    
    emit parametersChanged();
}

void InputWidget::removeParam(int index)
{
    if (index >= 0 && index < m_paramsList.size()) {
        m_paramsList.removeAt(index);
        updateParamCombo();
        updateTableFromParams();
        
        // Adjust current index
        if (m_currentParamIndex >= m_paramsList.size()) {
            m_currentParamIndex = m_paramsList.size() - 1;
        }
        if (m_currentParamIndex < 0) {
            clearParamFields();
        } else {
            updateCurrentParamDisplay();
        }
        
        emit parametersChanged();
    }
}

void InputWidget::updateParam(int index, const ParamInfo &param)
{
    if (index >= 0 && index < m_paramsList.size()) {
        m_paramsList[index] = param;
        updateParamCombo();
        updateTableFromParams();
        updateCurrentParamDisplay();
        emit parametersChanged();
    }
}

void InputWidget::clearParams()
{
    m_paramsList.clear();
    m_currentParamIndex = -1;
    updateParamCombo();
    updateTableFromParams();
    clearParamFields();
    emit parametersChanged();
}

int InputWidget::getParameterCount() const
{
    return m_paramsList.size();
}

// Removed const version - now using slot version below

void InputWidget::loadSampleData()
{
    clearParams();
    
    // Add sample parameters
    addParam(ParamInfo("Temperature", "300.0", "400.0", "250.0"));
    addParam(ParamInfo("Pressure", "1.0e5", "2.0e5", "0.5e5"));
    addParam(ParamInfo("Flow_Rate", "10.0", "20.0", "5.0"));
    
    QMessageBox::information(this, "Sample Data", "Sample parameters loaded successfully!");
}

void InputWidget::clearAllData()
{
    clearParams();
    QMessageBox::information(this, "Clear Data", "All data cleared successfully!");
}

// Private slots
void InputWidget::onParamNameChanged()
{
    if (m_updatingUI) return;
    
    QString selectedName = ui->ParamName->currentText();
    
    // Skip placeholder text
    if (selectedName == "Enter or select parameter name..." || selectedName.isEmpty()) {
        return;
    }
    
    // Find corresponding parameter
    for (int i = 0; i < m_paramsList.size(); ++i) {
        if (m_paramsList[i].paramName == selectedName) {
            m_currentParamIndex = i;
            loadParamFromTable(i);
            emit parameterSelected(i);
            break;
        }
    }
}

void InputWidget::onParamInitChanged()
{
    if (m_updatingUI) return;
    
    if (m_currentParamIndex >= 0 && m_currentParamIndex < m_paramsList.size()) {
        m_paramsList[m_currentParamIndex].paramInit = ui->editParamInit->text();
        updateTableFromParams();
        emit parametersChanged();
    }
}

void InputWidget::onParamUpperChanged()
{
    if (m_updatingUI) return;
    
    if (m_currentParamIndex >= 0 && m_currentParamIndex < m_paramsList.size()) {
        m_paramsList[m_currentParamIndex].paramUpper = ui->editParamUpper->text();
        updateTableFromParams();
        emit parametersChanged();
    }
}

void InputWidget::onParamLowerChanged()
{
    if (m_updatingUI) return;
    
    if (m_currentParamIndex >= 0 && m_currentParamIndex < m_paramsList.size()) {
        m_paramsList[m_currentParamIndex].paramLower = ui->editParamLower->text();
        updateTableFromParams();
        emit parametersChanged();
    }
}

void InputWidget::onAddParamClicked()
{
    QString paramName = generateUniqueParamName();
    QString initValue = ui->editParamInit->text().isEmpty() ? "0.0" : ui->editParamInit->text();
    QString upperValue = ui->editParamUpper->text().isEmpty() ? "1.0" : ui->editParamUpper->text();
    QString lowerValue = ui->editParamLower->text().isEmpty() ? "0.0" : ui->editParamLower->text();
    
    ParamInfo newParam(paramName, initValue, upperValue, lowerValue);
    
    // Validate parameter data
    QString errorMsg;
    if (!isValidParamData(newParam, errorMsg)) {
        QMessageBox::warning(this, "Invalid Parameter", errorMsg);
        return;
    }
    
    addParam(newParam);
}

void InputWidget::onDeleteParamClicked()
{
    if (m_currentParamIndex >= 0 && m_currentParamIndex < m_paramsList.size()) {
        QString paramName = m_paramsList[m_currentParamIndex].paramName;
        
        int ret = QMessageBox::question(this, "Delete Parameter",
                                      QString("Are you sure you want to delete parameter '%1'?").arg(paramName),
                                      QMessageBox::Yes | QMessageBox::No);
        
        if (ret == QMessageBox::Yes) {
            removeParam(m_currentParamIndex);
        }
    }
}

void InputWidget::onUpdateParamClicked()
{
    if (m_currentParamIndex >= 0 && m_currentParamIndex < m_paramsList.size()) {
        ParamInfo updatedParam;
        updatedParam.paramName = ui->ParamName->currentText();
        updatedParam.paramInit = ui->editParamInit->text();
        updatedParam.paramUpper = ui->editParamUpper->text();
        updatedParam.paramLower = ui->editParamLower->text();
        
        // Validate parameter data
        QString errorMsg;
        if (!isValidParamData(updatedParam, errorMsg)) {
            QMessageBox::warning(this, "Invalid Parameter", errorMsg);
            return;
        }
        
        updateParam(m_currentParamIndex, updatedParam);
        QMessageBox::information(this, "Update Parameter", "Parameter updated successfully!");
    }
}

void InputWidget::onTableSelectionChanged()
{
    int currentRow = ui->tableWidget->currentRow();
    if (currentRow >= 0 && currentRow < m_paramsList.size()) {
        m_currentParamIndex = currentRow;
        loadParamFromTable(currentRow);
        
        // Update combo box selection
        m_updatingUI = true;
        ui->ParamName->setCurrentText(m_paramsList[currentRow].paramName);
        m_updatingUI = false;
        
        emit parameterSelected(currentRow);
    }
}

// Private methods
void InputWidget::updateParamCombo()
{
    m_updatingUI = true;
    ui->ParamName->clear();
    
    // Add placeholder item
    ui->ParamName->addItem("Enter or select parameter name...");
    
    // Add existing parameters
    for (const auto& param : m_paramsList) {
        ui->ParamName->addItem(param.paramName);
    }
    
    // Set to placeholder by default
    ui->ParamName->setCurrentIndex(0);
    
    m_updatingUI = false;
}

void InputWidget::updateParamFields()
{
    if (m_currentParamIndex >= 0 && m_currentParamIndex < m_paramsList.size()) {
        const ParamInfo& param = m_paramsList[m_currentParamIndex];
        
        m_updatingUI = true;
        ui->editParamInit->setText(param.paramInit);
        ui->editParamUpper->setText(param.paramUpper);
        ui->editParamLower->setText(param.paramLower);
        m_updatingUI = false;
    }
}

void InputWidget::updateTableFromParams()
{
    ui->tableWidget->setRowCount(m_paramsList.size());
    
    for (int i = 0; i < m_paramsList.size(); ++i) {
        const ParamInfo& param = m_paramsList[i];
        
        ui->tableWidget->setItem(i, 0, new QTableWidgetItem(param.paramName));
        ui->tableWidget->setItem(i, 1, new QTableWidgetItem(param.paramInit));
        ui->tableWidget->setItem(i, 2, new QTableWidgetItem(param.paramUpper));
        ui->tableWidget->setItem(i, 3, new QTableWidgetItem(param.paramLower));
    }
    
    // Update parameter count label if it exists
    if (ui->paramCountLabel) {
        ui->paramCountLabel->setText(QString("Parameters: %1").arg(m_paramsList.size()));
    }
    
    // Update table selection
    if (m_currentParamIndex >= 0 && m_currentParamIndex < m_paramsList.size()) {
        ui->tableWidget->selectRow(m_currentParamIndex);
    }
}

void InputWidget::updateCurrentParamDisplay()
{
    updateParamFields();
    if (m_currentParamIndex >= 0 && m_currentParamIndex < m_paramsList.size()) {
        m_updatingUI = true;
        ui->ParamName->setCurrentText(m_paramsList[m_currentParamIndex].paramName);
        ui->tableWidget->selectRow(m_currentParamIndex);
        m_updatingUI = false;
    }
}

void InputWidget::loadParamFromTable(int row)
{
    if (row >= 0 && row < m_paramsList.size()) {
        const ParamInfo& param = m_paramsList[row];
        
        m_updatingUI = true;
        ui->editParamInit->setText(param.paramInit);
        ui->editParamUpper->setText(param.paramUpper);
        ui->editParamLower->setText(param.paramLower);
        m_updatingUI = false;
    }
}

void InputWidget::clearParamFields()
{
    m_updatingUI = true;
    ui->editParamInit->clear();
    ui->editParamUpper->clear();
    ui->editParamLower->clear();
    ui->ParamName->setCurrentIndex(-1);
    ui->tableWidget->clearSelection();
    m_updatingUI = false;
}

bool InputWidget::isValidParamData(const ParamInfo &param, QString &errorMsg) const
{
    // Check parameter name
    if (param.paramName.isEmpty()) {
        errorMsg = "Parameter name cannot be empty.";
        return false;
    }
    
    // Check for duplicate names (excluding current parameter)
    for (int i = 0; i < m_paramsList.size(); ++i) {
        if (i != m_currentParamIndex && m_paramsList[i].paramName == param.paramName) {
            errorMsg = QString("Parameter name '%1' already exists.").arg(param.paramName);
            return false;
        }
        }
        
    // Validate numeric values
        bool initOk, upperOk, lowerOk;
        double initVal = param.paramInit.toDouble(&initOk);
        double upperVal = param.paramUpper.toDouble(&upperOk);
        double lowerVal = param.paramLower.toDouble(&lowerOk);
        
        if (!initOk) {
        errorMsg = "Invalid initial value. Must be a number.";
        return false;
        }
        if (!upperOk) {
        errorMsg = "Invalid upper bound. Must be a number.";
        return false;
        }
        if (!lowerOk) {
        errorMsg = "Invalid lower bound. Must be a number.";
        return false;
        }
        
    // Check bounds relationship
            if (lowerVal >= upperVal) {
        errorMsg = "Lower bound must be less than upper bound.";
        return false;
            }
    
            if (initVal < lowerVal || initVal > upperVal) {
        errorMsg = "Initial value must be between lower and upper bounds.";
        return false;
    }
    
    return true;
}

QString InputWidget::generateUniqueParamName() const
{
    QString baseName = "Param";
    int counter = 1;
    QString paramName;
    
    do {
        paramName = QString("%1_%2").arg(baseName).arg(counter);
        counter++;
    } while (std::any_of(m_paramsList.begin(), m_paramsList.end(),
                        [&paramName](const ParamInfo& param) {
                            return param.paramName == paramName;
                        }));
    
    return paramName;
}

void InputWidget::validateParameters()
{
    if (m_paramsList.isEmpty()) {
        QMessageBox::information(this, "Validation", "No parameters to validate.");
        return;
    }
    
    QStringList errors;
    for (int i = 0; i < m_paramsList.size(); ++i) {
        QString errorMsg;
        if (!isValidParamData(m_paramsList[i], errorMsg)) {
            errors.append(QString("Parameter %1: %2").arg(i + 1).arg(errorMsg));
        }
    }
    
    if (errors.isEmpty()) {
        QMessageBox::information(this, "Validation", "All parameters are valid!");
    } else {
        QString message = "Validation errors found:\n\n" + errors.join("\n");
        QMessageBox::warning(this, "Validation Errors", message);
    }
}

void InputWidget::exportParameters()
{
    if (m_paramsList.isEmpty()) {
        QMessageBox::information(this, "Export", "No parameters to export.");
        return;
    }
    
    QString fileName = QFileDialog::getSaveFileName(this,
        "Export Parameters", "parameters.csv",
        "CSV Files (*.csv);;JSON Files (*.json);;All Files (*)");
    
    if (!fileName.isEmpty()) {
        // Simple CSV export
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            out << "Parameter Name,Initial Value,Upper Bound,Lower Bound\n";
            
            for (const auto& param : m_paramsList) {
                out << param.paramName << "," 
                    << param.paramInit << ","
                    << param.paramUpper << ","
                    << param.paramLower << "\n";
            }
            
            QMessageBox::information(this, "Export", "Parameters exported successfully!");
        } else {
            QMessageBox::warning(this, "Export Error", "Failed to save file.");
        }
    }
}
