# PowerShell script to create basic icons for the application
# Function to create a simple colored square icon with text
function Create-SimpleIcon {
    param (
        [string]$outputPath,
        [string]$text,
        [string]$backgroundColor,
        [int]$size = 64
    )
    
    # Create a new bitmap
    Add-Type -AssemblyName System.Drawing
    $bitmap = New-Object System.Drawing.Bitmap($size, $size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Set background color
    $brush = New-Object System.Drawing.SolidBrush([System.Drawing.ColorTranslator]::FromHtml($backgroundColor))
    $graphics.FillRectangle($brush, 0, 0, $size, $size)
    
    # Add text
    $font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $stringFormat = New-Object System.Drawing.StringFormat
    $stringFormat.Alignment = [System.Drawing.StringAlignment]::Center
    $stringFormat.LineAlignment = [System.Drawing.StringAlignment]::Center
    $graphics.DrawString($text, $font, $textBrush, [System.Drawing.RectangleF]::new(0, 0, $size, $size), $stringFormat)
    
    # Save the image
    $bitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    # Cleanup
    $graphics.Dispose()
    $bitmap.Dispose()
    
    Write-Host "Created icon: $outputPath"
}

# Create icons directory if it doesn't exist
$iconsDir = ".\resources\icons"
if (-not (Test-Path $iconsDir)) {
    New-Item -ItemType Directory -Path $iconsDir -Force
}

# Create all icons listed in the resources file
$iconList = @(
    @{name="application.png"; text="App"; color="#3498db"},
    @{name="open.png"; text="Open"; color="#2ecc71"},
    @{name="save.png"; text="Save"; color="#2ecc71"},
    @{name="exit.png"; text="Exit"; color="#e74c3c"},
    @{name="settings.png"; text="Set"; color="#f39c12"},
    @{name="about.png"; text="About"; color="#3498db"},
    @{name="help.png"; text="Help"; color="#3498db"},
    @{name="optimize.png"; text="Opt"; color="#9b59b6"},
    @{name="sensitivity.png"; text="Sens"; color="#9b59b6"},
    @{name="uncertainty.png"; text="Unc"; color="#9b59b6"},
    @{name="input.png"; text="In"; color="#2ecc71"},
    @{name="output.png"; text="Out"; color="#e74c3c"},
    @{name="chart.png"; text="Chart"; color="#3498db"},
    @{name="run.png"; text="Run"; color="#2ecc71"},
    @{name="stop.png"; text="Stop"; color="#e74c3c"},
    @{name="reset.png"; text="Reset"; color="#f39c12"},
    @{name="add.png"; text="Add"; color="#2ecc71"},
    @{name="remove.png"; text="Del"; color="#e74c3c"},
    @{name="edit.png"; text="Edit"; color="#f39c12"},
    @{name="gradient_descent.png"; text="GD"; color="#9b59b6"},
    @{name="newton.png"; text="Newton"; color="#9b59b6"},
    @{name="quasi_newton.png"; text="QN"; color="#9b59b6"},
    @{name="genetic.png"; text="GA"; color="#9b59b6"},
    @{name="particle_swarm.png"; text="PSO"; color="#9b59b6"},
    @{name="local_sensitivity.png"; text="Local"; color="#9b59b6"},
    @{name="global_sensitivity.png"; text="Global"; color="#9b59b6"},
    @{name="sobol.png"; text="Sobol"; color="#9b59b6"},
    @{name="morris.png"; text="Morris"; color="#9b59b6"},
    @{name="monte_carlo.png"; text="MC"; color="#9b59b6"},
    @{name="latin_hypercube.png"; text="LH"; color="#9b59b6"},
    @{name="quasi_monte_carlo.png"; text="QMC"; color="#9b59b6"},
    @{name="polynomial_chaos.png"; text="PC"; color="#9b59b6"},
    @{name="new.png"; text="New"; color="#2ecc71"}
)

foreach ($icon in $iconList) {
    $outputPath = Join-Path $iconsDir $icon.name
    Create-SimpleIcon -outputPath $outputPath -text $icon.text -backgroundColor $icon.color
}

Write-Host "All icons created successfully!" 