#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QLocale>
#include <QTranslator>
#include "ui/MainWindow.h"
#include "core/ApplicationCore.h"
#include "core/ConfigManager.h"
#include "core/ThemeManager.h"
#include "utils/Logger.h"
#include "utils/Common.h"

int main(int argc, char *argv[])
{
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
//    qputenv("QT_SCALE_FACTOR", "1.5");
    // Create application
    QApplication app(argc, argv);
//    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    // Set application properties
    app.setApplicationName(AppConstants::APP_NAME);
    app.setApplicationVersion(AppConstants::APP_VERSION);
    app.setOrganizationName(AppConstants::ORGANIZATION_NAME);
    app.setOrganizationDomain(AppConstants::ORGANIZATION_DOMAIN);
    
    // Setup internationalization
    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "OptimizeApplication_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            app.installTranslator(&translator);
            break;
        }
    }
    
    // Initialize application core
    ApplicationCore* core = ApplicationCore::instance();
    if (!core->initialize()) {
        LOG_CRITICAL("Failed to initialize application core", "main");
        return -1;
    }
    
    // Initialize theme manager
    ThemeManager* themeManager = ThemeManager::instance();
    themeManager->setTheme(ThemeType::Light);  // Default theme
    
    // Create and show main window
    MainWindow mainWindow;
    
    // Restore window geometry and state
    ConfigManager* config = core->getConfigManager();
    if (config) {
        QByteArray geometry = config->getWindowGeometry();
        QByteArray state = config->getWindowState();
        
        if (!geometry.isEmpty()) {
            mainWindow.restoreGeometry(geometry);
        }
        
        if (!state.isEmpty()) {
            mainWindow.restoreState(state);
        }
    }
    
    mainWindow.show();
    
    LOG_INFO("Application started successfully", "main");
    
    // Run application
    int result = app.exec();
    
    // Save window geometry and state
    if (config) {
        config->setWindowGeometry(mainWindow.saveGeometry());
        config->setWindowState(mainWindow.saveState());
    }
    
    // Shutdown application core
    core->shutdown();
    
    LOG_INFO("Application terminated", "main");
    
    return result;
} 
