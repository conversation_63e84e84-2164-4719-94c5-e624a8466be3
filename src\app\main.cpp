#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QLocale>
#include <QTranslator>
#include <QTimer>
#include <QMessageBox>
#include <QProcess>
#include <QThread>
#include <QStandardPaths>

#include "../core/application/ApplicationCore.h"
#include "../core/config/ConfigManager.h"
#include "../core/theme/ThemeManager.h"
#include "../ui/mainwindow/MainWindow.h"
#include "../utils/common/ErrorHandler.h"
#include "../utils/logging/LogManager.h"
#include "../../3rdparty/SARibbonBar/SARibbonBar.h"

int main(int argc, char *argv[])
{
    // Initialize SARibbonBar for high DPI support
    SARibbonBar::initHighDpi();

    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);

    // Create application
    QApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("OptimizeApplication");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("OptimizeApp");
    app.setOrganizationDomain("optimizeapp.com");

    // Setup internationalization
    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "OptimizeApplication_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            app.installTranslator(&translator);
            break;
        }
    }

    // Handle restart argument if present
    if (argc > 1 && QString(argv[1]) == "--restart") {
        // Wait a moment before starting to ensure the previous instance has closed
        QThread::msleep(1000);
    }

    // Set up error handling
    ErrorHandler::setErrorCallback([](const ErrorHandler::ErrorInfo& errorInfo) {
        if (errorInfo.severity == ErrorSeverity::Fatal) {
            QMessageBox::critical(nullptr, "Fatal Error", errorInfo.message);
        } else if (errorInfo.severity == ErrorSeverity::Critical) {
            QMessageBox::critical(nullptr, "Critical Error", errorInfo.message);
        }
    });
    // Initialize application core
    ApplicationCore* core = ApplicationCore::instance();
    if (!core->initialize()) {
        ErrorHandler::handleError("Failed to initialize application core",
                                ErrorSeverity::Fatal, "main");
        return -1;
    }

    // Create main window
    MainWindow* mainWindow = new MainWindow();

    // Restore window geometry and state
    ConfigManager* config = core->configManager();
    if (config) {
        QByteArray geometry = config->getValue("window/geometry").toByteArray();
        QByteArray state = config->getValue("window/state").toByteArray();

        if (!geometry.isEmpty()) {
            mainWindow->restoreGeometry(geometry);
        }

        if (!state.isEmpty()) {
            mainWindow->restoreState(state);
        }
    }

    // Show the window with a delay to allow Qt to properly initialize everything
    QTimer::singleShot(100, mainWindow, &MainWindow::show);

    if (core->logManager()) {
        core->logManager()->logInfo("Application started successfully");
    }
    // Run application
    int result = app.exec();

    // Save window geometry and state
    if (config) {
        config->setValue("window/geometry", mainWindow->saveGeometry());
        config->setValue("window/state", mainWindow->saveState());
        config->save();
    }

    // Clean up
    delete mainWindow;

    // Shutdown application core
    core->cleanup();

    if (core->logManager()) {
        core->logManager()->logInfo("Application terminated");
    }

    // If the application crashed or was killed abruptly, restart it
    if (result == 1) {
        QProcess::startDetached(QApplication::applicationFilePath(), QStringList() << "--restart");
    }

    return result;
}
