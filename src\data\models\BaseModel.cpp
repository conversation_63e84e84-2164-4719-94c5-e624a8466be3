#include "BaseModel.h"
#include <QUuid>

BaseModel::BaseModel()
    : m_id(QUuid::createUuid().toString(QUuid::WithoutBraces))
    , m_createdAt(QDateTime::currentDateTime())
    , m_updatedAt(QDateTime::currentDateTime())
{
}

BaseModel::BaseModel(const BaseModel& other)
    : m_id(other.m_id)
    , m_createdAt(other.m_createdAt)
    , m_updatedAt(other.m_updatedAt)
{
}

BaseModel& BaseModel::operator=(const BaseModel& other)
{
    if (this != &other) {
        m_id = other.m_id;
        m_createdAt = other.m_createdAt;
        m_updatedAt = other.m_updatedAt;
    }
    return *this;
}

QJsonObject BaseModel::toJson() const
{
    QJsonObject json;
    json["id"] = m_id;
    json["createdAt"] = m_createdAt.toString(Qt::ISODate);
    json["updatedAt"] = m_updatedAt.toString(Qt::ISODate);
    json["modelName"] = modelName();
    json["modelVersion"] = modelVersion();
    
    // 添加模型特定数据
    QVariant data = toVariant();
    if (data.canConvert<QJsonObject>()) {
        QJsonObject dataJson = data.toJsonObject();
        for (auto it = dataJson.begin(); it != dataJson.end(); ++it) {
            json[it.key()] = it.value();
        }
    }
    
    return json;
}

void BaseModel::fromJson(const QJsonObject& json)
{
    if (json.contains("id")) {
        m_id = json["id"].toString();
    }
    
    if (json.contains("createdAt")) {
        m_createdAt = QDateTime::fromString(json["createdAt"].toString(), Qt::ISODate);
    }
    
    if (json.contains("updatedAt")) {
        m_updatedAt = QDateTime::fromString(json["updatedAt"].toString(), Qt::ISODate);
    }
    
    // 移除基础字段，剩余的是模型特定数据
    QJsonObject dataJson = json;
    dataJson.remove("id");
    dataJson.remove("createdAt");
    dataJson.remove("updatedAt");
    dataJson.remove("modelName");
    dataJson.remove("modelVersion");
    
    fromVariant(dataJson.toVariantMap());
}

QString BaseModel::toJsonString() const
{
    QJsonDocument doc(toJson());
    return QString::fromUtf8(doc.toJson(QJsonDocument::Compact));
}

bool BaseModel::fromJsonString(const QString& jsonString)
{
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
    if (doc.isNull() || !doc.isObject()) {
        return false;
    }
    
    fromJson(doc.object());
    return true;
}

QString BaseModel::id() const
{
    return m_id;
}

void BaseModel::setId(const QString& id)
{
    m_id = id;
}

QDateTime BaseModel::createdAt() const
{
    return m_createdAt;
}

void BaseModel::setCreatedAt(const QDateTime& dateTime)
{
    m_createdAt = dateTime;
}

QDateTime BaseModel::updatedAt() const
{
    return m_updatedAt;
}

void BaseModel::setUpdatedAt(const QDateTime& dateTime)
{
    m_updatedAt = dateTime;
}

bool BaseModel::equals(const BaseModel& other) const
{
    if (modelName() != other.modelName() || modelVersion() != other.modelVersion()) {
        return false;
    }
    
    return m_id == other.id();
}

uint BaseModel::hash() const
{
    return qHash(m_id);
}
