#ifndef RIBBONMANAGER_H
#define RIBBONMANAGER_H

#include <QObject>
#include <QMap>
#include <QString>
#include <QColor>
#include <QAction>

class SARibbonBar;
class SARibbonCategory;
class SARibbonPannel;
class SARibbonContextCategory;

/**
 * @brief Ribbon管理器类，负责管理Ribbon界面
 * 
 * RibbonManager提供了Ribbon界面的创建、管理和自定义功能，
 * 包括标签页、面板和上下文标签页的管理。
 */
class RibbonManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param ribbonBar Ribbon控件指针
     */
    explicit RibbonManager(SARibbonBar* ribbonBar);

    /**
     * @brief 析构函数
     */
    ~RibbonManager();

    /**
     * @brief 初始化Ribbon界面
     */
    void initialize();

    /**
     * @brief 创建主页标签页
     * @return 创建的标签页指针
     */
    SARibbonCategory* createHomeTab();

    /**
     * @brief 创建编辑标签页
     * @return 创建的标签页指针
     */
    SARibbonCategory* createEditTab();

    /**
     * @brief 创建视图标签页
     * @return 创建的标签页指针
     */
    SARibbonCategory* createViewTab();

    /**
     * @brief 创建工具标签页
     * @return 创建的标签页指针
     */
    SARibbonCategory* createToolsTab();

    /**
     * @brief 创建帮助标签页
     * @return 创建的标签页指针
     */
    SARibbonCategory* createHelpTab();

    /**
     * @brief 添加自定义标签页
     * @param categoryName 标签页名称
     * @return 创建的标签页指针
     */
    SARibbonCategory* addCategory(const QString& categoryName);

    /**
     * @brief 移除标签页
     * @param categoryName 标签页名称
     * @return 是否成功移除
     */
    bool removeCategory(const QString& categoryName);

    /**
     * @brief 添加面板
     * @param categoryName 标签页名称
     * @param pannelName 面板名称
     * @return 创建的面板指针
     */
    SARibbonPannel* addPannel(const QString& categoryName, const QString& pannelName);

    /**
     * @brief 添加动作到面板
     * @param categoryName 标签页名称
     * @param pannelName 面板名称
     * @param action 动作指针
     */
    void addActionToPannel(const QString& categoryName, const QString& pannelName, QAction* action);

    /**
     * @brief 添加上下文标签页
     * @param contextName 上下文标签页名称
     * @param color 上下文标签页颜色
     * @return 创建的上下文标签页指针
     */
    SARibbonContextCategory* addContextCategory(const QString& contextName, const QColor& color);

    /**
     * @brief 设置上下文标签页可见性
     * @param contextName 上下文标签页名称
     * @param visible 是否可见
     */
    void setContextCategoryVisible(const QString& contextName, bool visible);

    /**
     * @brief 获取标签页
     * @param categoryName 标签页名称
     * @return 标签页指针
     */
    SARibbonCategory* getCategory(const QString& categoryName) const;

    /**
     * @brief 获取面板
     * @param categoryName 标签页名称
     * @param pannelName 面板名称
     * @return 面板指针
     */
    SARibbonPannel* getPannel(const QString& categoryName, const QString& pannelName) const;

    /**
     * @brief 获取上下文标签页
     * @param contextName 上下文标签页名称
     * @return 上下文标签页指针
     */
    SARibbonContextCategory* getContextCategory(const QString& contextName) const;

    /**
     * @brief 设置Ribbon样式
     * @param style 样式值
     */
    void setRibbonStyle(int style);

    /**
     * @brief 切换紧凑模式
     * @param compact 是否启用紧凑模式
     */
    void setCompactMode(bool compact);

private:
    SARibbonBar* m_ribbonBar;
    QMap<QString, SARibbonCategory*> m_categories;
    QMap<QString, QMap<QString, SARibbonPannel*>> m_pannels;
    QMap<QString, SARibbonContextCategory*> m_contextCategories;
};

#endif // RIBBONMANAGER_H
