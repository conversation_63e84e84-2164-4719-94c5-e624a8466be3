#ifndef ERRORHANDLER_H
#define ERRORHANDLER_H

#include <QString>
#include <QObject>
#include <functional>
#include <exception>

/**
 * @brief 错误严重程度枚举
 */
enum class ErrorSeverity {
    Info,       // 信息
    Warning,    // 警告
    Error,      // 错误
    Critical,   // 严重错误
    Fatal       // 致命错误
};

/**
 * @brief 错误处理器类，提供统一的错误处理机制
 * 
 * ErrorHandler提供了一个中心化的错误处理系统，
 * 支持自定义错误回调和异常处理。
 */
class ErrorHandler : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 错误信息结构
     */
    struct ErrorInfo {
        QString message;        // 错误消息
        ErrorSeverity severity; // 错误严重程度
        QString source;         // 错误源
        QString timestamp;      // 时间戳
        int errorCode;          // 错误代码
        QString stackTrace;     // 堆栈跟踪
        
        ErrorInfo() : severity(ErrorSeverity::Error), errorCode(0) {}
    };

    /**
     * @brief 错误回调函数类型
     */
    using ErrorCallback = std::function<void(const ErrorInfo&)>;

    /**
     * @brief 获取ErrorHandler的单例实例
     * @return ErrorHandler的单例实例指针
     */
    static ErrorHandler* instance();

    /**
     * @brief 处理错误
     * @param message 错误消息
     * @param severity 错误严重程度
     * @param source 错误源
     * @param errorCode 错误代码
     */
    static void handleError(const QString& message, 
                           ErrorSeverity severity = ErrorSeverity::Error,
                           const QString& source = QString(),
                           int errorCode = 0);

    /**
     * @brief 处理异常
     * @param e 异常对象
     * @param source 错误源
     */
    static void handleException(const std::exception& e, const QString& source = QString());

    /**
     * @brief 设置错误回调函数
     * @param callback 回调函数
     */
    static void setErrorCallback(const ErrorCallback& callback);

    /**
     * @brief 设置是否启用堆栈跟踪
     * @param enabled 是否启用
     */
    static void setStackTraceEnabled(bool enabled);

    /**
     * @brief 设置最小错误级别
     * @param severity 最小错误级别
     */
    static void setMinimumSeverity(ErrorSeverity severity);

    /**
     * @brief 获取错误严重程度字符串
     * @param severity 错误严重程度
     * @return 严重程度字符串
     */
    static QString severityToString(ErrorSeverity severity);

    /**
     * @brief 获取当前堆栈跟踪
     * @return 堆栈跟踪字符串
     */
    static QString getCurrentStackTrace();

signals:
    /**
     * @brief 错误处理信号
     * @param errorInfo 错误信息
     */
    void errorOccurred(const ErrorInfo& errorInfo);

private:
    /**
     * @brief 构造函数（私有，确保单例模式）
     */
    ErrorHandler();

    /**
     * @brief 析构函数
     */
    ~ErrorHandler();

    // 禁止拷贝和赋值
    ErrorHandler(const ErrorHandler&) = delete;
    ErrorHandler& operator=(const ErrorHandler&) = delete;

    /**
     * @brief 内部错误处理方法
     * @param errorInfo 错误信息
     */
    void handleErrorInternal(const ErrorInfo& errorInfo);

    static ErrorHandler* s_instance;
    ErrorCallback m_errorCallback;
    bool m_stackTraceEnabled;
    ErrorSeverity m_minimumSeverity;
};

/**
 * @brief RAII异常安全保护类
 */
class ExceptionGuard
{
public:
    /**
     * @brief 构造函数
     * @param source 错误源
     */
    explicit ExceptionGuard(const QString& source);

    /**
     * @brief 析构函数
     */
    ~ExceptionGuard();

    /**
     * @brief 执行函数并捕获异常
     * @param func 要执行的函数
     * @return 执行是否成功
     */
    template<typename Func>
    bool execute(Func&& func) {
        try {
            func();
            return true;
        } catch (const std::exception& e) {
            ErrorHandler::handleException(e, m_source);
            return false;
        } catch (...) {
            ErrorHandler::handleError("Unknown exception occurred", 
                                    ErrorSeverity::Error, m_source);
            return false;
        }
    }

private:
    QString m_source;
};

// 便利宏定义
#define HANDLE_ERROR(message) \
    ErrorHandler::handleError(message, ErrorSeverity::Error, __FUNCTION__)

#define HANDLE_WARNING(message) \
    ErrorHandler::handleError(message, ErrorSeverity::Warning, __FUNCTION__)

#define HANDLE_CRITICAL(message) \
    ErrorHandler::handleError(message, ErrorSeverity::Critical, __FUNCTION__)

#define EXCEPTION_GUARD(source) \
    ExceptionGuard guard(source)

#define SAFE_EXECUTE(func) \
    guard.execute([&]() { func; })

#endif // ERRORHANDLER_H
