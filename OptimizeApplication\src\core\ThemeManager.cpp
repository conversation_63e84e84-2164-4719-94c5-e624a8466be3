#include "ThemeManager.h"
#include <QMutexLocker>
#include <QDebug>
#include <QOperatingSystemVersion>
#include <QPalette>
#include <QApplication>

// Initialize static members
ThemeManager* ThemeManager::m_instance = nullptr;
QMutex ThemeManager::m_instanceMutex;

ThemeManager* ThemeManager::instance()
{
    // Thread-safe singleton instance creation
    QMutexLocker locker(&m_instanceMutex);
    
    if (!m_instance) {
        m_instance = new ThemeManager();
    }
    
    return m_instance;
}

ThemeManager::ThemeManager(QObject *parent)
    : QObject(parent)
    , m_currentTheme(ThemeType::Light)
    , m_highContrastMode(false)
{
    // Initialize default light theme
    initializeLightTheme();
}

void ThemeManager::initializeLightTheme()
{
    m_currentPalette = {
        {ColorRole::Primary, QColor(33, 150, 243)},       // Blue
        {ColorRole::Secondary, QColor(100, 181, 246)},    // Light Blue
        {ColorRole::Background, QColor(255, 255, 255)},   // White
        {ColorRole::Foreground, QColor(240, 240, 240)},   // Light Gray
        {ColorRole::Accent, QColor(0, 188, 212)},         // Cyan
        {ColorRole::Text, QColor(0, 0, 0)},               // Black
        {ColorRole::ButtonBackground, QColor(230, 230, 230)}, // Light Gray
        {ColorRole::ButtonText, QColor(0, 0, 0)},         // Black
        {ColorRole::Highlight, QColor(33, 150, 243)}      // Blue
    };
}

void ThemeManager::initializeDarkTheme()
{
    m_currentPalette = {
        {ColorRole::Primary, QColor(63, 81, 181)},        // Indigo
        {ColorRole::Secondary, QColor(97, 97, 97)},       // Dark Gray
        {ColorRole::Background, QColor(48, 48, 48)},      // Dark Background
        {ColorRole::Foreground, QColor(66, 66, 66)},      // Darker Gray
        {ColorRole::Accent, QColor(0, 150, 136)},         // Teal
        {ColorRole::Text, QColor(255, 255, 255)},         // White
        {ColorRole::ButtonBackground, QColor(88, 88, 88)},// Dark Button
        {ColorRole::ButtonText, QColor(255, 255, 255)},   // White
        {ColorRole::Highlight, QColor(63, 81, 181)}       // Indigo
    };
}

void ThemeManager::initializeSystemTheme()
{
    // Detect system theme based on OS
    auto osVersion = QOperatingSystemVersion::current();
    
    // Windows 10+ dark mode detection
    if (osVersion.type() == QOperatingSystemVersion::Windows &&
        osVersion.majorVersion() >= 10) {
        initializeDarkTheme();
    } else {
        initializeLightTheme();
    }
}

void ThemeManager::setTheme(ThemeType type)
{
    switch (type) {
        case ThemeType::Light:
            initializeLightTheme();
            break;
        case ThemeType::Dark:
            initializeDarkTheme();
            break;
        case ThemeType::System:
            initializeSystemTheme();
            break;
        default:
            initializeLightTheme();
    }

    m_currentTheme = type;
    applyPalette();
    emit themeChanged(type);
}

void ThemeManager::setCustomTheme(const QJsonObject& themeData)
{
    // Parse custom theme JSON
    try {
        m_currentPalette = {
            {ColorRole::Primary, QColor(themeData["primary"].toString())},
            {ColorRole::Secondary, QColor(themeData["secondary"].toString())},
            {ColorRole::Background, QColor(themeData["background"].toString())},
            {ColorRole::Foreground, QColor(themeData["foreground"].toString())},
            {ColorRole::Accent, QColor(themeData["accent"].toString())},
            {ColorRole::Text, QColor(themeData["text"].toString())},
            {ColorRole::ButtonBackground, QColor(themeData["buttonBackground"].toString())},
            {ColorRole::ButtonText, QColor(themeData["buttonText"].toString())},
            {ColorRole::Highlight, QColor(themeData["highlight"].toString())}
        };

        m_currentTheme = ThemeType::Custom;
        applyPalette();
        emit themeChanged(ThemeType::Custom);
    } catch (const std::exception& e) {
        qWarning() << "Failed to set custom theme:" << e.what();
        initializeLightTheme();
    }
}

ThemeType ThemeManager::getCurrentTheme() const
{
    return m_currentTheme;
}

QColor ThemeManager::getColor(ColorRole role) const
{
    return m_currentPalette.value(role, QColor()); // Default to invalid color if not found
}

void ThemeManager::applyStyle(const QString& styleName)
{
    QStyle* style = QStyleFactory::create(styleName);
    if (style) {
        QApplication::setStyle(style);
    }
}

void ThemeManager::applyPalette()
{
    QPalette palette;
    
    // Set colors based on current palette
    palette.setColor(QPalette::Window, getColor(ColorRole::Background));
    palette.setColor(QPalette::WindowText, getColor(ColorRole::Text));
    palette.setColor(QPalette::Base, getColor(ColorRole::Foreground));
    palette.setColor(QPalette::AlternateBase, getColor(ColorRole::Secondary));
    palette.setColor(QPalette::ToolTipBase, getColor(ColorRole::Accent));
    palette.setColor(QPalette::ToolTipText, getColor(ColorRole::Text));
    palette.setColor(QPalette::Text, getColor(ColorRole::Text));
    palette.setColor(QPalette::Button, getColor(ColorRole::ButtonBackground));
    palette.setColor(QPalette::ButtonText, getColor(ColorRole::ButtonText));
    palette.setColor(QPalette::BrightText, getColor(ColorRole::Highlight));
    palette.setColor(QPalette::Highlight, getColor(ColorRole::Highlight));
    palette.setColor(QPalette::HighlightedText, getColor(ColorRole::Text));

    QApplication::setPalette(palette);
}

QJsonObject ThemeManager::exportCurrentTheme() const
{
    QJsonObject themeData;
    
    // Convert color palette to JSON
    for (auto it = m_currentPalette.begin(); it != m_currentPalette.end(); ++it) {
        QString key;
        switch (it.key()) {
            case ColorRole::Primary: key = "primary"; break;
            case ColorRole::Secondary: key = "secondary"; break;
            case ColorRole::Background: key = "background"; break;
            case ColorRole::Foreground: key = "foreground"; break;
            case ColorRole::Accent: key = "accent"; break;
            case ColorRole::Text: key = "text"; break;
            case ColorRole::ButtonBackground: key = "buttonBackground"; break;
            case ColorRole::ButtonText: key = "buttonText"; break;
            case ColorRole::Highlight: key = "highlight"; break;
        }
        themeData[key] = it.value().name();
    }

    return themeData;
}

void ThemeManager::importThemeFromFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Could not open theme file:" << filePath;
        return;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    if (!doc.isObject()) {
        qWarning() << "Invalid theme file format";
        return;
    }

    setCustomTheme(doc.object());
}

void ThemeManager::setHighContrastMode(bool enabled)
{
    m_highContrastMode = enabled;

    if (enabled) {
        // High contrast theme
        m_currentPalette = {
            {ColorRole::Primary, QColor(0, 0, 0)},        // Black
            {ColorRole::Secondary, QColor(255, 255, 255)},// White
            {ColorRole::Background, QColor(0, 0, 0)},     // Black
            {ColorRole::Foreground, QColor(255, 255, 255)},// White
            {ColorRole::Accent, QColor(255, 255, 0)},     // Yellow
            {ColorRole::Text, QColor(255, 255, 255)},     // White
            {ColorRole::ButtonBackground, QColor(0, 0, 0)},// Black
            {ColorRole::ButtonText, QColor(255, 255, 255)},// White
            {ColorRole::Highlight, QColor(255, 255, 0)}   // Yellow
        };
    } else {
        // Revert to current theme
        setTheme(m_currentTheme);
    }

    applyPalette();
    emit colorPaletteUpdated();
}

bool ThemeManager::isHighContrastMode() const
{
    return m_highContrastMode;
} 