#ifndef IFILEEXAMPLE_H
#define IFILEEXAMPLE_H

#include <QObject>
#include <QString>
#include <QStringList>
#include "InputData.h"

/**
 * @brief IFileExample class provides comprehensive .i file processing examples
 *
 * This class demonstrates various .i file operations including:
 * - Loading and parsing .i files
 * - Saving and exporting .i files with different formats
 * - Creating new .i files programmatically
 * - Batch processing multiple .i files
 * - File validation and consistency checking
 */
class IFileExample : public QObject
{
    Q_OBJECT

public:
    explicit IFileExample(QObject *parent = nullptr);
    ~IFileExample();

    /**
     * @brief Load and parse a .i file
     * @param filePath Path to the .i file
     * @return True if successful, false otherwise
     */
    bool loadIFile(const QString &filePath);

    /**
     * @brief Save a .i file
     * @param filePath Path to save the file
     * @return True if successful, false otherwise
     */
    bool saveIFile(const QString &filePath);

    /**
     * @brief Create a new .i file with sample data
     * @param problemType Type of problem to create
     * @return True if successful, false otherwise
     */
    bool createSampleIFile(InputFileType problemType);

    /**
     * @brief Process multiple .i files in a directory
     * @param directoryPath Path to the directory containing .i files
     * @param recursive Whether to search recursively
     * @return Number of successfully processed files
     */
    int batchProcessIFiles(const QString &directoryPath, bool recursive = false);

    /**
     * @brief Validate a .i file
     * @param filePath Path to the .i file
     * @return List of validation errors, empty if valid
     */
    QStringList validateIFile(const QString &filePath);

    /**
     * @brief Get the current input data
     * @return Current input data
     */
    const InputData& getInputData() const;

    /**
     * @brief Get the errors from the last operation
     * @return List of errors
     */
    QStringList getErrors() const;

    /**
     * @brief Clear all data
     */
    void clear();

private:
    /**
     * @brief Create a sample NC Welander problem
     */
    void createNCWelanderSample();

    /**
     * @brief Create a sample discharge air problem
     */
    void createDischargeAirSample();

    /**
     * @brief Create a sample critical flow problem
     */
    void createCriticalFlowSample();

    /**
     * @brief Add standard components to a problem
     */
    void addStandardComponents();

    /**
     * @brief Add standard control cards to a problem
     */
    void addStandardControlCards();

    InputData m_inputData;        ///< Current input data
    QStringList m_errors;         ///< Errors from the last operation
};

#endif // IFILEEXAMPLE_H 