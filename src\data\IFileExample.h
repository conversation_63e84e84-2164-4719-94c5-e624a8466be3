#ifndef IFILEEXAMPLE_H
#define IFILEEXAMPLE_H

#include <QObject>
#include <QString>
#include <QStringList>
#include "InputData.h"

/**
 * @brief IFileExample class provides comprehensive .i file processing examples
 * 
 * This class demonstrates various .i file operations including:
 * - Loading and parsing .i files
 * - Saving and exporting .i files with different formats
 * - Creating new .i files programmatically
 * - Batch processing multiple .i files
 * - File validation and consistency checking
 */
class IFileExample : public QObject
{
    Q_OBJECT

public:
    explicit IFileExample(QObject *parent = nullptr);
    ~IFileExample();

    // Main demonstration methods
    bool demonstrateIFileReadWrite(const QString& inputFile = "input/NC_Welander.i");
    bool demonstrateCreateNewIFile(const QString& outputFile = "output/new_test_problem.i");
    bool demonstrateBatchIFileProcessing(const QString& inputDir = "input");
    
    // Individual operation methods
    bool loadAndAnalyzeIFile(const QString& filePath);
    bool saveIFileWithOptions(const QString& inputFile, const QString& outputFile, 
                             const InputData::IFileFormatOptions& options);
    bool createSampleIFile(const QString& outputFile);
    
    // Validation and analysis methods
    bool validateIFile(const QString& filePath);
    QStringList checkIFileConsistency(const QString& filePath);
    QString generateIFilePreview(const QString& filePath, int maxLines = 20);
    
    // Utility methods
    void displayFileStatistics(const InputData& data);
    void displayControlCardInfo(const InputData& data);
    void displayUnitSystemInfo(const InputData& data);
    void displayComponentStatistics(const InputData& data);
    
    // Getters for last processed data
    const InputData& getLastProcessedData() const { return m_lastProcessedData; }
    QString getLastError() const { return m_lastError; }
    bool hasError() const { return !m_lastError.isEmpty(); }

signals:
    void fileProcessed(const QString& filePath, bool success);
    void operationCompleted(const QString& operation, bool success);
    void logMessage(const QString& message);

private:
    InputData m_lastProcessedData;
    QString m_lastError;
    
    // Helper methods
    void clearError();
    void setError(const QString& error);
    void logInfo(const QString& message);
    void logError(const QString& message);
    
    // Default format options
    InputData::IFileFormatOptions getDefaultFormatOptions() const;
};

#endif // IFILEEXAMPLE_H 