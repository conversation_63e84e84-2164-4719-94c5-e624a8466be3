#include "ConfigManager.h"
#include <QStandardPaths>
#include <QDir>
#include <QMutexLocker>

ConfigManager::ConfigManager(const QString& configPath)
    : QObject(nullptr)
    , m_settings(nullptr)
{
    if (configPath.isEmpty()) {
        QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
        QDir().mkpath(configDir);
        m_configPath = configDir + "/config.ini";
    } else {
        m_configPath = configPath;
    }

    m_settings = new QSettings(m_configPath, QSettings::IniFormat, this);
}

ConfigManager::~ConfigManager()
{
    save();
}

bool ConfigManager::load()
{
    QMutexLocker locker(&m_mutex);

    if (!m_settings) {
        return false;
    }

    m_cache.clear();
    
    // 加载所有配置到缓存
    QStringList keys = m_settings->allKeys();
    for (const QString& key : keys) {
        m_cache[key] = m_settings->value(key);
    }

    return true;
}

bool ConfigManager::save()
{
    QMutexLocker locker(&m_mutex);

    if (!m_settings) {
        return false;
    }

    // 将缓存中的配置写入文件
    for (auto it = m_cache.begin(); it != m_cache.end(); ++it) {
        m_settings->setValue(it.key(), it.value());
    }

    m_settings->sync();
    return m_settings->status() == QSettings::NoError;
}

QVariant ConfigManager::getValue(const QString& key, const QVariant& defaultValue) const
{
    QMutexLocker locker(&m_mutex);

    if (m_cache.contains(key)) {
        return m_cache[key];
    }

    if (m_settings && m_settings->contains(key)) {
        QVariant value = m_settings->value(key, defaultValue);
        m_cache[key] = value;
        return value;
    }

    return defaultValue;
}

void ConfigManager::setValue(const QString& key, const QVariant& value)
{
    QMutexLocker locker(&m_mutex);

    QVariant oldValue = m_cache.value(key);
    m_cache[key] = value;

    if (oldValue != value) {
        emit valueChanged(key, value);
    }
}

bool ConfigManager::containsKey(const QString& key) const
{
    QMutexLocker locker(&m_mutex);

    return m_cache.contains(key) || (m_settings && m_settings->contains(key));
}

void ConfigManager::removeKey(const QString& key)
{
    QMutexLocker locker(&m_mutex);

    m_cache.remove(key);
    if (m_settings) {
        m_settings->remove(key);
    }
}

void ConfigManager::clear()
{
    QMutexLocker locker(&m_mutex);

    m_cache.clear();
    if (m_settings) {
        m_settings->clear();
    }
}

QStringList ConfigManager::keys() const
{
    QMutexLocker locker(&m_mutex);

    QStringList allKeys = m_cache.keys();
    
    if (m_settings) {
        QStringList settingsKeys = m_settings->allKeys();
        for (const QString& key : settingsKeys) {
            if (!allKeys.contains(key)) {
                allKeys.append(key);
            }
        }
    }

    return allKeys;
}

QString ConfigManager::configPath() const
{
    return m_configPath;
}
