# Interface Module

This document contains all interface related documentation organized by hierarchy.

---

## interface

# interface

Specifies how function evaluations will be performed in order to map the variables into the responses.

**Topics**

block

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [id_interface](interface-id_interface.html) | Name the interface block; helpful when there are multiple  
Optional | [analysis_drivers](interface-analysis_drivers.html) | Define how Dakota should run a function evaluation  
Optional | [algebraic_mappings](interface-algebraic_mappings.html) | Use AMPL to define algebraic input-output mappings  
Optional | [failure_capture](interface-failure_capture.html) | Determine how Dakota responds to analysis driver failure  
Optional | [deactivate](interface-deactivate.html) | Deactivate Dakota interface features for simplicity or efficiency  
Optional (Choose One) | Optional (Choose One) | [batch](interface-batch.html) | Perform evaluations in batches  
[asynchronous](interface-asynchronous.html) | Specify local evaluation or analysis concurrency  
Optional | [evaluation_servers](interface-evaluation_servers.html) | Specify the number of evaluation servers when Dakota is run in parallel  
Optional | [evaluation_scheduling](interface-evaluation_scheduling.html) | Specify the scheduling of concurrent evaluations when Dakota is run in parallel  
Optional | [processors_per_evaluation](interface-processors_per_evaluation.html) | Specify the number of processors per evaluation server when Dakota is run in parallel  
Optional | [analysis_servers](interface-analysis_servers.html) | Specify the number of analysis servers when Dakota is run in parallel  
Optional | [analysis_scheduling](interface-analysis_scheduling.html) | Specify the scheduling of concurrent analyses when Dakota is run in parallel  
  
**Description**

The interface section in a Dakota input file specifies how function evaluations will be performed in order to map the variables into the responses. The term “interface” refers to the bridge between Dakota and the underlying simulation code.

In this context, a “function evaluation” is the series of operations that takes the variables and computes the responses. This can be comprised of one or many codes, scripts, and glue, which are generically referred to as “analysis drivers” (and optional input/output filters). The mapping actions of `[analysis_drivers](../../usingdakota/reference/interface-analysis_drivers.html)` may be combined with explicit `[algebraic_mappings](../../usingdakota/reference/interface-algebraic_mappings.html)`

_Parallelism Options_

  * The `[asynchronous](../../usingdakota/reference/interface-asynchronous.html)` keyword enables concurrent local function evaluations or analyses via operating system process management. Its child keywords allow tailoring the evaluation and analysis concurency.

  * The evaluation servers, scheduling mode (master, peer static or dynamic), and processor keywords allow a user to override Dakota’s default evaluation configuration when running in parallel (MPI) mode.

  * The analysis servers and scheduling mode (master, peer static or dynamic) keywords allow a user to override Dakota’s default analysis configuration when running in parallel (MPI) mode.

Note: see `[direct](../../usingdakota/reference/interface-analysis_drivers-direct.html)` for the specific `processors_per_analysis` specification supported for direct interfaces.

The ParallelLibrary class and the [Parallel Computing section](../advanced/parallelcomputing.html#parallel) provide additional details on parallel configurations.

**Theory**

Function evaluations are performed using either interfaces to simulation codes, algebraic mappings, or a combination of the two.

When employing mappings with simulation codes, the interface invokes the simulation using either forks, direct function invocations, or computational grid invocations.

  * In the fork case, Dakota will treat the simulation as a black-box and communication between Dakota and the simulation occurs through parameter and result files. This is the most common case.

  * In the direct function case, the simulation is internal to Dakota and communication occurs through the function parameter list. The direct case can involve linked simulation codes or test functions which are compiled into the Dakota executable. The test functions allow for rapid testing of algorithms without process creation overhead or engineering simulation expense.

  * The grid case is deprecated, but was an experiment in interfacing Dakota to distributed computing engines.

When employing algebraic mappings, the AMPL solver library [[Gay97](../../misc/bibliography.html#id103 "D. M. Gay. Hooking your solver to AMPL. Technical Report Technical Report 97-4-06, Bell Laboratories, Murray Hill, NJ, 1997. Available online as http://www.ampl.com/REFS/HOOKING/index.html and http://www.ampl.com/REFS/hooking2.pdf and http://www.ampl.com/REFS/hooking2.ps.gz.")] is used to evaluate a directed acyclic graph (DAG) specification from a separate stub.nl file. Separate stub.col and stub.row files are also required to declare the string identifiers of the subset of inputs and outputs, respectively, that will be used in the algebraic mappings.


---

### interface → algebraic_mappings

# algebraic_mappings

Use AMPL to define algebraic input-output mappings

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no algebraic mappings

**Description**

Dakota can evaluate algebraic input-output mappings using AMPL [[FGK03](../../misc/bibliography.html#id95 "R. Fourer, D. M. Gay, and B. W. Kernighan. AMPL: A Modeling Language for Mathematical Programming, 2nd ed. Duxbury Press/Brooks/Cole Publishing Co., Pacific Grove, CA, 2003. For small examples, e.g., at most 300 variables, a student version of AMPL suffices; see \\texttt http://www.ampl.com/DOWNLOADS.")]. The mappings are expressed in 3 files: `stub`.nl, `stub`.col, and `col,stub`.row, where `row,stub` is a particular root name describing a particular problem. The file names are communicated to Dakota using the `algebraic_mappings` keyword. It may either specify the full `stub`.nl filename, or alternatively, just the `nlstub` basename.

Dakota then extracts the input and output identifier strings from `stub`.col and `colstub`.row and employs the AMPL solver library [[Gay97](../../misc/bibliography.html#id103 "D. M. Gay. Hooking your solver to AMPL. Technical Report Technical Report 97-4-06, Bell Laboratories, Murray Hill, NJ, 1997. Available online as http://www.ampl.com/REFS/HOOKING/index.html and http://www.ampl.com/REFS/hooking2.pdf and http://www.ampl.com/REFS/hooking2.ps.gz.")] to process the directed acyclic graphc (DAG) specification in `stub`.nl. The variable and objective function names declared within AMPL should be a subset of the variable and response descriptors specified in the `[variables](../../usingdakota/reference/variables.html)` and `[responses](../../usingdakota/reference/responses.html)` blocks. Ordering is not important, as Dakota will reorder data as needed.

**Examples**

An interface employing both algebraic and simulation-based mappings. The results from the individual algebraic and simulation mappings are overlaid based on the variable and response descriptors used by the individual mappings.

    interface,
     algebraic_mappings = 'ampl/fma.nl'
     fork
       analysis_driver = 'text_book'
       parameters_file = 'tb.in'
       results_file    = 'tb.out'


---

### interface → analysis_drivers

# analysis_drivers

Define how Dakota should run a function evaluation

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [input_filter](interface-analysis_drivers-input_filter.html) | Run a pre-processing script before the analysis drivers  
Optional | [output_filter](interface-analysis_drivers-output_filter.html) | Run a post-processing script after the analysis drivers  
Required (Choose One) | Interface Type | [system](interface-analysis_drivers-system.html) | (Not recommended) Launch analysis drivers with a system call  
[fork](interface-analysis_drivers-fork.html) | Launch analysis drivers using fork command  
[direct](interface-analysis_drivers-direct.html) | Run analysis drivers that are linked-to or compiled-with Dakota  
[plugin](interface-analysis_drivers-plugin.html) | Dynamically load a plugin analysis driver  
[matlab](interface-analysis_drivers-matlab.html) | Run Matlab through a direct interface - requires special Dakota build  
[python](interface-analysis_drivers-python.html) | Run Python through a Pybind11-based direct interface - requires a special Dakota build  
[legacy_python](interface-analysis_drivers-legacy_python.html) | Run Python through a deprecated C-based direct interface - requires a special Dakota build  
[scilab](interface-analysis_drivers-scilab.html) | Run Scilab through a direct interface - requires special Dakota build  
[grid](interface-analysis_drivers-grid.html) | Deprecated grid computing interface  
Optional | [analysis_components](interface-analysis_drivers-analysis_components.html) | Provide additional identifiers to analysis drivers.  
  
**Description**

The `analysis_drivers` keyword provides the names of one or more executable analysis programs or scripts, a.k.a. “drivers” which comprise a function evaluation. The optional and required sub-keywords specify how Dakota will manage directories and files, and run the driver(s).

_Types of Interfaces_

Dakota has two recommended ways of running analysis drivers:

  * as an external processes ( `fork`), or

  * using internal code to couple to the analysis driver ( `direct`)

Other options are available for advanced users, and are not as well documented, supported, or tested:

  * external processes ( `system`)

  * internal coupling ( `python`, `matlab`, `scilab`, `grid`)

_Use Cases_

The internally coupled codes have few options because many of the details are already handled with the coupling. Their behavior is described in the `[direct](../../usingdakota/reference/interface-analysis_drivers-direct.html)` keyword.

For external processes using the `[fork](../../usingdakota/reference/interface-analysis_drivers-fork.html)` keyword,

A function evaluation may comprise:

  * _A single analysis driver_ : Function evaluation, including all pre- and post-processing is contained entirely within a single script/executable.

  * _A single analysis driver with filters_ : Function evaluation is explicitly split into pre-processing (performed by the input filter), analysis, and post-processing (by the output filter).

  * _A single analysis driver with environment variables_ : Function evaluation is contained within one analysis driver, but it requires environment variables to be set before running.

  * _Multiple analysis drivers_ : Drivers are run sequentially or concurrently (See the `asynchronous` keyword) and can have any of the above options as well.

For fork and system interfaces, the analysis_driver list contains the names of one or more executable programs or scripts taking parameters files as input and producing results files as output. The first field in each analysis driver string must be an executable program or script for Dakota to spawn to perform the function evaluation. Drivers support:

>   * One set of nested quotes, for arguments with spaces
> 
>   * Dakota will define special environment variables `DAKOTA_PARAMETERS_FILE` and `DAKOTA_RESULTS_FILE` which can be used in the driver script.
> 
>   * Dakota will replace the tokens {PARAMETERS} and {RESULTS} in an anslysis driver string with the names of the parameters and results files for that analysis/evaluation. Along with the `[verbatim](../../usingdakota/reference/interface-analysis_drivers-fork-verbatim.html)` keyword, which prevents Dakota from appending the names of the parameters and reslts files as command line arguments, this feature provides users with greater control over how their analysis drivers are invoked by Dakota.
> 
>   * Variable definitions preceding the executable program or script, such as ‘MY_VAR=2 run_analysis.sh’ are no longer supported.
> 
> 

For details and examples see the Simulation Interface Components section of the Interfaces chapter of the User’s Manual; for details on the filters and environment variables, see the subsection on Syntax for Filter and Driver Strings.

**Examples**

Examples:

    1. analysis_drivers = 'run_simulation_part1.sh' 'run_simulation_part2.sh'
    
    2. analysis_driver = 'run_simulation.sh -option "option 1"'
    
    3. analysis_driver = 'simulation.exe -option value -dakota_params $DAKOTA_PARAMETERS_FILE -input sim.in -dakota_results_file $DAKOTA_RESULTS_FILE'

**FAQ**

_Where will Dakota look for the analysis_driver?_ Dakota will locate analysis_driver programs first in (or relative to) the present working directory (“.”, the interface-analysis_drivers-fork-work_directory if used, otherwise the directory in which Dakota is started), then the directory from which Dakota is started, then using the system $PATH environment variable (Path% on Windows).

_Where should the driver be located?_ When the driver is a script it is most commonly placed in the same directory as the Dakota input file. When using a `[work_directory](../../usingdakota/reference/interface-analysis_drivers-fork-work_directory.html)`, Dakota will also look for drivers in the specified working directory, so link_files or copy_files may specify the driver to get copied or linked into the work directory. When executable programs are used as drivers, they are often elsewhere on the filesystem. These can be specified using absolute paths, or by prepending the PATH environment variable so Dakota finds them.

_What if Dakota fails to run my analysis_driver?_ Prepend the absolute location of the driver to the PATH environment variable before running Dakota, or specify an absolute path to the driver in the Dakota input file.


---

#### interface → analysis_drivers → analysis_components

# analysis_components

Provide additional identifiers to analysis drivers.

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ no additional identifiers

**Description**

The optional `analysis_components` specification allows the user to provide additional identifiers (e.g., mesh file names) for use by the analysis drivers. This is particularly useful when the same analysis driver is to be reused multiple times for slightly different analyses. The specific content within the strings is open-ended and can involve whatever syntax is convenient for a particular analysis driver. The number of analysis components \\(n_c\\) should be an integer multiple of the number of drivers \\(n_d\\) , and the first \\(n_c/n_d\\) component strings will be passed to the first driver, etc.


---

#### interface → analysis_drivers → direct

# direct

Run analysis drivers that are linked-to or compiled-with Dakota

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [processors_per_analysis](interface-analysis_drivers-direct-processors_per_analysis.html) | Specify the number of processors per analysis when Dakota is run in parallel  
  
**Description**

Direct interfaces are used to compile/link simulation programs into Dakota and to invoke Dakota’s built-in algebraic test problems.

Direct simulation interfaces communicate variable and response data in-core instead of through the filesystem. This typically requires modification to simulator programs so that they can be linked into Dakota; however it can be more efficient due to elimination of external processes and auxilliary simulator output, more accurate due to higher numerics, and more flexible in terms of MPI parallelism.

Direct interfaces are also used to invoke internal test functions that perform parameter to response mappings for simple functions as inexpensively as possible. These problems are compiled directly into the Dakota executable as part of the direct function interface class and are used for algorithm testing.

Dakota supports direct interfaces to a few select simulation codes such as Matlab, Python, and Scilab. Another example is ModelCenter, a commercial simulation management framework from Phoenix Integration. To utilize this interface, a user must first define the simulation specifics within a ModelCenter session and then save these definitions to a ModelCenter configuration file. The `analysis_components` specification provides the means to communicate this configuration file to Dakota’s ModelCenter interface.

**Examples**

The rosenbrock function is available as an executable, which can be launched with `[fork](../../usingdakota/reference/interface-analysis_drivers-fork.html)`, and is also compiled with Dakota. The internal version can be used with:

    interface
      analysis_drivers = 'rosenbrock'
        direct


---

##### interface → analysis_drivers → direct → processors_per_analysis

# processors_per_analysis

Specify the number of processors per analysis when Dakota is run in parallel

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ automatic (see discussion)

**Description**

For direct function interfaces, `processors_per_analysis` can be used to specify multiprocessor analysis partitions. As with the `evaluation_servers`, `analysis_servers`, `evaluation_self_scheduling`, `evaluation_static_scheduling`, `analysis_self_scheduling`, and `analysis_static_scheduling` specifications, `processors_per_analysis` provides a means for the user to override the automatic parallel configuration (refer to ParallelLibrary and the [Parallel Computing section](../advanced/parallelcomputing.html#parallel)) for the number of processors used for each analysis partition.

_Usage Tips_

  * If both `analysis_servers` and `processors_per_analysis` are specified and they are not in agreement, then `analysis_servers` takes precedence.


---

#### interface → analysis_drivers → fork

# fork

Launch analysis drivers using fork command

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [parameters_file](interface-analysis_drivers-fork-parameters_file.html) | Specify the name of the parameters file  
Optional | [results_file](interface-analysis_drivers-fork-results_file.html) | Specify the name of the results file  
Optional | [file_tag](interface-analysis_drivers-fork-file_tag.html) | Tag each parameters & results file name with the function evaluation number  
Optional | [file_save](interface-analysis_drivers-fork-file_save.html) | Keep the parameters & results files after the analysis driver completes  
Optional | [labeled](interface-analysis_drivers-fork-labeled.html) | Requires correct function value labels in results file  
Optional | [aprepro](interface-analysis_drivers-fork-aprepro.html) | Write parameters files in APREPRO syntax  
Optional | [work_directory](interface-analysis_drivers-fork-work_directory.html) | Perform each function evaluation in a separate working directory  
Optional | [allow_existing_results](interface-analysis_drivers-fork-allow_existing_results.html) | Change how Dakota deals with existing results files  
Optional | [verbatim](interface-analysis_drivers-fork-verbatim.html) | Specify the command Dakota uses to launch analysis driver(s) and filters  
  
**Description**

The `fork` interface is the most common means by which Dakota launches a separate application analysis process.

The `fork` interface is recommended over `system` for most analysis drivers that are external to Dakota, i.e., any driver not linked in via the `direct` interface.

The parameters and results file names are passed on the command line to the analysis driver(s). If input/output filters are specified, they will be run before/after the analysis drivers. The `verbatim` keyword is used to modify the default driver/filter commands.

For additional information on invocation syntax, refer to [Simulation Interfaces](../inputfile/interfaces/simulationinterfaces.html#interfaces-sim).

**Examples**

Spawn (fork) an external executable/script called ‘rosenbrock’ which reads variables from params.in and writes responses to results.out. Preserve the analysis files for each function evaluation with tag and save.

    interface
      analysis_drivers = 'rosenbrock'
        fork
          parameters_file = 'params.in'
          results_file   = 'results.out'
          file_tag
          file_save


---

##### interface → analysis_drivers → fork → allow_existing_results

# allow_existing_results

Change how Dakota deals with existing results files

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ results files removed before each evaluation

**Description**

By default Dakota will remove existing results files before invoking the `analysis_driver` to avoid problems created by stale files in the current directory. To override this behavior and not delete existing files, specify `allow_existing_results`.


---

##### interface → analysis_drivers → fork → aprepro

# aprepro

Write parameters files in APREPRO syntax

**Topics**

file_formats

**Specification**

  * _Alias:_ dprepro

  * _Arguments:_ None

  * _Default:_ standard parameters file format

**Description**

The format of data in the parameters files can be modified for direct usage with the APREPRO pre-processing tool [[Sja92](../../misc/bibliography.html#id265 "G. D. Sjaardema. APREPRO: an algebraic preprocessor for parameterizing finite element analyses. Technical Report SAND92-2291, Sandia National Laboratories, Albuquerque, NM, 1992.")] using the `aprepro` specification

Without this keyword, the parameters file are written in DPrePro format. [DPrePro](../../externaltools/dpreproandpyprepro.html#interfaces-dprepro-and-pyprepro) is a utility included with Dakota.


---

##### interface → analysis_drivers → fork → file_save

# file_save

Keep the parameters & results files after the analysis driver completes

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ file cleanup

**Description**

If `file_save` is used, Dakota will not delete the parameters and results files after the function evaluation is completed.

The default behavior is NOT to save these files.

If `file_tag` is not specified and the saved files would be overwritten by a future evaluation, Dakota renames them after the analysis driver has run by tagging them with the evaluation number.

File saving is most useful when debugging the data communication between Dakota and the simulation.


---

##### interface → analysis_drivers → fork → file_tag

# file_tag

Tag each parameters & results file name with the function evaluation number

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no tagging

**Description**

If this keyword is used, Dakota will append a period and the function evaluation number to the names of the parameter and results files.

_Default Behavior_ If this keyword is omitted, the default is no file tagging.

_Usage Tips_

  * File tagging is most useful when multiple function evaluations are running simultaneously using files in a shared disk space. The analysis driver will be able to infer the function evaluation number from the file names.

  * Note that when the `file_save` keyword is used, Dakota automatically renames parameters and results files, giving them tags after execution of the analysis driver if they otherwise would be overwritten by the next evaluation.

**Examples**

If the following is included in the `interface` section of the Dakota input:

    parameters_file = params.in
    results_file = results.out
    file_tag

Then for the 3rd evaluation, Dakota will write `params`.in.3, and will expect `results`.out.3 to be written by the analysis driver.


---

##### interface → analysis_drivers → fork → labeled

# labeled

Requires correct function value labels in results file

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ Function value labels optional

**Description**

The `labeled` keyword directs Dakota to enforce a stricter results file format and enables more detailed error reporting.

When the `labeled` keyword is used, function values in results files must be accompanied by their corresponding descriptors. If the user did not supply response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` in her Dakota input file, then Dakota auto-generated descriptors are expected.

Distinct error messages are emitted for function values that are out-of-order, repeated, or missing. Labels that appear without a function value and unexpected data are also reported as errors. Dakota attempts to report all errors in a results file, not just the first it encounters. After reporting results file errors, Dakota aborts.

Labels for analytic gradients and Hessians currently are not supported.

Although the `labeled` keyword is optional, its use is recommended to help catch and identify problems with results files. The User’s Manual contains further information about the results file format.

_Default Behavior_ <p> By default, Dakota does not require labels for function values, and ignores them if they are present.</p>


---

##### interface → analysis_drivers → fork → parameters_file

# parameters_file

Specify the name of the parameters file

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ Unix temp files

**Description**

The parameters file is used by Dakota to pass the parameter values to the analysis driver. The name of the file can be optionally specified using the `parameters_file` keyword.

If this is not specified, the default data transfer files are temporary files with system-generated names (e.g., `/tmp/dakota_params_aaaa0886`).


---

##### interface → analysis_drivers → fork → results_file

# results_file

Specify the name of the results file

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ Unix temp files

**Description**

The results file must be written by the analysis driver. It is read by Dakota to determine the response values for each function evaluation.

The name of the file can be optionally specified using the `results_file` keyword.

If this is not specified, the default data transfer files are temporary files with system-generated names (e.g., `/tmp/dakota_results_aaaa0886`).


---

##### interface → analysis_drivers → fork → verbatim

# verbatim

Specify the command Dakota uses to launch analysis driver(s) and filters

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ driver/filter invocation syntax augmented with file names

**Description**

The typical commands that Dakota uses to launch analysis drivers are:

    > analysis_driver parameters_file_name results_file_name

Dakota will automatically arrange the executables and file names.

If the analysis driver requires a different syntax, the entire command can be specified as the analysis driver and the `verbatim` keyword will tell Dakota to use this as the command.

Note, this will not allow the use of `file_tag`, because the exact command must be specified.

For additional information on invocation syntax, see the [Interfaces section](../inputfile/interfaces.html#interfaces-main).

**Examples**

In the following example, the analysis_driver command is run without any edits from Dakota.

    interface
      analysis_driver = "matlab -nodesktop -nojvm -r 'MatlabDriver_hardcoded_filenames; exit' "
        fork
          parameters_file 'params.in'
          results_file 'results.out'
          verbatim # this tells Dakota to fork the command exactly as written, instead of appending I/O filenames

The -r flag identifies the commands that will be run by matlab. The Matlab script has the parameters_file and results_file names hardcoded, so no additional arguments are required.


---

##### interface → analysis_drivers → fork → work_directory

# work_directory

Perform each function evaluation in a separate working directory

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no work directory

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [named](interface-analysis_drivers-fork-work_directory-named.html) | The base name of the work directory created by Dakota  
Optional | [directory_tag](interface-analysis_drivers-fork-work_directory-directory_tag.html) | Tag each work directory with the function evaluation number  
Optional | [directory_save](interface-analysis_drivers-fork-work_directory-directory_save.html) | Preserve the work directory after function evaluation completion  
Optional | [link_files](interface-analysis_drivers-fork-work_directory-link_files.html) | Paths to be linked into each working directory  
Optional | [copy_files](interface-analysis_drivers-fork-work_directory-copy_files.html) | Files and directories to be copied into each working directory  
Optional | [replace](interface-analysis_drivers-fork-work_directory-replace.html) | Overwrite existing files within a work directory  
  
**Description**

When performing concurrent evaluations, it is typically necessary to cloister simulation input and output files in separate directories to avoid conflicts. When the `work_directory` feature is enabled, Dakota will create a directory for each evaluation, with optional tagging ( `directory_tag`) and saving ( `directory_save` ), as with files, and execute the analysis driver from that working directory.

The directory may be `named` with a string, or left anonymous to use an automatically-generated directory in the system’s temporary file space, e.g., /tmp/dakota_work_c93vb71z/. The optional `link_files` and `copy_files` keywords specify files or directories which should appear in each working directory.

When using work_directory, the `[analysis_drivers](../../usingdakota/reference/interface-analysis_drivers.html)` may be given by an absolute path, located in (or relative to) the startup directory alongside the Dakota input file, in the list of template files linked or copied, or on the $PATH (Path% on Windows).


---

###### interface → analysis_drivers → fork → work_directory → copy_files

# copy_files

Files and directories to be copied into each working directory

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ no copied files

**Description**

Specifies the files or directories that will be recursively copied into each working directory. Wildcards using * and ? are permitted.

**Examples**

Specifying

    copy_files = 'siminput*.in' '/path/to/simdir1' 'simdir2/*'

will create copies

    workdir/siminput*.in  # files rundir/siminput*.in copied
    workdir/simdir1/      # whole directory simdir1 recursively copied
    workdir/*  # contents of directory simdir2 recursively copied

where rundir is the directory in which Dakota was started.


---

###### interface → analysis_drivers → fork → work_directory → directory_save

# directory_save

Preserve the work directory after function evaluation completion

**Specification**

  * _Alias:_ dir_save

  * _Arguments:_ None

  * _Default:_ remove work directory

**Description**

By default, when a working directory is created by Dakota using the `work_directory` keyword, it is deleted after the evaluation is completed. The `directory_save` keyword will cause Dakota to leave (not delete) the directory.


---

###### interface → analysis_drivers → fork → work_directory → directory_tag

# directory_tag

Tag each work directory with the function evaluation number

**Specification**

  * _Alias:_ dir_tag

  * _Arguments:_ None

  * _Default:_ no work directory tagging

**Description**

If this keyword is used, Dakota will append a period and the function evaluation number to the work directory names.

If this keyword is omitted, the default is no tagging, and the same work directory will be used for ALL function evaluations. Tagging is most useful when multiple function evaluations are running simultaneously.


---

###### interface → analysis_drivers → fork → work_directory → link_files

# link_files

Paths to be linked into each working directory

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ no linked files

**Description**

Specifies the paths (files or directories) that will be symbolically linked from each working directory. Wildcards using * and ? are permitted. Linking is space-saving and useful for files not modified during the function evaluation. However, not all filesystems support linking, for example, support on Windows varies.

**Examples**

Specifying

    link_files = 'siminput*.in' '/path/to/simdir1' 'simdir2/*'

will create copies

    workdir/siminput*.in  # links to each of rundir / siminput*.in
    workdir/simdir1/      # whole directory simdir1 linked
    workdir/*             # each entry in directory simdir2 linked


---

###### interface → analysis_drivers → fork → work_directory → named

# named

The base name of the work directory created by Dakota

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ dakota_work_xxxxxxxx

**Description**

The `named` keyword is followed by a string, indicating the name of the work directory created by Dakota. If relative, the work directory will be created relative to the directory from which Dakota is invoked.

If `named` is not used, the default work directory is a temporary directory with a system-generated name (e.g., `/tmp/dakota_work_c93vb71z/`).


---

###### interface → analysis_drivers → fork → work_directory → replace

# replace

Overwrite existing files within a work directory

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ do not overwrite files

**Description**

By default, Dakota will not overwrite any existing files in a work directory. The `replace` keyword changes this behavior to force overwriting.


---

#### interface → analysis_drivers → grid

# grid

Deprecated grid computing interface

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Interface Dakota directly to a grid (distributed) computing engine. This deprecated capability was historically used for interfaces with IDEA and JAVASpaces in the past and was intended as a placeholder for future work with Condor and/or Globus services. It is not currently operational.


---

#### interface → analysis_drivers → input_filter

# input_filter

Run a pre-processing script before the analysis drivers

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no input filter

**Description**

The optional `input_filter` and `output_filter` specifications provide the names of separate pre- and post-processing programs or scripts which assist in mapping Dakota parameters files into analysis input files and mapping analysis output files into Dakota results files, respectively.

If there is only a single analysis driver, then it is usually most convenient to combine pre- and post-processing requirements into a single analysis driver script and omit the separate input and output filters. However, in the case of multiple analysis drivers, the input and output filters provide a convenient location for non-repeated pre- and post-processing requirements. That is, input and output filters are only executed once per function evaluation, regardless of the number of analysis drivers, which makes them convenient locations for data processing operations that are shared among the analysis drivers.

The `[verbatim](../../usingdakota/reference/interface-analysis_drivers-fork-verbatim.html)` keyword applies to input and output filters as well as analysis drivers, and Dakota also will substitute the names of the parameters and results files for the tokens {PARAMETERS} and {RESULTS} in input and output filer strings, as explained in the documentation for `[analysis_drivers](../../usingdakota/reference/interface-analysis_drivers.html)`.


---

#### interface → analysis_drivers → legacy_python

# legacy_python

Run Python through a deprecated C-based direct interface - requires a special Dakota build

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [numpy](interface-analysis_drivers-legacy_python-numpy.html) | Enable the use of numpy in Dakota’s Python interface  
  
**Description**


---

##### interface → analysis_drivers → legacy_python → numpy

# numpy

Enable the use of numpy in Dakota’s Python interface

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ Python list dataflow

**Description**


---

#### interface → analysis_drivers → matlab

# matlab

Run Matlab through a direct interface - requires special Dakota build

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Dakota supports a [library-linked interface to Matlab](../advanced/advancedsimulationcodeinterfaces.html#advint-existingdirect), which may not be available in pre-built downloads of Dakota and must be explicitly enabled when compiling Dakota from source.

The `[analysis_drivers](../../usingdakota/reference/interface-analysis_drivers.html)` specifies a Matlab file which implements the parameter to response mapping.

**Examples**

See `dakota/share/dakota/examples/users/MATLAB/linked`


---

#### interface → analysis_drivers → output_filter

# output_filter

Run a post-processing script after the analysis drivers

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no output filter

**Description**

The optional `input_filter` and `output_filter` specifications provide the names of separate pre- and post-processing programs or scripts which assist in mapping Dakota parameters files into analysis input files and mapping analysis output files into Dakota results files, respectively.

If there is only a single analysis driver, then it is usually most convenient to combine pre- and post-processing requirements into a single analysis driver script and omit the separate input and output filters. However, in the case of multiple analysis drivers, the input and output filters provide a convenient location for non-repeated pre- and post-processing requirements. That is, input and output filters are only executed once per function evaluation, regardless of the number of analysis drivers, which makes them convenient locations for data processing operations that are shared among the analysis drivers.

The `[verbatim](../../usingdakota/reference/interface-analysis_drivers-fork-verbatim.html)` keyword applies to input and output filters as well as analysis drivers, and Dakota also will substitute the names of the parameters and results files for the tokens {PARAMETERS} and {RESULTS} in input and output filer strings, as explained in the documentation for `[analysis_drivers](../../usingdakota/reference/interface-analysis_drivers.html)`.


---

#### interface → analysis_drivers → plugin

# plugin

Dynamically load a plugin analysis driver

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [library_path](interface-analysis_drivers-plugin-library_path.html) | Path to the plugin shared object file  
  
**Description**

Dakota 6.16 allows for run-time loading of a C++ or Python plugin analysis driver as a shared object file.

Python plugins support batch evaluations ( `[batch](../../usingdakota/reference/interface-batch.html)`) through a list of dictionaries.

**Examples**

See `dakota/src/plugins` and `dakota/share/dakota/test/dakota_plugin`.in for C++ and Python demos. These have only been tested on Linux.


---

##### interface → analysis_drivers → plugin → library_path

# library_path

Path to the plugin shared object file

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ none

**Description**


---

#### interface → analysis_drivers → python

# python

Run Python through a Pybind11-based direct interface - requires a special Dakota build

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [numpy](interface-analysis_drivers-python-numpy.html) | Enable the use of numpy in Dakota’s Python interface  
  
**Description**

Dakota supports [a library-linked interface to Python](../advanced/advancedsimulationcodeinterfaces.html#advint-existingdirect), which may not be available in pre-built downloads of Dakota and must be explicitly enabled when compiling Dakota from source.

The `[analysis_drivers](../../usingdakota/reference/interface-analysis_drivers.html)` keyword specifies a Python module:function which implements the parameter to response mapping. List data structures are the default, but NumPy is also supported, if enabled in the build.

Batch evaluations ( `[batch](../../usingdakota/reference/interface-batch.html)`) are supported through a list of dictionaries.


---

##### interface → analysis_drivers → python → numpy

# numpy

Enable the use of numpy in Dakota’s Python interface

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ Python list dataflow

**Description**


---

#### interface → analysis_drivers → scilab

# scilab

Run Scilab through a direct interface - requires special Dakota build

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Dakota supports [a library-linked interface to Scilab](../advanced/advancedsimulationcodeinterfaces.html#advint-scilab), which may not be available in pre-built downloads of Dakota and must be explicitly enabled when compiling Dakota from source.

The `[analysis_drivers](../../usingdakota/reference/interface-analysis_drivers.html)` specifies a Scilab file which implements the parameter to response mapping.

**Examples**

See `dakota/share/dakota/examples/users/Scilab/linked`


---

#### interface → analysis_drivers → system

# system

(Not recommended) Launch analysis drivers with a system call

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [parameters_file](interface-analysis_drivers-system-parameters_file.html) | Specify the name of the parameters file  
Optional | [results_file](interface-analysis_drivers-system-results_file.html) | Specify the name of the results file  
Optional | [file_tag](interface-analysis_drivers-system-file_tag.html) | Tag each parameters & results file name with the function evaluation number  
Optional | [file_save](interface-analysis_drivers-system-file_save.html) | Keep the parameters & results files after the analysis driver completes  
Optional | [labeled](interface-analysis_drivers-system-labeled.html) | Requires correct function value labels in results file  
Optional | [aprepro](interface-analysis_drivers-system-aprepro.html) | Write parameters files in APREPRO syntax  
Optional | [work_directory](interface-analysis_drivers-system-work_directory.html) | Perform each function evaluation in a separate working directory  
Optional | [allow_existing_results](interface-analysis_drivers-system-allow_existing_results.html) | Change how Dakota deals with existing results files  
Optional | [verbatim](interface-analysis_drivers-system-verbatim.html) | Specify the command Dakota uses to launch analysis driver(s) and filters  
  
**Description**

The system call interface is included in Dakota for portability and backward compatibility. Users are strongly encouraged to use the `fork` interface if possible, reverting to system only when necessary. To enable the system call interface, replace the `fork` keyword with `system`. All other keywords have identical meanings to those for the fork interface


---

##### interface → analysis_drivers → system → allow_existing_results

# allow_existing_results

Change how Dakota deals with existing results files

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ results files removed before each evaluation

**Description**

By default Dakota will remove existing results files before invoking the `analysis_driver` to avoid problems created by stale files in the current directory. To override this behavior and not delete existing files, specify `allow_existing_results`.


---

##### interface → analysis_drivers → system → aprepro

# aprepro

Write parameters files in APREPRO syntax

**Topics**

file_formats

**Specification**

  * _Alias:_ dprepro

  * _Arguments:_ None

  * _Default:_ standard parameters file format

**Description**

The format of data in the parameters files can be modified for direct usage with the APREPRO pre-processing tool [[Sja92](../../misc/bibliography.html#id265 "G. D. Sjaardema. APREPRO: an algebraic preprocessor for parameterizing finite element analyses. Technical Report SAND92-2291, Sandia National Laboratories, Albuquerque, NM, 1992.")] using the `aprepro` specification

Without this keyword, the parameters file are written in DPrePro format. [DPrePro](../../externaltools/dpreproandpyprepro.html#interfaces-dprepro-and-pyprepro) is a utility included with Dakota.


---

##### interface → analysis_drivers → system → file_save

# file_save

Keep the parameters & results files after the analysis driver completes

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ file cleanup

**Description**

If `file_save` is used, Dakota will not delete the parameters and results files after the function evaluation is completed.

The default behavior is NOT to save these files.

If `file_tag` is not specified and the saved files would be overwritten by a future evaluation, Dakota renames them after the analysis driver has run by tagging them with the evaluation number.

File saving is most useful when debugging the data communication between Dakota and the simulation.


---

##### interface → analysis_drivers → system → file_tag

# file_tag

Tag each parameters & results file name with the function evaluation number

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no tagging

**Description**

If this keyword is used, Dakota will append a period and the function evaluation number to the names of the parameter and results files.

_Default Behavior_ If this keyword is omitted, the default is no file tagging.

_Usage Tips_

  * File tagging is most useful when multiple function evaluations are running simultaneously using files in a shared disk space. The analysis driver will be able to infer the function evaluation number from the file names.

  * Note that when the `file_save` keyword is used, Dakota automatically renames parameters and results files, giving them tags after execution of the analysis driver if they otherwise would be overwritten by the next evaluation.

**Examples**

If the following is included in the `interface` section of the Dakota input:

    parameters_file = params.in
    results_file = results.out
    file_tag

Then for the 3rd evaluation, Dakota will write `params`.in.3, and will expect `results`.out.3 to be written by the analysis driver.


---

##### interface → analysis_drivers → system → labeled

# labeled

Requires correct function value labels in results file

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ Function value labels optional

**Description**

The `labeled` keyword directs Dakota to enforce a stricter results file format and enables more detailed error reporting.

When the `labeled` keyword is used, function values in results files must be accompanied by their corresponding descriptors. If the user did not supply response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` in her Dakota input file, then Dakota auto-generated descriptors are expected.

Distinct error messages are emitted for function values that are out-of-order, repeated, or missing. Labels that appear without a function value and unexpected data are also reported as errors. Dakota attempts to report all errors in a results file, not just the first it encounters. After reporting results file errors, Dakota aborts.

Labels for analytic gradients and Hessians currently are not supported.

Although the `labeled` keyword is optional, its use is recommended to help catch and identify problems with results files. The User’s Manual contains further information about the results file format.

_Default Behavior_ <p> By default, Dakota does not require labels for function values, and ignores them if they are present.</p>


---

##### interface → analysis_drivers → system → parameters_file

# parameters_file

Specify the name of the parameters file

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ Unix temp files

**Description**

The parameters file is used by Dakota to pass the parameter values to the analysis driver. The name of the file can be optionally specified using the `parameters_file` keyword.

If this is not specified, the default data transfer files are temporary files with system-generated names (e.g., `/tmp/dakota_params_aaaa0886`).


---

##### interface → analysis_drivers → system → results_file

# results_file

Specify the name of the results file

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ Unix temp files

**Description**

The results file must be written by the analysis driver. It is read by Dakota to determine the response values for each function evaluation.

The name of the file can be optionally specified using the `results_file` keyword.

If this is not specified, the default data transfer files are temporary files with system-generated names (e.g., `/tmp/dakota_results_aaaa0886`).


---

##### interface → analysis_drivers → system → verbatim

# verbatim

Specify the command Dakota uses to launch analysis driver(s) and filters

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ driver/filter invocation syntax augmented with file names

**Description**

The typical commands that Dakota uses to launch analysis drivers are:

    > analysis_driver parameters_file_name results_file_name

Dakota will automatically arrange the executables and file names.

If the analysis driver requires a different syntax, the entire command can be specified as the analysis driver and the `verbatim` keyword will tell Dakota to use this as the command.

Note, this will not allow the use of `file_tag`, because the exact command must be specified.

For additional information on invocation syntax, see the [Interfaces section](../inputfile/interfaces.html#interfaces-main).

**Examples**

In the following example, the analysis_driver command is run without any edits from Dakota.

    interface
      analysis_driver = "matlab -nodesktop -nojvm -r 'MatlabDriver_hardcoded_filenames; exit' "
        fork
          parameters_file 'params.in'
          results_file 'results.out'
          verbatim # this tells Dakota to fork the command exactly as written, instead of appending I/O filenames

The -r flag identifies the commands that will be run by matlab. The Matlab script has the parameters_file and results_file names hardcoded, so no additional arguments are required.


---

##### interface → analysis_drivers → system → work_directory

# work_directory

Perform each function evaluation in a separate working directory

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no work directory

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [named](interface-analysis_drivers-system-work_directory-named.html) | The base name of the work directory created by Dakota  
Optional | [directory_tag](interface-analysis_drivers-system-work_directory-directory_tag.html) | Tag each work directory with the function evaluation number  
Optional | [directory_save](interface-analysis_drivers-system-work_directory-directory_save.html) | Preserve the work directory after function evaluation completion  
Optional | [link_files](interface-analysis_drivers-system-work_directory-link_files.html) | Paths to be linked into each working directory  
Optional | [copy_files](interface-analysis_drivers-system-work_directory-copy_files.html) | Files and directories to be copied into each working directory  
Optional | [replace](interface-analysis_drivers-system-work_directory-replace.html) | Overwrite existing files within a work directory  
  
**Description**

When performing concurrent evaluations, it is typically necessary to cloister simulation input and output files in separate directories to avoid conflicts. When the `work_directory` feature is enabled, Dakota will create a directory for each evaluation, with optional tagging ( `directory_tag`) and saving ( `directory_save` ), as with files, and execute the analysis driver from that working directory.

The directory may be `named` with a string, or left anonymous to use an automatically-generated directory in the system’s temporary file space, e.g., /tmp/dakota_work_c93vb71z/. The optional `link_files` and `copy_files` keywords specify files or directories which should appear in each working directory.

When using work_directory, the `[analysis_drivers](../../usingdakota/reference/interface-analysis_drivers.html)` may be given by an absolute path, located in (or relative to) the startup directory alongside the Dakota input file, in the list of template files linked or copied, or on the $PATH (Path% on Windows).


---

###### interface → analysis_drivers → system → work_directory → copy_files

# copy_files

Files and directories to be copied into each working directory

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ no copied files

**Description**

Specifies the files or directories that will be recursively copied into each working directory. Wildcards using * and ? are permitted.

**Examples**

Specifying

    copy_files = 'siminput*.in' '/path/to/simdir1' 'simdir2/*'

will create copies

    workdir/siminput*.in  # files rundir/siminput*.in copied
    workdir/simdir1/      # whole directory simdir1 recursively copied
    workdir/*  # contents of directory simdir2 recursively copied

where rundir is the directory in which Dakota was started.


---

###### interface → analysis_drivers → system → work_directory → directory_save

# directory_save

Preserve the work directory after function evaluation completion

**Specification**

  * _Alias:_ dir_save

  * _Arguments:_ None

  * _Default:_ remove work directory

**Description**

By default, when a working directory is created by Dakota using the `work_directory` keyword, it is deleted after the evaluation is completed. The `directory_save` keyword will cause Dakota to leave (not delete) the directory.


---

###### interface → analysis_drivers → system → work_directory → directory_tag

# directory_tag

Tag each work directory with the function evaluation number

**Specification**

  * _Alias:_ dir_tag

  * _Arguments:_ None

  * _Default:_ no work directory tagging

**Description**

If this keyword is used, Dakota will append a period and the function evaluation number to the work directory names.

If this keyword is omitted, the default is no tagging, and the same work directory will be used for ALL function evaluations. Tagging is most useful when multiple function evaluations are running simultaneously.


---

###### interface → analysis_drivers → system → work_directory → link_files

# link_files

Paths to be linked into each working directory

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ no linked files

**Description**

Specifies the paths (files or directories) that will be symbolically linked from each working directory. Wildcards using * and ? are permitted. Linking is space-saving and useful for files not modified during the function evaluation. However, not all filesystems support linking, for example, support on Windows varies.

**Examples**

Specifying

    link_files = 'siminput*.in' '/path/to/simdir1' 'simdir2/*'

will create copies

    workdir/siminput*.in  # links to each of rundir / siminput*.in
    workdir/simdir1/      # whole directory simdir1 linked
    workdir/*             # each entry in directory simdir2 linked


---

###### interface → analysis_drivers → system → work_directory → named

# named

The base name of the work directory created by Dakota

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ dakota_work_xxxxxxxx

**Description**

The `named` keyword is followed by a string, indicating the name of the work directory created by Dakota. If relative, the work directory will be created relative to the directory from which Dakota is invoked.

If `named` is not used, the default work directory is a temporary directory with a system-generated name (e.g., `/tmp/dakota_work_c93vb71z/`).


---

###### interface → analysis_drivers → system → work_directory → replace

# replace

Overwrite existing files within a work directory

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ do not overwrite files

**Description**

By default, Dakota will not overwrite any existing files in a work directory. The `replace` keyword changes this behavior to force overwriting.


---

### interface → analysis_scheduling

# analysis_scheduling

Specify the scheduling of concurrent analyses when Dakota is run in parallel

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ automatic (see discussion)

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Scheduling Mode | [master](interface-analysis_scheduling-master.html) | Specify a dedicated master partition for parallel analysis scheduling  
[peer](interface-analysis_scheduling-peer.html) | Specify a peer partition for parallel analysis scheduling  
  
**Description**

When Dakota is run in parallel, the partition type for the analysis servers is determined automatically. If this setting is undesirable, it may be overridden by the user using the `analysis_scheduling` keyword.


---

#### interface → analysis_scheduling → master

# master

Specify a dedicated master partition for parallel analysis scheduling

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This option overrides the Dakota parallel automatic configuration, forcing the use of a dedicated master partition. In a dedicated master partition, one processor (the “master”) dynamically schedules work on the analysis servers. This reduces the number of processors available to create servers by 1.


---

#### interface → analysis_scheduling → peer

# peer

Specify a peer partition for parallel analysis scheduling

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This option overrides the Dakota parallel automatic configuration, forcing the use of a peer partition. In a peer partition, all processors are available to be assigned to analysis servers. Note that unlike the case of `evaluation_scheduling`, it is not possible to specify `static` or `dynamic`.


---

### interface → analysis_servers

# analysis_servers

Specify the number of analysis servers when Dakota is run in parallel

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ automatic (see discussion)

**Description**

The optional `analysis_servers` specification supports user override of the automatic parallel configuration for the number of analysis servers. That is, if the automatic configuration is undesirable for some reason, the user can enforce a desired number of partitions at the analysis parallelism level. Refer to ParallelLibrary and the [Parallel Computing section](../advanced/parallelcomputing.html#parallel) for additional information.


---

### interface → asynchronous

# asynchronous

Specify local evaluation or analysis concurrency

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ synchronous interface usage

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [evaluation_concurrency](interface-asynchronous-evaluation_concurrency.html) | Determine how many concurrent evaluations Dakota will schedule  
Optional | [local_evaluation_scheduling](interface-asynchronous-local_evaluation_scheduling.html) | Control how local asynchronous jobs are scheduled  
Optional | [analysis_concurrency](interface-asynchronous-analysis_concurrency.html) | Limit the number of analysis drivers within an evaluation that Dakota will schedule  
  
**Description**

The optional `asynchronous` keyword specifies use of asynchronous protocols (i.e., background system calls, nonblocking forks, POSIX threads) when evaluations or analyses are invoked. Evaluation and analysis concurrency can be independently controlled, as can the scheduling mode (static vs. dynamic) of the local evaluations.

_Default Behavior_

  * when running Dakota on a single processor in `asynchronous` mode, the default concurrency of evaluations and analyses is all concurrency that is available. The `evaluation_concurrency` and `analysis_concurrency` specifications can be used to limit this concurrency in order to avoid machine overload or usage policy violation.

  * when running Dakota on multiple processors in message passing mode, the default concurrency of evaluations and analyses on each of the servers is one (i.e., the parallelism is exclusively that of the message passing). With the `evaluation_concurrency` and `analysis_concurrency` specifications, a hybrid parallelism can be selected through combination of message passing parallelism with asynchronous parallelism on each server.


---

#### interface → asynchronous → analysis_concurrency

# analysis_concurrency

Limit the number of analysis drivers within an evaluation that Dakota will schedule

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ local: unlimited concurrency, hybrid: no concurrency

**Description**

When `asynchronous` execution is enabled and each evaluation involves multiple analysis drivers, then the default behavior is to launch all drivers simultaneously. The `analysis_concurrency` keyword can be used to limit the number of concurrently run drivers.


---

#### interface → asynchronous → evaluation_concurrency

# evaluation_concurrency

Determine how many concurrent evaluations Dakota will schedule

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ local: unlimited concurrency, hybrid: no concurrency

**Description**

When `asynchronous` execution is enabled, the default behavior is to launch all available evaluations simultaneously. The `evaluation_concurrency` keyword can be used to limit the number of concurrent evaluations.


---

#### interface → asynchronous → local_evaluation_scheduling

# local_evaluation_scheduling

Control how local asynchronous jobs are scheduled

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ dynamic

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Scheduling Mode | [dynamic](interface-asynchronous-local_evaluation_scheduling-dynamic.html) | Dynamic local scheduling (sequential)  
[static](interface-asynchronous-local_evaluation_scheduling-static.html) | Static local scheduling (tiled)  
  
**Description**

When performing asynchronous local evaluations, the local_evaluation_scheduling keyword controls how new evaluation jobs are dispatched when one completes.

The two options are:

  * `dynamic`

  * `static`

If the local_evaluation_scheduling is specified as dynamic (the default), each completed evaluation will be replaced by the next in the local evaluation queue.

If local_evaluation_scheduling is specified as static, each completed evaluation will be replaced by an evaluation number that is congruent modulo the evaluation_concurrency. This is helpful for relative node scheduling as described in `dakota/share/dakota/examples/parallelism`. For example, assuming only asynchronous local concurrency (no MPI), if the local concurrency is 6 and job 2 completes, it will be replaced with job 8.

For the case of hybrid parallelism, static local scheduling results in evaluation replacements that are modulo the total capacity, defined as the product of the evaluation concurrency and the number of evaluation servers. Both of these cases can result in idle processors if runtimes are non-uniform, so the default dynamic scheduling is preferred when relative node scheduling is not required.


---

##### interface → asynchronous → local_evaluation_scheduling → dynamic

# dynamic

Dynamic local scheduling (sequential)

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

If the local_evaluation_scheduling is specified as dynamic (the default), each completed evaluation will be replaced by the next in the local evaluation queue.


---

##### interface → asynchronous → local_evaluation_scheduling → static

# static

Static local scheduling (tiled)

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

If local_evaluation_scheduling is specified as static, each completed evaluation will be replaced by an evaluation number that is congruent modulo the evaluation_concurrency. This is helpful for relative node scheduling as described in dakota/share/dakota/examples/parallelism. For example, assuming only asynchronous local concurrency (no MPI), if the local concurrency is 6 and job 2 completes, it will be replaced with job 8.

For the case of hybrid parallelism, static local scheduling results in evaluation replacements that are modulo the total capacity, defined as the product of the evaluation concurrency and the number of evaluation servers. Both of these cases can result in idle processors if runtimes are non-uniform, so the default dynamic scheduling is preferred when relative node scheduling is not required.


---

### interface → batch

# batch

Perform evaluations in batches

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ sequential interface usage

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [size](interface-batch-size.html) | Limit the number of evaluations in a batch  
  
**Description**

When the optional `batch` keyword is used, Dakota performs evaluations in batches. In batch mode, Dakota writes the parameters for multiple (a batch of) evaluations to a single file, invokes the analysis driver once for the whole batch, and expects to find results for the entire batch in a single file after the analysis driver has exited. Batch mode may be useful when a user desires to take greater control over job management. For example, the analysis driver can perform the evaluations in the batch in any sequence or in concurrent sub-batches.

The names of the parameters file and results file are provided as command line arguments to the analysis driver, just as in a conventional, non-batch evaluation. By default, all currently available evaluations will be performed in a single batch, but the batch size can be limited using the `[size](../../usingdakota/reference/interface-batch-size.html)` keyword.

Batch mode has a few important limitations.

  * Only one batch at a time may be executed. Asynchronous execution of multiple concurrent batches is not supported.

  * No `[input_filter](../../usingdakota/reference/interface-analysis_drivers-input_filter.html)` or `[output_filter](../../usingdakota/reference/interface-analysis_drivers-output_filter.html)` is permitted.

  * Only one `analysis_driver` is allowed.

  * `failure_capture` modes are restricted to `[abort](../../usingdakota/reference/interface-failure_capture-abort.html)` and `[recover](../../usingdakota/reference/interface-failure_capture-recover.html)`.

Some of these restrictions may be lifted in future Dakota releases.

_File Formats_

A batch parameters file written by Dakota is simply a concatenated set of parameters files for the set of evaluations, either in `[aprepro](../../usingdakota/reference/interface-analysis_drivers-fork-aprepro.html)` or default Dakota format.

The batch results file is also a concatenated set of results files for the individual evaluations. However, because Dakota’s results file format is not as rich as its parameters file format, evaluations in the batch results file must be separated by a line that begins with the ‘#’ character.

The order of evaluations in the batch results file must match the order in the batch parameters file.

_Tagging and Work Directories_

When Dakota’s `[work_directory](../../usingdakota/reference/interface-analysis_drivers-fork-work_directory.html)` feature is enabled, one directory is created per batch. If `[file_tag](../../usingdakota/reference/interface-analysis_drivers-fork-file_tag.html)` or `[directory_tag](../../usingdakota/reference/interface-analysis_drivers-fork-work_directory-directory_tag.html)` is used, parameters/results files and work directories are tagged with a batch Id, which is an integer that begins with 1 and increments for each new batch.


---

#### interface → batch → size

# size

Limit the number of evaluations in a batch

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ local: unlimited batch size, hybrid: zero batch size

**Description**

When `batch` execution is enabled, the default behavior is to add all available evaluations to the current batch. The `size` keyword limits the number of evaluations in a batch.


---

### interface → deactivate

# deactivate

Deactivate Dakota interface features for simplicity or efficiency

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ Active set vector control, function evaluation cache, and restart file features are active

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [active_set_vector](interface-deactivate-active_set_vector.html) | Deactivate the Active Set Vector  
Optional | [evaluation_cache](interface-deactivate-evaluation_cache.html) | Do not retain function evaluation history in memory  
Optional | [strict_cache_equality](interface-deactivate-strict_cache_equality.html) | Do not require strict cache equality when finding duplicates  
Optional | [restart_file](interface-deactivate-restart_file.html) | Deactivate writing to the restart file  
  
**Description**

The optional `deactivate` specification block allows a user to deactivate interface features in order to simplify interface development, increase execution speed, and/or reduce memory and disk requirements. Any or all of these features may be specified concurrently.

  * Active set vector (ASV) control: deactivate so that Dakota expects the same response data (all functions, gradients, Hessian) back from the simulation on every evaluation, instead of only those components required by the method for this particular function evaluation.

  * Function evaluation cache: save memory by not caching the function evaluation history. May result in additional (duplicate) function evaluations.

  * Strict cache equality: allow a relaxed tolerance when detecting duplicate function evaluations. Can be useful when importing data or restarting across machines.

  * Restart file: improve efficiency and eliminate restart file storage at the risk of not being able to recover a failed or partial Dakota study.


---

#### interface → deactivate → active_set_vector

# active_set_vector

Deactivate the Active Set Vector

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Allows the user to turn off any variability in ASV values so that active set logic can be omitted in the user’s simulation interface. This option trades some efficiency for simplicity in interface development.

The default behavior is to request the minimum amount of data required by an algorithm at any given time, which implies that the ASV values may vary from one function evaluation to the next. Since the user’s interface must return the data set requested by the ASV values, this interface must contain additional logic to account for any variations in ASV content.

Deactivating this ASV control causes Dakota to always request a “full” data set (the full function, gradient, and Hessian data that is available from the interface as specified in the responses specification) on each function evaluation.

For example, if ASV control has been deactivated and the responses section specifies four response functions, analytic gradients, and no Hessians, then the ASV on every function evaluation will be { 3 3 3 3 }, regardless of what subset of this data is currently needed. While wasteful of computations in many instances, this simplifies the interface and allows the user to return the same data set on every evaluation. Conversely, if ASV control is active (the default behavior), then the ASV requests in this example might vary from { 1 1 1 1 } to { 2 0 0 2 }, etc., according to the specific data needed on a particular function evaluation. This will require the user’s interface to read the ASV requests and perform the appropriate logic in conditionally returning only the data requested.

_Usage Tips_

  * In general, the default ASV behavior is recommended for the sake of computational efficiency, unless interface development time is a critical concern.

  * Whether active or inactive, the data returned to Dakota from the user’s interface must match the ASV passed in, or else a response recovery error will result. However, when the ASV control is deactivated, the ASV values are invariant and need not be checked on every evaluation.

  * Deactivating the ASV control can have a positive effect on load balancing for parallel Dakota executions. Thus, there is significant overlap in this ASV control option with speculative gradients. There is also overlap with the mode override approach used with certain optimizers to combine individual value, gradient, and Hessian requests.


---

#### interface → deactivate → evaluation_cache

# evaluation_cache

Do not retain function evaluation history in memory

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Do not retain the complete function evaluation history in memory.

This can be important for reducing memory requirements in large-scale applications (i.e., applications with a large number of variables or response functions) and for eliminating the overhead of searching for duplicates within the function evaluation cache prior to each new function evaluation (e.g., for improving speed in problems with 1000’s of inexpensive function evaluations or for eliminating overhead when performing timing studies).

However, the downside is that unnecessary computations may be performed since duplication in function evaluation requests may not be detected. For this reason, this option is not recommended when function evaluations are costly.

Note: duplication detection within Dakota can be deactivated, but duplication detection features within specific optimizers may still be active.


---

#### interface → deactivate → restart_file

# restart_file

Deactivate writing to the restart file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Eliminate the output of each new function evaluation to the binary restart file. This can increase speed and reduce disk storage requirements, but at the expense of a loss in the ability to recover and continue a run that terminates prematurely (e.g., due to a system crash or network problem).

_Usage Tips_

  * This option is not recommended when function evaluations are costly or prone to failure.

  * Using the `deactivate` `restart_file` specification will result in a zero length restart file with the default name `dakota`.rst.


---

#### interface → deactivate → strict_cache_equality

# strict_cache_equality

Do not require strict cache equality when finding duplicates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [cache_tolerance](interface-deactivate-strict_cache_equality-cache_tolerance.html) | Specify tolerance when identifying duplicate function evaluations  
  
**Description**

By default, Dakota’s evaluation cache and restart capabilities are based on strict binary equality. This provides a performance advantage, as it permits a hash-based data structure to be used to search the evaluation cache. However, deactiving strict equality may prevent cache misses, which can occur when attempting to use a restart file on a machine different from the one on which it was generated.


---

##### interface → deactivate → strict_cache_equality → cache_tolerance

# cache_tolerance

Specify tolerance when identifying duplicate function evaluations

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

**Description**

Described on parent page


---

### interface → evaluation_scheduling

# evaluation_scheduling

Specify the scheduling of concurrent evaluations when Dakota is run in parallel

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ automatic (see discussion)

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Server Mode | [master](interface-evaluation_scheduling-master.html) | Specify a dedicated master partition for parallel evaluation scheduling  
[peer](interface-evaluation_scheduling-peer.html) | Specify a peer partition for parallel evaluation scheduling  
  
**Description**

When Dakota is run in parallel, the partition type and scheduling for the evaluation servers are determined automatically. If these settings are undesirable, they may be overridden by the user using the `evaluation_scheduling` keyword.


---

#### interface → evaluation_scheduling → master

# master

Specify a dedicated master partition for parallel evaluation scheduling

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This option overrides the Dakota parallel automatic configuration, forcing the use of a dedicated master partition. In a dedicated master partition, one processor (the “master”) dynamically schedules work on the evaluation servers. This reduces the number of processors available to create servers by 1.


---

#### interface → evaluation_scheduling → peer

# peer

Specify a peer partition for parallel evaluation scheduling

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Scheduling Mode | [dynamic](interface-evaluation_scheduling-peer-dynamic.html) | Specify dynamic scheduling in a peer partition when Dakota is run in parallel.  
[static](interface-evaluation_scheduling-peer-static.html) | Specify static scheduling in a peer partition when Dakota is run in parallel.  
  
**Description**

This option overrides the Dakota parallel automatic configuration, forcing the use of a peer partition. In a peer partition, all processors are available to be assigned to evaluation servers. The scheduling, `static` or `dynamic`, must also be specified.


---

##### interface → evaluation_scheduling → peer → dynamic

# dynamic

Specify dynamic scheduling in a peer partition when Dakota is run in parallel.

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ dynamic (see discussion)

**Description**

In `dynamic` scheduling, evaluations are assigned to servers as earlier evaluations complete. Dynamic scheduling is advantageous when evaluations are of uneven duration.


---

##### interface → evaluation_scheduling → peer → static

# static

Specify static scheduling in a peer partition when Dakota is run in parallel.

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

In `static` scheduling, all available evaluations are assigned to servers in a predetermined fashion. Each completed evaluation is replaced with one congruent modulo the evaluation concurrency. For example, with 6 servers, eval number 2 will be replaced by eval number 8.


---

### interface → evaluation_servers

# evaluation_servers

Specify the number of evaluation servers when Dakota is run in parallel

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ automatic (see discussion)

**Description**

The optional `evaluation_servers` specification supports user override of the automatic parallel configuration for the number of evaluation servers. That is, if the automatic configuration is undesirable for some reason, the user can enforce a desired number of partitions at the evaluation parallelism level. Refer to ParallelLibrary and the [Parallel Computing section](../advanced/parallelcomputing.html#parallel) for additional information.


---

### interface → failure_capture

# failure_capture

Determine how Dakota responds to analysis driver failure

**Topics**

**Default Behavior:** If no failure capturing specification is provided, then the default behavior is method dependent. For those iterative algorithms that provide internal failure mitigation strategies (currently NL2SOL), the default is to transfer the failure information from the interface back to the algorithm for mitigation, with no specific action taken by Dakota. For all other algorithms, the default is to abort.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ abort

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Failure Mitigation | [abort](interface-failure_capture-abort.html) | (Default) Abort the Dakota job  
[retry](interface-failure_capture-retry.html) | Rerun failed analyses  
[recover](interface-failure_capture-recover.html) | Substitute dummy values for the responses  
[continuation](interface-failure_capture-continuation.html) | Cause Dakota to step toward the failed “target” simulation from a nearby successful “source”  
  
**Description**

Dakota can deal with analysis failure in a few ways.

The first step is that Dakota must detect analysis failure. Importantly, Dakota always expects a results file to be written by the analysis driver, even when a failure has occurred. If the file does not exist when the analysis driver exits, a Dakota error results, causing Dakota itself to terminate. The analysis driver communicates an analysis failure to Dakota by writing a results file beginning with the (case-insensitive) word “fail”. Any file contents after “fail” are ignored.

Once Dakota detects analysis failure, the failure can be mitigated in four ways:

  * `[abort](../../usingdakota/reference/interface-failure_capture-abort.html)` (the default)

  * `[retry](../../usingdakota/reference/interface-failure_capture-retry.html)`

  * `[recover](../../usingdakota/reference/interface-failure_capture-recover.html)`

  * `[continuation](../../usingdakota/reference/interface-failure_capture-continuation.html)`

Refer to [Simulation Failure Capturing](../advanced/simulationfailurecapturing.html#failure) for additional information.


---

#### interface → failure_capture → abort

# abort

(Default) Abort the Dakota job

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Stop the Dakota job, as well as any other running analysis drivers when a failure is communicated.


---

#### interface → failure_capture → continuation

# continuation

Cause Dakota to step toward the failed “target” simulation from a nearby successful “source”

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When `failure_capture` `continuation` is enabled and an evaluation fails, then Dakota will attempt to march incrementally from a previous good evaluation (the “source”) toward the failing one (the “target”). Further details about the algorithm employed by Dakota are supplied [here](../advanced/simulationfailurecapturing.html#failure-mitigation-continuation).


---

#### interface → failure_capture → recover

# recover

Substitute dummy values for the responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

When a simulation failure is detected, substitute the provided dummy function values in the response. Gradient and Hessian are not supported.


---

#### interface → failure_capture → retry

# retry

Rerun failed analyses

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

Number of times to rety a failing analysis


---

### interface → id_interface

# id_interface

Name the interface block; helpful when there are multiple

**Topics**

block_identifier

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ use of last interface parsed

**Description**

The optional `id_interface` keyword accepts a string that uniquely identifies this interface block. A model can then use this interface by specifying the same string in its `interface_pointer` specification.

_Default Behavior_

If the `id_interface` specification is omitted, a particular interface specification will be used by a model only if that model does not include an `interface_pointer` and the interface block was the last (or only) one parsed.

_Usage Tips_

  * It is a best practice to always use explicit interface IDs and pointers to avoid confusion.

  * If only one interface block exists, then `id_interface` can be safely omitted from the interface block (and `interface_pointer` omitted from the model specification(s)), since there is no ambiguity.

**Examples**

For example, a model specification including

    model
      interface_pointer = 'I1'

will link to an interface with

    id_interface = 'I1'


---

### interface → processors_per_evaluation

# processors_per_evaluation

Specify the number of processors per evaluation server when Dakota is run in parallel

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ automatic (see discussion)

**Description**

The optional `processors_per_evaluation` specification supports user override of the automatic parallel configuration for the number of processors in each evaluation server. That is, if the automatic configuration is undesirable for some reason, the user can enforce a desired server size at the evaluation parallelism level. Refer to ParallelLibrary and the [Parallel Computing section](../advanced/parallelcomputing.html#parallel) for additional information.

