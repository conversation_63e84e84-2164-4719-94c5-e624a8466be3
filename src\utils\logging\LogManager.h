#ifndef LOGMANAGER_H
#define LOGMANAGER_H

#include <QObject>
#include <QString>
#include <QFile>
#include <QTextStream>
#include <QMutex>
#include <QDateTime>

/**
 * @brief 日志级别枚举
 */
enum class LogLevel {
    Debug,
    Info,
    Warning,
    Error,
    Fatal
};

/**
 * @brief 日志管理器类，负责应用程序日志记录
 * 
 * LogManager提供了一个中心化的日志记录机制，
 * 支持不同级别的日志记录和输出到文件。
 */
class LogManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     */
    LogManager();

    /**
     * @brief 析构函数
     */
    ~LogManager();

    /**
     * @brief 初始化日志管理器
     * @param logFilePath 日志文件路径，默认为空（使用默认路径）
     * @return 初始化是否成功
     */
    bool initialize(const QString& logFilePath = QString());

    /**
     * @brief 关闭日志管理器
     */
    void shutdown();

    /**
     * @brief 记录调试日志
     * @param message 日志消息
     */
    void logDebug(const QString& message);

    /**
     * @brief 记录信息日志
     * @param message 日志消息
     */
    void logInfo(const QString& message);

    /**
     * @brief 记录警告日志
     * @param message 日志消息
     */
    void logWarning(const QString& message);

    /**
     * @brief 记录错误日志
     * @param message 日志消息
     */
    void logError(const QString& message);

    /**
     * @brief 记录致命错误日志
     * @param message 日志消息
     */
    void logFatal(const QString& message);

    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param message 日志消息
     */
    void log(LogLevel level, const QString& message);

    /**
     * @brief 设置日志级别
     * @param level 日志级别
     */
    void setLogLevel(LogLevel level);

    /**
     * @brief 获取当前日志级别
     * @return 日志级别
     */
    LogLevel logLevel() const;

    /**
     * @brief 设置是否输出到控制台
     * @param enabled 是否启用
     */
    void setConsoleOutput(bool enabled);

    /**
     * @brief 设置是否输出到文件
     * @param enabled 是否启用
     */
    void setFileOutput(bool enabled);

    /**
     * @brief 清空日志文件
     */
    void clearLogFile();

signals:
    /**
     * @brief 日志记录信号
     * @param level 日志级别
     * @param message 日志消息
     * @param timestamp 时间戳
     */
    void logRecorded(LogLevel level, const QString& message, const QDateTime& timestamp);

private:
    /**
     * @brief 获取日志级别字符串
     * @param level 日志级别
     * @return 日志级别字符串
     */
    QString levelToString(LogLevel level) const;

    /**
     * @brief 格式化日志消息
     * @param level 日志级别
     * @param message 日志消息
     * @return 格式化后的日志消息
     */
    QString formatLogMessage(LogLevel level, const QString& message) const;

    /**
     * @brief 写入日志到文件
     * @param message 日志消息
     */
    void writeToFile(const QString& message);

    QFile m_logFile;
    QTextStream m_logStream;
    QMutex m_mutex;
    LogLevel m_logLevel;
    bool m_consoleOutput;
    bool m_fileOutput;
    bool m_initialized;
};

#endif // LOGMANAGER_H
