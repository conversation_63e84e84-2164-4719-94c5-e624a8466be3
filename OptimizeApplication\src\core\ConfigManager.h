#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QSettings>
#include <QVariant>
#include <QString>
#include <QMutex>
#include "Common.h"

class ConfigManager : public QObject
{
    Q_OBJECT

public:
    static ConfigManager* instance();
    
    // 基本配置操作
    void setValue(const QString& key, const QVariant& value);
    QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const;
    bool contains(const QString& key) const;
    void remove(const QString& key);
    void clear();
    
    // 应用程序设置
    void setWindowGeometry(const QByteArray& geometry);
    QByteArray getWindowGeometry() const;
    
    void setWindowState(const QByteArray& state);
    QByteArray getWindowState() const;
    
    void setRecentFiles(const QStringList& files);
    QStringList getRecentFiles() const;
    void addRecentFile(const QString& file);
    void removeRecentFile(const QString& file);
    
    void setLanguage(const QString& language);
    QString getLanguage() const;
    
    void setTheme(const QString& theme);
    QString getTheme() const;
    
    // 日志设置
    void setLogLevel(LogLevel level);
    LogLevel getLogLevel() const;
    
    void setLogToFile(bool enabled);
    bool isLogToFileEnabled() const;
    
    // 优化参数设置
    void setOptimizationParams(const QVariantMap& params);
    QVariantMap getOptimizationParams() const;
    
    // 输入输出设置
    void setInputParams(const QVariantMap& params);
    QVariantMap getInputParams() const;
    
    void setOutputParams(const QVariantMap& params);
    QVariantMap getOutputParams() const;

signals:
    void configChanged(const QString& key, const QVariant& value);

private:
    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();
    
    void initDefaultSettings();
    QString logLevelToString(LogLevel level) const;
    LogLevel stringToLogLevel(const QString& str) const;
    
    static ConfigManager* m_instance;
    static QMutex m_mutex;
    
    QSettings* m_settings;
    mutable QMutex m_settingsMutex;
    
    // 配置键常量
    static const QString KEY_WINDOW_GEOMETRY;
    static const QString KEY_WINDOW_STATE;
    static const QString KEY_RECENT_FILES;
    static const QString KEY_LANGUAGE;
    static const QString KEY_THEME;
    static const QString KEY_LOG_LEVEL;
    static const QString KEY_LOG_TO_FILE;
    static const QString KEY_OPTIMIZATION_PARAMS;
    static const QString KEY_INPUT_PARAMS;
    static const QString KEY_OUTPUT_PARAMS;
};

#endif // CONFIGMANAGER_H 