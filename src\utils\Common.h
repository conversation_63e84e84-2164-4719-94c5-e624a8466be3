#ifndef COMMON_H
#define COMMON_H

#include <QString>

/**
 * @brief Constants used throughout the application
 */
namespace AppConstants {
    const QString APP_NAME = "Optimize Application";
    const QString APP_VERSION = "1.0.0";
    const QString ORGANIZATION_NAME = "Optimize Solutions";
    const QString ORGANIZATION_DOMAIN = "optimizesolutions.com";
    
    // File extensions
    const QString FILE_EXTENSION = ".opt";
    const QString FILE_FILTER = "Optimize Files (*.opt);;All Files (*.*)";
    
    // Settings keys
    namespace Settings {
        const QString WINDOW_GEOMETRY = "window/geometry";
        const QString WINDOW_STATE = "window/state";
        const QString THEME = "appearance/theme";
        const QString RECENT_FILES = "files/recent";
        const QString LAST_DIRECTORY = "files/lastDirectory";
    }
    
    // Default values
    namespace Defaults {
        const QString DEFAULT_DIRECTORY = ".";
    }
}

#endif // COMMON_H 