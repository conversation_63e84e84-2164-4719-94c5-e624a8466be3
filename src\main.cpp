#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QLocale>
#include <QTranslator>
#include <QTimer>
#include <QMessageBox>
#include <QProcess>
#include <QThread>
#include "ui/MainWindow.h"
#include "core/ApplicationCore.h"
#include "core/ConfigManager.h"
#include "core/ThemeManager.h"
#include "utils/Logger.h"
#include "utils/Common.h"
#include "SARibbonBar/SARibbonBar.h"

int main(int argc, char *argv[])
{
    // Initialize SARibbonBar for high DPI support
    SARibbonBar::initHighDpi();
    
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    
    // Create application
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName(AppConstants::APP_NAME);
    app.setApplicationVersion(AppConstants::APP_VERSION);
    app.setOrganizationName(AppConstants::ORGANIZATION_NAME);
    app.setOrganizationDomain(AppConstants::ORGANIZATION_DOMAIN);

    // Setup internationalization
    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "OptimizeApplication_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            app.installTranslator(&translator);
            break;
        }
    }

    // Handle restart argument if present
    if (argc > 1 && QString(argv[1]) == "--restart") {
        // Wait a moment before starting to ensure the previous instance has closed
        QThread::msleep(1000);
    }

    // Initialize application core
    ApplicationCore* core = ApplicationCore::instance();
    if (!core->initialize()) {
        LOG_CRITICAL("Failed to initialize application core", "main");
        return -1;
    }

    // Initialize theme manager
    ThemeManager* themeManager = ThemeManager::instance();
    themeManager->setTheme(ThemeType::Light);  // Default theme

    // Create main window
    MainWindow* mainWindow = new MainWindow();

    // Restore window geometry and state
    ConfigManager* config = core->getConfigManager();
    if (config) {
        QByteArray geometry = config->getWindowGeometry();
        QByteArray state = config->getWindowState();

        if (!geometry.isEmpty()) {
            mainWindow->restoreGeometry(geometry);
        }

        if (!state.isEmpty()) {
            mainWindow->restoreState(state);
        }
    }

    // Show the window with a delay to allow Qt to properly initialize everything
    QTimer::singleShot(100, mainWindow, &MainWindow::show);
    
    LOG_INFO("Application started successfully", "main");

    // Run application
    int result = app.exec();

    // Save window geometry and state
    if (config) {
        config->setWindowGeometry(mainWindow->saveGeometry());
        config->setWindowState(mainWindow->saveState());
    }

    // Clean up
    delete mainWindow;

    // Shutdown application core
    core->shutdown();

    LOG_INFO("Application terminated", "main");

    // If the application crashed or was killed abruptly, restart it
    if (result == 1) {
        QProcess::startDetached(QApplication::applicationFilePath(), QStringList() << "--restart");
    }

    return result;
} 