cmake_minimum_required(VERSION 3.14)

project(OptimizeApplication VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Find Qt package
find_package(Qt5 5.14 COMPONENTS 
    Core 
    Gui 
    Widgets 
    Charts
    REQUIRED
)

# Set Qt automatic MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/SARibbonBar
)

# Add third-party libraries
add_subdirectory(3rdparty/SARibbonBar)

# Core library sources
set(CORE_SOURCES
    src/core/ApplicationCore.cpp
    src/core/ConfigManager.cpp
    src/core/ThemeManager.cpp
)

set(CORE_HEADERS
    src/core/ApplicationCore.h
    src/core/ConfigManager.h
    src/core/ThemeManager.h
)

# Utils library sources
set(UTILS_SOURCES
    src/utils/Logger.cpp
    src/utils/Utils.cpp
)

set(UTILS_HEADERS
    src/utils/Logger.h
    src/utils/Common.h
)

# Data library sources
set(DATA_SOURCES
    src/data/InputData.cpp
    src/data/IFileExample.cpp
    src/data/InputDataExample.cpp
)

set(DATA_HEADERS
    src/data/InputData.h
    src/data/IFileExample.h
)

# UI library sources
set(UI_SOURCES
    src/ui/mainwindow/MainWindow.cpp
    src/ui/widgets/CustomTreeWidget.cpp
    src/ui/widgets/ParamWidget.cpp
    src/ui/widgets/OptimizeWidget.cpp
    src/ui/widgets/InputWidget.cpp
    src/ui/widgets/OutputWidget.cpp
    src/ui/widgets/ChartWidget.cpp
    src/ui/widgets/RibbonWidget.cpp
    src/ui/widgets/PipeWidget.cpp
    src/ui/widgets/TubeWidget.cpp
    src/ui/widgets/SensitivityWidget.cpp
    src/ui/widgets/SolverOutputWidget.cpp
    src/ui/widgets/UQWidget.cpp
)

set(UI_HEADERS
    src/ui/mainwindow/MainWindow.h
    src/ui/widgets/CustomTreeWidget.h
    src/ui/widgets/ParamWidget.h
    src/ui/widgets/OptimizeWidget.h
    src/ui/widgets/InputWidget.h
    src/ui/widgets/OutputWidget.h
    src/ui/widgets/ChartWidget.h
    src/ui/widgets/RibbonWidget.h
    src/ui/widgets/PipeWidget.h
    src/ui/widgets/TubeWidget.h
    src/ui/widgets/SensitivityWidget.h
    src/ui/widgets/SolverOutputWidget.h
    src/ui/widgets/UQWidget.h
)

# UI files
set(UI_FILES
    src/ui/mainwindow/MainWindow.ui
    src/ui/widgets/OptimizeWidget.ui
    src/ui/widgets/InputWidget.ui
    src/ui/widgets/OutputWidget.ui
    src/ui/widgets/PipeWidget.ui
    src/ui/widgets/TubeWidget.ui
    src/ui/widgets/SensitivityWidget.ui
    src/ui/widgets/SolverOutputWidget.ui
    src/ui/widgets/UQWidget.ui
)

# Resource files
set(RESOURCE_FILES
    resources/icons.qrc
)

# Create core library
add_library(OptimizeCore STATIC
    ${CORE_SOURCES}
    ${CORE_HEADERS}
    ${UTILS_SOURCES}
    ${UTILS_HEADERS}
)

target_include_directories(OptimizeCore PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
    $<INSTALL_INTERFACE:include>
)

target_link_libraries(OptimizeCore PUBLIC
    Qt5::Core
    Qt5::Gui
)

# Create data library
add_library(OptimizeData STATIC
    ${DATA_SOURCES}
    ${DATA_HEADERS}
)

target_include_directories(OptimizeData PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
    $<INSTALL_INTERFACE:include>
)

target_link_libraries(OptimizeData PUBLIC
    OptimizeCore
    Qt5::Core
)

# Create UI library
add_library(OptimizeUI STATIC
    ${UI_SOURCES}
    ${UI_HEADERS}
    ${UI_FILES}
    ${RESOURCE_FILES}
)

target_include_directories(OptimizeUI PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
    $<INSTALL_INTERFACE:include>
)

target_link_libraries(OptimizeUI PUBLIC
    OptimizeCore
    OptimizeData
    SARibbonBar
    Qt5::Widgets
    Qt5::Charts
)

# Create main executable
add_executable(${PROJECT_NAME}
    src/app/main.cpp
)

target_link_libraries(${PROJECT_NAME} PRIVATE
    OptimizeCore
    OptimizeData
    OptimizeUI
    SARibbonBar
    Qt5::Widgets
    Qt5::Charts
)

# Compiler-specific settings
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE 
        /W4 /wd4996 /std:c++17
    )
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
    )
endif()

# Debug/Release configurations
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG_MODE)
    set_target_properties(${PROJECT_NAME} PROPERTIES OUTPUT_NAME "${PROJECT_NAME}_debug")
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE QT_NO_DEBUG_OUTPUT)
endif()

# Installation
install(TARGETS ${PROJECT_NAME} OptimizeCore OptimizeData OptimizeUI
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)
