cmake_minimum_required(VERSION 3.14)

project(OptimizeApplication VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Find Qt package
find_package(Qt5 5.14 COMPONENTS 
    Core 
    Gui 
    Widgets 
    Charts
    REQUIRED
)

# Set Qt automatic MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/SARibbonBar
)

# Add third-party libraries
add_subdirectory(3rdparty/SARibbonBar)

# Core library sources
set(CORE_SOURCES
    src/core/application/ApplicationCore.cpp
    src/core/application/EventBus.cpp
    src/core/application/PluginManager.cpp
    src/core/application/ServiceLocator.cpp
    src/core/config/ConfigManager.cpp
    src/core/theme/ThemeManager.cpp
)

set(CORE_HEADERS
    src/core/application/ApplicationCore.h
    src/core/application/EventBus.h
    src/core/application/PluginManager.h
    src/core/application/ServiceLocator.h
    src/core/config/ConfigManager.h
    src/core/theme/ThemeManager.h
)

# Utils library sources
set(UTILS_SOURCES
    src/utils/logging/LogManager.cpp
    src/utils/common/ErrorHandler.cpp
    src/utils/common/MemoryUtils.cpp
    src/utils/common/PerformanceProfiler.cpp
)

set(UTILS_HEADERS
    src/utils/logging/LogManager.h
    src/utils/common/ErrorHandler.h
    src/utils/common/MemoryUtils.h
    src/utils/common/PerformanceProfiler.h
)

# Data library sources
set(DATA_SOURCES
    src/data/models/BaseModel.cpp
    src/data/models/OptimizationParameters.cpp
    src/data/parsers/IFileParser.cpp
)

set(DATA_HEADERS
    src/data/models/BaseModel.h
    src/data/models/OptimizationParameters.h
    src/data/parsers/IFileParser.h
    src/data/repositories/FileRepository.h
    src/data/repositories/IRepository.h
)

# UI library sources
set(UI_SOURCES
    src/ui/mainwindow/MainWindow.cpp
    src/ui/mainwindow/RibbonManager.cpp
    src/ui/mainwindow/WorkspaceManager.cpp
    src/ui/widgets/ParameterWidgetBase.cpp
)

set(UI_HEADERS
    src/ui/mainwindow/MainWindow.h
    src/ui/mainwindow/RibbonManager.h
    src/ui/mainwindow/WorkspaceManager.h
    src/ui/widgets/IWidget.h
    src/ui/widgets/ParameterWidgetBase.h
)

# UI files
set(UI_FILES
    src/ui/mainwindow/MainWindow.ui
)

# Resource files
set(RESOURCE_FILES
    resources/icons.qrc
)

# Create core library
add_library(OptimizeCore STATIC
    ${CORE_SOURCES}
    ${CORE_HEADERS}
    ${UTILS_SOURCES}
    ${UTILS_HEADERS}
)

target_include_directories(OptimizeCore PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
    $<INSTALL_INTERFACE:include>
)

target_link_libraries(OptimizeCore PUBLIC
    Qt5::Core
    Qt5::Gui
)

# Create data library
add_library(OptimizeData STATIC
    ${DATA_SOURCES}
    ${DATA_HEADERS}
)

target_include_directories(OptimizeData PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
    $<INSTALL_INTERFACE:include>
)

target_link_libraries(OptimizeData PUBLIC
    OptimizeCore
    Qt5::Core
)

# Create UI library
add_library(OptimizeUI STATIC
    ${UI_SOURCES}
    ${UI_HEADERS}
    ${UI_FILES}
    ${RESOURCE_FILES}
)

target_include_directories(OptimizeUI PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/src>
    $<INSTALL_INTERFACE:include>
)

target_link_libraries(OptimizeUI PUBLIC
    OptimizeCore
    OptimizeData
    SARibbonBar
    Qt5::Widgets
    Qt5::Charts
)

# Create main executable
add_executable(${PROJECT_NAME}
    src/app/main.cpp
)

target_link_libraries(${PROJECT_NAME} PRIVATE
    OptimizeCore
    OptimizeData
    OptimizeUI
    SARibbonBar
    Qt5::Widgets
    Qt5::Charts
)

# Compiler-specific settings
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE 
        /W4 /wd4996 /std:c++17
    )
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
    )
endif()

# Debug/Release configurations
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG_MODE)
    set_target_properties(${PROJECT_NAME} PROPERTIES OUTPUT_NAME "${PROJECT_NAME}_debug")
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE QT_NO_DEBUG_OUTPUT)
endif()

# Installation
install(TARGETS ${PROJECT_NAME} OptimizeCore OptimizeData OptimizeUI
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)
