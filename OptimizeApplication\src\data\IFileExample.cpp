#include "IFileExample.h"
#include <QDebug>
#include <QDir>
#include <QFileInfo>

IFileExample::IFileExample(QObject *parent)
    : QObject(parent)
{
    clearError();
}

IFileExample::~IFileExample()
{
}

bool IFileExample::demonstrateIFileReadWrite(const QString& inputFile)
{
    clearError();
    logInfo("=== .i file read/write functionality demonstration ===");
    
    // 1. Load existing .i file
    logInfo(QString("--- Loading .i file: %1 ---").arg(inputFile));
    
    if (!loadAndAnalyzeIFile(inputFile)) {
        return false;
    }
    
    // 2. Save as new .i file (preserve original format)
    logInfo("--- Saving .i file (original format) ---");
    QString outputFile1 = "output/" + QFileInfo(inputFile).baseName() + "_copy.i";
    
    // Ensure output directory exists
    QDir().mkpath("output");
    
    if (m_lastProcessedData.saveToIFile(outputFile1)) {
        logInfo(QString("Successfully saved file: %1").arg(outputFile1));
    } else {
        logError(QString("Failed to save file: %1").arg(outputFile1));
    }
    
    // 3. Export as formatted .i file (no comments)
    logInfo("--- Exporting .i file (formatted, no comments) ---");
    QString outputFile2 = "output/" + QFileInfo(inputFile).baseName() + "_formatted.i";
    
    if (m_lastProcessedData.exportToIFile(outputFile2, false)) {
        logInfo(QString("Successfully exported formatted file: %1").arg(outputFile2));
    } else {
        logError(QString("Failed to export file: %1").arg(outputFile2));
    }
    
    // 4. Save with custom format options
    logInfo("--- Custom format save ---");
    QString outputFile3 = "output/" + QFileInfo(inputFile).baseName() + "_custom.i";
    
    InputData::IFileFormatOptions options = getDefaultFormatOptions();
    
    if (!saveIFileWithOptions(inputFile, outputFile3, options)) {
        logError(QString("Failed to save custom format file: %1").arg(outputFile3));
    }
    
    // 5. Generate .i file content preview
    QString preview = generateIFilePreview(inputFile);
    if (!preview.isEmpty()) {
        logInfo("--- Generated .i file content preview ---");
        logInfo(preview);
    }
    
    // 6. Validate .i file format
    logInfo("--- Validating .i file format ---");
    bool isValid = validateIFile(inputFile);
    logInfo(QString(".i file format validity: %1").arg(isValid ? "Valid" : "Invalid"));
    
    QStringList issues = checkIFileConsistency(inputFile);
    if (!issues.isEmpty()) {
        logInfo("Consistency check found issues:");
        for (const QString& issue : issues) {
            logInfo(QString("  - %1").arg(issue));
        }
    } else {
        logInfo("Consistency check passed");
    }
    
    logInfo("=== .i file read/write demonstration completed ===");
    emit operationCompleted("demonstrateIFileReadWrite", true);
    return true;
}

bool IFileExample::demonstrateCreateNewIFile(const QString& outputFile)
{
    clearError();
    logInfo("=== Creating new .i file demonstration ===");
    
    InputData newInputData;
    
    // Set basic information
    newInputData.problemTitle = "Test Problem Created by IFileExample";
    newInputData.fileType = InputFileType::NC_WELANDER;
    
    // Set control card
    newInputData.controlCard.problemNumber = 100;
    newInputData.controlCard.problemType = "new";
    newInputData.controlCard.analysisType = "transnt";
    newInputData.controlCard.endTime = 1000.0;
    newInputData.controlCard.minTimeStep = 1.0e-6;
    newInputData.controlCard.maxTimeStep = 0.1;
    newInputData.controlCard.controlOption = 3;
    newInputData.controlCard.minorEdit = 10;
    newInputData.controlCard.majorEdit = 100;
    newInputData.controlCard.restartFreq = 1000;
    
    // Set unit system
    newInputData.unitSystem.inputUnits = "si";
    newInputData.unitSystem.outputUnits = "si";
    newInputData.unitSystem.workingFluid = "water";
    newInputData.unitSystem.scaleFactor = 1.0;
    
    // Create a simple pipe component
    PipeComponent pipe;
    pipe.componentId = "1000000";
    pipe.componentName = "test_pipe";
    pipe.componentType = "pipe";
    pipe.numberOfVolumes = 10;
    newInputData.pipes.append(pipe);
    
    // Create a single volume component
    SingleVolumeComponent volume;
    volume.componentId = "2000000";
    volume.componentName = "test_volume";
    volume.componentType = "snglvol";
    volume.volume = 1.0;
    volume.length = 1.0;
    volume.elevation = 0.0;
    volume.pressure = 1.0e5;
    volume.temperature = 300.0;
    newInputData.volumes.append(volume);
    
    // Create a junction component
    JunctionComponent junction;
    junction.componentId = "3000000";
    junction.componentName = "test_junction";
    junction.componentType = "sngljun";
    junction.fromComponent = "1000000";
    junction.toComponent = "2000000";
    junction.area = 0.01;
    junction.forwardLoss = 0.0;
    junction.reverseLoss = 0.0;
    newInputData.junctions.append(junction);
    
    // Add plot variables
    PlotVariable plotVar;
    plotVar.variableId = 20300001;
    plotVar.variableType = "mflowj";
    plotVar.componentId = "3000000";
    plotVar.plotFlag = 1;
    newInputData.plotVariables.append(plotVar);
    
    // Add minor edit variables
    MinorEditVariable editVar;
    editVar.editId = 301;
    editVar.variableType = "p";
    editVar.componentId = "2000000";
    newInputData.minorEditVars.append(editVar);
    
    // Save the newly created .i file
    QDir().mkpath(QFileInfo(outputFile).absolutePath());
    
    if (newInputData.exportToIFile(outputFile, true)) {
        logInfo(QString("Successfully created new .i file: %1").arg(outputFile));
        
        // Display created content
        logInfo("Created file content:");
        QString content = newInputData.generateIFileContent();
        QStringList lines = content.split('\n');
        
        for (int i = 0; i < qMin(30, lines.size()); ++i) {
            logInfo(QString("  %1: %2").arg(i+1, 3).arg(lines[i]));
        }
        
        if (lines.size() > 30) {
            logInfo(QString("  ... (total %1 lines)").arg(lines.size()));
        }
        
        m_lastProcessedData = newInputData;
        emit operationCompleted("demonstrateCreateNewIFile", true);
        return true;
    } else {
        setError(QString("Failed to create new .i file: %1").arg(outputFile));
        emit operationCompleted("demonstrateCreateNewIFile", false);
        return false;
    }
}

bool IFileExample::demonstrateBatchIFileProcessing(const QString& inputDir)
{
    clearError();
    logInfo("=== Batch .i file processing demonstration ===");
    
    QDir dir(inputDir);
    if (!dir.exists()) {
        setError(QString("Input directory does not exist: %1").arg(inputDir));
        return false;
    }
    
    QStringList filters;
    filters << "*.i";
    QFileInfoList fileList = dir.entryInfoList(filters, QDir::Files);
    
    logInfo(QString("Found %1 .i files in directory: %2").arg(fileList.size()).arg(inputDir));
    
    int successCount = 0;
    int failureCount = 0;
    
    for (const QFileInfo& fileInfo : fileList) {
        QString inputFile = fileInfo.absoluteFilePath();
        logInfo(QString("Processing file: %1").arg(fileInfo.fileName()));
        
        if (loadAndAnalyzeIFile(inputFile)) {
            displayFileStatistics(m_lastProcessedData);
            successCount++;
            emit fileProcessed(inputFile, true);
        } else {
            logError(QString("Failed to process file: %1").arg(fileInfo.fileName()));
            failureCount++;
            emit fileProcessed(inputFile, false);
        }
    }
    
    logInfo(QString("Batch processing completed. Success: %1, Failures: %2")
            .arg(successCount).arg(failureCount));
    
    emit operationCompleted("demonstrateBatchIFileProcessing", failureCount == 0);
    return failureCount == 0;
}

bool IFileExample::loadAndAnalyzeIFile(const QString& filePath)
{
    clearError();
    
    InputData inputData;
    bool success = inputData.loadFromIFile(filePath);
    
    if (success) {
        logInfo(QString("Successfully loaded file: %1").arg(inputData.fileName));
        logInfo(QString("File type: %1").arg(InputData::fileTypeToString(inputData.fileType)));
        logInfo(QString("Problem title: %1").arg(inputData.problemTitle));
        logInfo(QString("Total components: %1").arg(inputData.getTotalComponents()));
        
        displayControlCardInfo(inputData);
        displayUnitSystemInfo(inputData);
        displayComponentStatistics(inputData);
        
        m_lastProcessedData = inputData;
        emit fileProcessed(filePath, true);
        return true;
    } else {
        setError(QString("Failed to load file: %1").arg(filePath));
        emit fileProcessed(filePath, false);
        return false;
    }
}

bool IFileExample::saveIFileWithOptions(const QString& inputFile, const QString& outputFile, 
                                        const InputData::IFileFormatOptions& options)
{
    if (!loadAndAnalyzeIFile(inputFile)) {
        return false;
    }
    
    QDir().mkpath(QFileInfo(outputFile).absolutePath());
    
    if (m_lastProcessedData.saveToIFileWithOptions(outputFile, options)) {
        logInfo(QString("Successfully saved custom format file: %1").arg(outputFile));
        return true;
    } else {
        setError(QString("Failed to save custom format file: %1").arg(outputFile));
        return false;
    }
}

bool IFileExample::createSampleIFile(const QString& outputFile)
{
    return demonstrateCreateNewIFile(outputFile);
}

bool IFileExample::validateIFile(const QString& filePath)
{
    if (!loadAndAnalyzeIFile(filePath)) {
        return false;
    }
    
    return m_lastProcessedData.validateIFileFormat();
}

QStringList IFileExample::checkIFileConsistency(const QString& filePath)
{
    if (!loadAndAnalyzeIFile(filePath)) {
        return QStringList() << QString("Failed to load file: %1").arg(filePath);
    }
    
    // Use public validateData method instead of private checkIFileConsistency
    return m_lastProcessedData.validateData();
}

QString IFileExample::generateIFilePreview(const QString& filePath, int maxLines)
{
    if (!loadAndAnalyzeIFile(filePath)) {
        return QString();
    }
    
    QString generatedContent = m_lastProcessedData.generateIFileContent();
    QStringList lines = generatedContent.split('\n');
    
    QStringList preview;
    preview << QString("Generated content first %1 lines:").arg(qMin(maxLines, lines.size()));
    
    for (int i = 0; i < qMin(maxLines, lines.size()); ++i) {
        preview << QString("  %1: %2").arg(i+1, 3).arg(lines[i]);
    }
    
    if (lines.size() > maxLines) {
        preview << QString("  ... (total %1 lines)").arg(lines.size());
    }
    
    return preview.join('\n');
}

void IFileExample::displayFileStatistics(const InputData& data)
{
    logInfo("File Statistics:");
    logInfo(QString("  File name: %1").arg(data.fileName));
    logInfo(QString("  Problem title: %1").arg(data.problemTitle));
    logInfo(QString("  File type: %1").arg(InputData::fileTypeToString(data.fileType)));
    logInfo(QString("  Total components: %1").arg(data.getTotalComponents()));
}

void IFileExample::displayControlCardInfo(const InputData& data)
{
    logInfo("Control card information:");
    logInfo(QString("  Problem number: %1").arg(data.controlCard.problemNumber));
    logInfo(QString("  Problem type: %1").arg(data.controlCard.problemType));
    logInfo(QString("  Analysis type: %1").arg(data.controlCard.analysisType));
    logInfo(QString("  End time: %1").arg(data.controlCard.endTime));
}

void IFileExample::displayUnitSystemInfo(const InputData& data)
{
    logInfo("Unit system:");
    logInfo(QString("  Working fluid: %1").arg(data.unitSystem.workingFluid));
    logInfo(QString("  Input units: %1").arg(data.unitSystem.inputUnits));
    logInfo(QString("  Output units: %1").arg(data.unitSystem.outputUnits));
}

void IFileExample::displayComponentStatistics(const InputData& data)
{
    logInfo("Component statistics:");
    logInfo(QString("  Pipe count: %1").arg(data.getComponentCount("pipe")));
    logInfo(QString("  Branch count: %1").arg(data.getComponentCount("branch")));
    logInfo(QString("  Volume count: %1").arg(data.getComponentCount("snglvol")));
    logInfo(QString("  Junction count: %1").arg(data.getComponentCount("sngljun")));
    
    // Additional detailed component information
    logInfo(QString("Pipes found: %1").arg(data.pipes.size()));
    logInfo(QString("Junctions found: %1").arg(data.junctions.size()));
    logInfo(QString("Volumes found: %1").arg(data.volumes.size()));
    logInfo(QString("Branches found: %1").arg(data.branches.size()));
}

void IFileExample::clearError()
{
    m_lastError.clear();
}

void IFileExample::setError(const QString& error)
{
    m_lastError = error;
    logError(error);
}

void IFileExample::logInfo(const QString& message)
{
    qDebug() << message;
    emit logMessage(QString("[INFO] %1").arg(message));
}

void IFileExample::logError(const QString& message)
{
    qDebug() << "[ERROR]" << message;
    emit logMessage(QString("[ERROR] %1").arg(message));
}

InputData::IFileFormatOptions IFileExample::getDefaultFormatOptions() const
{
    InputData::IFileFormatOptions options;
    options.preserveComments = true;
    options.addSectionHeaders = true;
    options.preserveOriginalSpacing = false;
    options.sortComponents = false;
    options.lineEnding = "\r\n";  // Windows line ending
    return options;
}

 