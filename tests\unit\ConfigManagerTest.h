#ifndef CONFIGMANAGERTEST_H
#define CONFIGMANAGERTEST_H

#include <QtTest>
#include <QObject>
#include <QTemporaryDir>
#include "../../src/core/config/ConfigManager.h"

/**
 * @brief ConfigManager单元测试类
 * 
 * 测试ConfigManager类的各种功能，包括配置的读取、保存、
 * 缓存机制和错误处理等。
 */
class ConfigManagerTest : public QObject
{
    Q_OBJECT

private slots:
    /**
     * @brief 测试初始化
     */
    void initTestCase();

    /**
     * @brief 测试清理
     */
    void cleanupTestCase();

    /**
     * @brief 每个测试前的初始化
     */
    void init();

    /**
     * @brief 每个测试后的清理
     */
    void cleanup();

    /**
     * @brief 测试获取配置值
     */
    void testGetValue();

    /**
     * @brief 测试获取配置值的数据驱动测试
     */
    void testGetValue_data();

    /**
     * @brief 测试设置配置值
     */
    void testSetValue();

    /**
     * @brief 测试配置加载
     */
    void testLoadConfig();

    /**
     * @brief 测试配置保存
     */
    void testSaveConfig();

    /**
     * @brief 测试配置键存在性检查
     */
    void testContainsKey();

    /**
     * @brief 测试移除配置键
     */
    void testRemoveKey();

    /**
     * @brief 测试清空配置
     */
    void testClear();

    /**
     * @brief 测试获取所有配置键
     */
    void testKeys();

    /**
     * @brief 测试配置变更信号
     */
    void testValueChangedSignal();

    /**
     * @brief 测试线程安全性
     */
    void testThreadSafety();

    /**
     * @brief 测试错误处理
     */
    void testErrorHandling();

    /**
     * @brief 测试性能
     */
    void testPerformance();

private:
    /**
     * @brief 创建测试配置文件
     * @param content 配置文件内容
     * @return 配置文件路径
     */
    QString createTestConfigFile(const QString& content);

    /**
     * @brief 验证配置文件内容
     * @param filePath 配置文件路径
     * @param expectedContent 期望的内容
     * @return 验证是否通过
     */
    bool verifyConfigFileContent(const QString& filePath, const QVariantMap& expectedContent);

    QTemporaryDir* m_tempDir;
    ConfigManager* m_configManager;
    QString m_testConfigPath;
};

#endif // CONFIGMANAGERTEST_H
