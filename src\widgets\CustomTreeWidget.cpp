#include "CustomTreeWidget.h"
#include <QHeaderView>
#include <QMouseEvent>
#include <QApplication>
#include <QDrag>
#include <QMimeData>
#include <QByteArray>
#include <QDataStream>
#include <QDebug>
#include <QContextMenuEvent>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QPainter>
#include <QHeaderView>
#include <QInputDialog>
#include <QMenu>
#include <QAction>

CustomTreeWidget::CustomTreeWidget(QWidget *parent)
    : QTreeWidget(parent),
      contextMenu(nullptr),
      actionAddItem(nullptr),
      actionRemoveItem(nullptr),
      actionEditItem(nullptr),
      actionMoveUp(nullptr),
      actionMoveDown(nullptr),
      dragEnabled(false),
      contextMenuEnabled(true),
      dragStartPosition(),
      isEditing(false)
{
    // 设置树控件属性
    setDragEnabled(true);
    setAcceptDrops(true);
    setDragDropMode(QAbstractItemView::InternalMove);
    setSelectionMode(QAbstractItemView::ExtendedSelection);
    setEditTriggers(QAbstractItemView::DoubleClicked | QAbstractItemView::EditKeyPressed);
    setAlternatingRowColors(true);
    
    // 设置表头
    setHeaderHidden(false);
    header()->setSectionResizeMode(QHeaderView::ResizeToContents);
    header()->setStretchLastSection(true);
    
    // 初始化上下文菜单
    initContextMenu();
    
    // 连接信号和槽
    connect(this, &QTreeWidget::itemChanged, this, &CustomTreeWidget::onItemChanged);
}

CustomTreeWidget::~CustomTreeWidget()
{
    if (contextMenu) {
        delete contextMenu;
    }
}

QTreeWidgetItem* CustomTreeWidget::addTopLevelItem(const QString &text, const QIcon &icon, const QVariant &userData)
{
    QTreeWidgetItem *item = new QTreeWidgetItem(this);
    item->setText(0, text);
    if (!icon.isNull()) {
        item->setIcon(0, icon);
    }
    if (userData.isValid()) {
        item->setData(0, Qt::UserRole, userData);
    }
    
    QTreeWidget::addTopLevelItem(item);
    Q_EMIT itemAdded(item);
    
    return item;
}

QTreeWidgetItem* CustomTreeWidget::addChildItem(QTreeWidgetItem *parent, const QString &text, const QIcon &icon, const QVariant &userData)
{
    if (!parent) {
        return nullptr;
    }
    
    QTreeWidgetItem *item = new QTreeWidgetItem(parent);
    item->setText(0, text);
    if (!icon.isNull()) {
        item->setIcon(0, icon);
    }
    if (userData.isValid()) {
        item->setData(0, Qt::UserRole, userData);
    }
    
    parent->addChild(item);
    Q_EMIT itemAdded(item);
    
    return item;
}

void CustomTreeWidget::setDragEnabled(bool enabled)
{
    dragEnabled = enabled;
    QTreeWidget::setDragEnabled(enabled);
    setAcceptDrops(enabled);
    
    if (enabled) {
        setDragDropMode(QAbstractItemView::InternalMove);
    } else {
        setDragDropMode(QAbstractItemView::NoDragDrop);
    }
}

void CustomTreeWidget::setContextMenuEnabled(bool enabled)
{
    contextMenuEnabled = enabled;
}

QTreeWidgetItem* CustomTreeWidget::findItem(const QString &text, int column, Qt::MatchFlags flags, QTreeWidgetItem *startItem)
{
    QList<QTreeWidgetItem*> items;
    if (startItem) {
        // 从指定项开始搜索
        items = QTreeWidget::findItems(text, flags, column);
        int startIndex = items.indexOf(startItem);
        if (startIndex != -1 && startIndex + 1 < items.size()) {
            return items.at(startIndex + 1);
        }
        return nullptr;
    } else {
        // 从头开始搜索
        items = QTreeWidget::findItems(text, flags, column);
        if (!items.isEmpty()) {
            return items.first();
        }
        return nullptr;
    }
}

QList<QTreeWidgetItem*> CustomTreeWidget::getSelectedItems() const
{
    return selectedItems();
}

void CustomTreeWidget::expandAll()
{
    QTreeWidget::expandAll();
}

void CustomTreeWidget::collapseAll()
{
    QTreeWidget::collapseAll();
}

void CustomTreeWidget::onAddItem()
{
    QTreeWidgetItem *current = QTreeWidget::currentItem();
    QTreeWidgetItem *newItem;
    
    if (current) {
        if (current->flags() & Qt::ItemIsDropEnabled) {
            // If current item can accept child items, add a child item
            newItem = addChildItem(current, "New Item");
        } else {
            // Otherwise add to the parent of the current item
            QTreeWidgetItem *parent = current->parent();
            if (parent) {
                newItem = addChildItem(parent, "New Item");
            } else {
                newItem = addTopLevelItem("New Item");
            }
        }
    } else {
        // If no item is selected, add a top level item
        newItem = addTopLevelItem("New Item");
    }
    
    if (newItem) {
        // Select the new item and start editing
        setCurrentItem(newItem);
        editItem(newItem, 0);
    }
}

void CustomTreeWidget::onRemoveItem()
{
    QList<QTreeWidgetItem*> items = selectedItems();
    for (QTreeWidgetItem *item : items) {
        Q_EMIT itemRemoved(item);
        
        QTreeWidgetItem *parent = item->parent();
        if (parent) {
            parent->removeChild(item);
        } else {
            int index = indexOfTopLevelItem(item);
            if (index != -1) {
                takeTopLevelItem(index);
            }
        }
        
        delete item;
    }
}

void CustomTreeWidget::onEditItem()
{
    QTreeWidgetItem *item = currentItem();
    if (item) {
        editItem(item, 0);
    }
}

void CustomTreeWidget::onMoveItemUp()
{
    QTreeWidgetItem *item = currentItem();
    if (!item) {
        return;
    }
    
    QTreeWidgetItem *parent = item->parent();
    int index;
    
    if (parent) {
        // 如果是子项
        index = parent->indexOfChild(item);
        if (index <= 0) {
            return; // 已经是第一个，无法上移
        }
        
        // 从父项中移除
        parent->removeChild(item);
        
        // 插入到上一个位置
        parent->insertChild(index - 1, item);
        
        // 更新选中状态
        setCurrentItem(item);
        
        Q_EMIT itemMoved(item, parent, parent);
    } else {
        // 如果是顶级项
        index = indexOfTopLevelItem(item);
        if (index <= 0) {
            return; // 已经是第一个，无法上移
        }
        
        // 从顶级项中移除
        takeTopLevelItem(index);
        
        // 插入到上一个位置
        insertTopLevelItem(index - 1, item);
        
        // 更新选中状态
        setCurrentItem(item);
        
        Q_EMIT itemMoved(item, nullptr, nullptr);
    }
}

void CustomTreeWidget::onMoveItemDown()
{
    QTreeWidgetItem *item = currentItem();
    if (!item) {
        return;
    }
    
    QTreeWidgetItem *parent = item->parent();
    int index;
    
    if (parent) {
        // 如果是子项
        index = parent->indexOfChild(item);
        if (index >= parent->childCount() - 1) {
            return; // 已经是最后一个，无法下移
        }
        
        // 从父项中移除
        parent->removeChild(item);
        
        // 插入到下一个位置
        parent->insertChild(index + 1, item);
        
        // 更新选中状态
        setCurrentItem(item);
        
        Q_EMIT itemMoved(item, parent, parent);
    } else {
        // 如果是顶级项
        index = indexOfTopLevelItem(item);
        if (index >= topLevelItemCount() - 1) {
            return; // 已经是最后一个，无法下移
        }
        
        // 从顶级项中移除
        takeTopLevelItem(index);
        
        // 插入到下一个位置
        insertTopLevelItem(index + 1, item);
        
        // 更新选中状态
        setCurrentItem(item);
        
        Q_EMIT itemMoved(item, nullptr, nullptr);
    }
}

void CustomTreeWidget::contextMenuEvent(QContextMenuEvent *event)
{
    if (!contextMenuEnabled || !contextMenu) {
        QTreeWidget::contextMenuEvent(event);
        return;
    }
    
    QTreeWidgetItem *item = itemAt(event->pos());
    updateContextMenu(item);
    
    contextMenu->exec(event->globalPos());
    
    if (item) {
        Q_EMIT contextMenuRequested(item, event->pos());
    }
    
    event->accept();
}

void CustomTreeWidget::dragEnterEvent(QDragEnterEvent *event)
{
    if (!dragEnabled) {
        event->ignore();
        return;
    }
    
    if (event->mimeData()->hasFormat("application/x-qabstractitemmodeldatalist")) {
        event->acceptProposedAction();
    } else {
        event->ignore();
    }
}

void CustomTreeWidget::dragMoveEvent(QDragMoveEvent *event)
{
    if (!dragEnabled) {
        event->ignore();
        return;
    }
    
    QTreeWidgetItem *dropItem = itemAt(event->pos());
    if (canDrop(event, dropItem)) {
        event->acceptProposedAction();
    } else {
        event->ignore();
    }
}

void CustomTreeWidget::dropEvent(QDropEvent *event)
{
    if (!dragEnabled) {
        event->ignore();
        return;
    }
    
    QTreeWidgetItem *dropItem = itemAt(event->pos());
    handleDrop(event, dropItem);
}

void CustomTreeWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        dragStartPosition = event->pos();
    }
    
    QTreeWidget::mousePressEvent(event);
}

void CustomTreeWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (!dragEnabled || !(event->buttons() & Qt::LeftButton)) {
        QTreeWidget::mouseMoveEvent(event);
        return;
    }
    
    // 检查是否达到拖拽启动条件
    if ((event->pos() - dragStartPosition).manhattanLength() < QApplication::startDragDistance()) {
        QTreeWidget::mouseMoveEvent(event);
        return;
    }
    
    QTreeWidgetItem *item = itemAt(dragStartPosition);
    if (!item) {
        QTreeWidget::mouseMoveEvent(event);
        return;
    }
    
    // 创建拖拽对象
    QDrag *drag = new QDrag(this);
    QMimeData *mimeData = createMimeData();
    drag->setMimeData(mimeData);
    
    // 设置拖拽图标
    QPixmap pixmap(item->sizeHint(0));
    pixmap.fill(Qt::transparent);
    
    QPainter painter(&pixmap);
    QStyleOptionViewItem option;
    option.rect = QRect(0, 0, pixmap.width(), pixmap.height());
    itemDelegate()->paint(&painter, option, indexFromItem(item));
    
    drag->setPixmap(pixmap);
    drag->setHotSpot(QPoint(pixmap.width() / 2, pixmap.height() / 2));
    
    // 执行拖拽
    Qt::DropAction dropAction = drag->exec(Qt::MoveAction | Qt::CopyAction);
    
    QTreeWidget::mouseMoveEvent(event);
}

void CustomTreeWidget::mouseReleaseEvent(QMouseEvent *event)
{
    QTreeWidget::mouseReleaseEvent(event);
}

void CustomTreeWidget::onItemChanged(QTreeWidgetItem *item, int column)
{
    if (isEditing) {
        return;
    }
    
    isEditing = true;
    Q_EMIT itemEdited(item, column, item->text(column));
    isEditing = false;
}

void CustomTreeWidget::showContextMenu(const QPoint &pos)
{
    if (!contextMenuEnabled || !contextMenu) {
        return;
    }
    
    QTreeWidgetItem *item = itemAt(pos);
    updateContextMenu(item);
    
    contextMenu->exec(mapToGlobal(pos));
    
    if (item) {
        Q_EMIT contextMenuRequested(item, pos);
    }
}

void CustomTreeWidget::initContextMenu()
{
    contextMenu = new QMenu(this);
    
    actionAddItem = contextMenu->addAction("Add");
    actionRemoveItem = contextMenu->addAction("Remove");
    actionEditItem = contextMenu->addAction("Edit");
    
    contextMenu->addSeparator();
    
    actionMoveUp = contextMenu->addAction("Move Up");
    actionMoveDown = contextMenu->addAction("Move Down");
    
    connect(actionAddItem, &QAction::triggered, this, &CustomTreeWidget::onAddItem);
    connect(actionRemoveItem, &QAction::triggered, this, &CustomTreeWidget::onRemoveItem);
    connect(actionEditItem, &QAction::triggered, this, &CustomTreeWidget::onEditItem);
    connect(actionMoveUp, &QAction::triggered, this, &CustomTreeWidget::onMoveItemUp);
    connect(actionMoveDown, &QAction::triggered, this, &CustomTreeWidget::onMoveItemDown);
    
    setContextMenuPolicy(Qt::CustomContextMenu);
    connect(this, &QWidget::customContextMenuRequested, this, &CustomTreeWidget::showContextMenu);
}

void CustomTreeWidget::updateContextMenu(QTreeWidgetItem *item)
{
    bool hasItem = (item != nullptr);
    bool hasParent = (hasItem && item->parent() != nullptr);
    bool isTopLevel = (hasItem && item->parent() == nullptr);
    
    actionAddItem->setEnabled(true);
    actionRemoveItem->setEnabled(hasItem);
    actionEditItem->setEnabled(hasItem);
    
    // 对于顶级项，只有在不是第一个时才能上移
    actionMoveUp->setEnabled(hasItem && 
                           ((isTopLevel && indexOfTopLevelItem(item) > 0) || 
                            (hasParent && item->parent()->indexOfChild(item) > 0)));
    
    // 对于顶级项，只有在不是最后一个时才能下移
    actionMoveDown->setEnabled(hasItem && 
                             ((isTopLevel && indexOfTopLevelItem(item) < topLevelItemCount() - 1) || 
                              (hasParent && item->parent()->indexOfChild(item) < item->parent()->childCount() - 1)));
}

QMimeData* CustomTreeWidget::createMimeData() const
{
    QList<QTreeWidgetItem*> items = selectedItems();
    if (items.isEmpty()) {
        return nullptr;
    }
    
    QMimeData *mimeData = new QMimeData();
    
    // 将选中项的索引序列化为字节数组
    QByteArray itemData;
    QDataStream dataStream(&itemData, QIODevice::WriteOnly);
    
    for (QTreeWidgetItem *item : items) {
        if (item->parent()) {
            dataStream << true << indexFromItem(item->parent()).row() << item->parent()->indexOfChild(item);
        } else {
            dataStream << false << indexOfTopLevelItem(item) << -1;
        }
    }
    
    mimeData->setData("application/x-qabstractitemmodeldatalist", itemData);
    return mimeData;
}

bool CustomTreeWidget::canDrop(QDropEvent *event, QTreeWidgetItem *dropItem) const
{
    if (!event->mimeData()->hasFormat("application/x-qabstractitemmodeldatalist")) {
        return false;
    }
    
    // 检查拖拽源和目标是否兼容
    QList<QTreeWidgetItem*> selectedItems = getSelectedItems();
    
    for (QTreeWidgetItem *item : selectedItems) {
        // 不能将项拖放到其自身或其子项上
        if (dropItem == item) {
            return false;
        }
        
        if (dropItem) {
            QTreeWidgetItem *parent = dropItem;
            while (parent) {
                if (parent == item) {
                    return false;
                }
                parent = parent->parent();
            }
        }
    }
    
    return true;
}

void CustomTreeWidget::handleDrop(QDropEvent *event, QTreeWidgetItem *dropItem)
{
    if (!canDrop(event, dropItem)) {
        event->ignore();
        return;
    }
    
    QList<QTreeWidgetItem*> items = selectedItems();
    if (items.isEmpty()) {
        event->ignore();
        return;
    }
    
    // 防止在处理拖放过程中修改选择
    setSelectionMode(QAbstractItemView::NoSelection);
    
    for (QTreeWidgetItem *item : items) {
        QTreeWidgetItem *oldParent = item->parent();
        
        // 从原位置移除
        if (oldParent) {
            oldParent->removeChild(item);
        } else {
            int index = indexOfTopLevelItem(item);
            if (index != -1) {
                takeTopLevelItem(index);
            }
        }
        
        // 添加到新位置
        if (dropItem) {
            if (dropItem->flags() & Qt::ItemIsDropEnabled) {
                // 放到目标项中作为子项
                dropItem->addChild(item);
                Q_EMIT itemMoved(item, dropItem, oldParent);
            } else if (dropItem->parent()) {
                // 放到目标项的父项中作为兄弟项
                int index = dropItem->parent()->indexOfChild(dropItem);
                dropItem->parent()->insertChild(index, item);
                Q_EMIT itemMoved(item, dropItem->parent(), oldParent);
            } else {
                // 放到根级别
                int index = indexOfTopLevelItem(dropItem);
                insertTopLevelItem(index, item);
                Q_EMIT itemMoved(item, nullptr, oldParent);
            }
        } else {
            // 没有目标项，添加为顶级项
            QTreeWidget::addTopLevelItem(item);
            Q_EMIT itemMoved(item, nullptr, oldParent);
        }
    }
    
    // 恢复选择模式
    setSelectionMode(QAbstractItemView::ExtendedSelection);
    
    // 更新选中状态
    clearSelection();
    for (QTreeWidgetItem *item : items) {
        item->setSelected(true);
    }
    
    event->acceptProposedAction();
} 