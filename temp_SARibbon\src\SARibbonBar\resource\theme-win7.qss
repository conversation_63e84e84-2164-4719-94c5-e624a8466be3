﻿/* 
start ribbon set
win7 style theme
*/

/*SARibbonBar*/

SARibbonBar{
  background-color: #E3E6E8;
  border: solid #707070;
}



/*SARibbonQuickAccessBar*/
SARibbonQuickAccessBar{
    background-color: #FF0000;
}


/*SARibbonCtrlContainer*/
SARibbonCtrlContainer{
    background-color: transparent;
}

/*
SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
如果想给不同的部件下的分割线设置不同风格，可以使用如SARibbonCategory > SARibbonSeparatorWidget的方式，参考theme-dark2.qss
*/
SARibbonSeparatorWidget {
  color: #bec0c2;
}

/*SARibbonCategory*/
SARibbonCategory:focus{
  outline: none;
}
SARibbonCategory{
  background-color: white;
}

/*
SARibbonCategory下的SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
*/
SARibbonCategory > SARibbonSeparatorWidget {
  color: #bec0c2;
  margin-top:3px;
  margin-bottom:3px;
}

/*SARibbonPannel*/
SARibbonPannel {
  background-color: transparent;
  border: none;
  color: #444444;
}

/*SARibbonPannelLabel Pannel下标题栏*/

SARibbonPannelLabel {
    background-color: transparent;
    color: #666666;
}


/*SARibbonPannelOptionButton*/
SARibbonPannelOptionButton {
  border: none;
  background-color: transparent;
  color:#444444;
}

SARibbonPannelOptionButton:hover {
  background-color: #FDEEB3;
}


/*SARibbonButtonGroupWidget*/
SARibbonButtonGroupWidget{
  background-color: transparent;
}

SARibbonPannel > SARibbonButtonGroupWidget {
  border: 1px solid #888888;
  background-color: transparent;
}


/*SARibbonStackedWidget*/

SARibbonStackedWidget:focus{
  outline: none;
}

/*SARibbonApplicationButton*/
SARibbonApplicationButton{
  color:white;
  border: 1px solid #416ABD;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #467FBD, stop:0.5 #2A5FAC,stop:0.51 #1A4088,
stop:1 #419ACF);
}

SARibbonApplicationButton:hover{
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #7BB2EB, stop:0.5 #477ECD,stop:0.51 #114ECF,
stop:1 #80E1FF);
}

SARibbonApplicationButton:pressed{
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #467BBB, stop:0.5 #2F63AE,stop:0.51 #1C438A,
stop:1 #358BC9);
}

SARibbonApplicationButton:focus{
  outline: none;
}

SARibbonApplicationButton::menu-indicator {
  /*subcontrol-position: right;*/
  width:0px;
}

/*SARibbonTabBar*/
SARibbonTabBar{
    background-color: transparent;
}

SARibbonTabBar::tab {
    color:#444444;
    border:1px solid transparent;/*加上边框，否则选中后加入边框后会有1px的偏移*/
    background: transparent;
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 5px;
    margin-bottom: 0px;
    min-width: 50px;
    padding-left: 5px;
    padding-right: 5px;
}

SARibbonTabBar::tab:selected, SARibbonTabBar::tab:hover {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

SARibbonTabBar::tab:selected{
    color:#000000;
    border: 1px solid #BAC9DB;
    background: white;
    border-bottom-color: #FFFFFF;
}

SARibbonTabBar::tab:hover:!selected{
    border: 1px solid #ECBC3D;
    color: #000000;
}


/*SARibbonCheckBox*/
SARibbonCheckBox{
  color:#444444;
  background-color:transparent;
}

/*SARibbonLineEdit*/
SARibbonLineEdit {
    color:#444444;
    border: 1px solid #C0C2C4;
    background: #FFF;
    selection-background-color: #9BBBF7;
    selection-color: #000;
}

/*SARibbonToolButton*/
SARibbonToolButton{
    border:none;
    color:#444444;
    background-color:white;
}
SARibbonToolButton:focus {
  border: 1px solid #FCBF21;
  color: #333;
  background-color: #FCD364;
}
SARibbonToolButton:pressed{
    color:#444444;
    background-color: #FDEEB3;
}
SARibbonToolButton:checked{
    color:#444444;
    background-color: #FDEEB3;
}
SARibbonToolButton:hover {
    color:#000000;
    background-color: #FDEEB3;
}

/*SARibbonControlToolButton*/

SARibbonControlToolButton {
  background-color: transparent;/*{ControlToolButton.BKColor}*/
  color: #444444;
  border: none;
}

SARibbonControlToolButton:pressed {
  background-color: #FDEEB3;/*{ControlToolButton.BKColor:pressed}*/
}

SARibbonControlToolButton:checked {
  border: 1px solid #face54;/*{ControlToolButton.BorderColor:checked}*/
  background-color: #FDEEB3;/*{ControlToolButton.BKColor:checked}*/
}

SARibbonControlToolButton:hover {
  background-color: #FDEEB3;/*{ControlToolButton.BKColor:hover}*/
}

/*SARibbonControlButton*/
SARibbonControlButton{
  background-color:transparent;
  border: none;
  color:#444444;
}
SARibbonControlButton:pressed{
  background-color: #FDEEB3;
}
SARibbonControlButton:checked{
  border: 1px solid #face54;
  background-color: #FDEEB3;
}
SARibbonControlButton:hover {
  background-color: #FDEEB3;
 }
SARibbonGalleryButton{
  border: 1px solid #C0C2C4;
}




/*SARibbonMenu*/
SARibbonMenu {
  color:#444444;
  background-color: #FCFCFC;
  border: 1px solid #8492A6;
}
SARibbonMenu::item {
  padding: 5px;
  background-color: transparent;
}
SARibbonMenu::item:selected {
  background-color: #FDEEB3;
}
SARibbonMenu::item:hover {
    color:#000;
    border: 1px solid #FDEEB3;
}
SARibbonMenu::icon{
  margin-left: 1px;
}



/*SARibbonGallery*/
SARibbonGallery {
  background-color: transparent;
  color: #444444;
}

/*SARibbonGalleryGroup*/
SARibbonGalleryGroup {
  show-decoration-selected: 1;
  background-color: transparent;
  color: #444444;
  border: 1px solid #C0C2C4;
}
SARibbonGalleryGroup::item:selected {
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #FDEEB3, stop:0.1282 #FDE38A,stop:0.8333
#FCE58C, stop:1 #FDFDEB);
  color: #444444;
}
SARibbonGalleryGroup::item:hover {
  border: 1px solid #FDEEB3;
}

/*RibbonGalleryViewport*/

RibbonGalleryViewport {
  background-color: white;
}

/*SARibbonComboBox*/
SARibbonComboBox {
  border: 1px solid #C0C2C4;
  background-color: white;
}

SARibbonComboBox:hover{
  border: 1px solid #FDEEB3;
  color : #444444;
}

SARibbonComboBox:editable {
  color : #444444;
  background: white;
  selection-background-color: #9BBBF7;
  selection-color: #000;
}

SARibbonComboBox::drop-down {
  subcontrol-origin: padding;
  subcontrol-position: top right;
  width: 1em;
  border: none;
}

SARibbonComboBox::drop-down:hover {
  border: none;
  background-color: #FDEEB3;
}
SARibbonComboBox::down-arrow {
  image: url(:/SARibbon/image/resource/ArrowDown.png);
}



SARibbonCategoryScrollButton {
  border: 1px solid #c5d2e0;
  color: #444444;
  background-color: white;
}

SARibbonCategoryScrollButton[arrowType="3"] {
  border-right-width: 1px;
}

SARibbonCategoryScrollButton[arrowType="4"] {
  border-left-width: 1px;
}

/*SARibbonSystemToolButton*/

SARibbonSystemToolButton {
  background-color: transparent;
  border:none;
}

SARibbonSystemToolButton:focus {
  outline: none;
}
/*Min*/
SARibbonSystemToolButton#SAMinimizeWindowButton {
  background-position:center;
  background-repeat: no-repeat;
  background-image: url(:/SARibbon/image/resource/Titlebar_Min.svg);
}
SARibbonSystemToolButton#SAMinimizeWindowButton:hover{
  background-color: #f5f6f6;
}
SARibbonSystemToolButton#SAMinimizeWindowButton:pressed{
  background-color: #cacacb;
}
/*Max*/
SARibbonSystemToolButton#SAMaximizeWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/SARibbon/image/resource/Titlebar_Max.svg);
}
SARibbonSystemToolButton#SAMaximizeWindowButton:hover {
    background-color: #f5f6f6;
}
SARibbonSystemToolButton#SAMaximizeWindowButton:checked {
    background-image: url(:/SARibbon/image/resource/Titlebar_Normal.svg) center;
}


/*Close*/
SARibbonSystemToolButton#SACloseWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/SARibbon/image/resource/Titlebar_Close.svg);
}
SARibbonSystemToolButton#SACloseWindowButton:hover {
  background-color: #e81123;
}
SARibbonSystemToolButton#SACloseWindowButton:pressed {
    background-color: #f1707a;
}

