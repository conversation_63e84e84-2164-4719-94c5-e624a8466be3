#ifndef CUSTOMTREEWIDGET_H
#define CUSTOMTREEWIDGET_H

#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QMenu>
#include <QAction>
#include <QPoint>
#include <QContextMenuEvent>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QMimeData>

/**
 * @brief 自定义树控件类，提供拖拽和上下文菜单功能
 */
class CustomTreeWidget : public QTreeWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit CustomTreeWidget(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~CustomTreeWidget();

    /**
     * @brief 添加顶级项
     * @param text 项文本
     * @param icon 项图标
     * @param userData 用户数据
     * @return 新添加的项
     */
    QTreeWidgetItem* addTopLevelItem(const QString &text, const QIcon &icon = QIcon(), const QVariant &userData = QVariant());

    /**
     * @brief 添加子项
     * @param parent 父项
     * @param text 项文本
     * @param icon 项图标
     * @param userData 用户数据
     * @return 新添加的项
     */
    QTreeWidgetItem* addChildItem(QTreeWidgetItem *parent, const QString &text, const QIcon &icon = QIcon(), const QVariant &userData = QVariant());

    /**
     * @brief 设置是否启用拖拽功能
     * @param enabled 是否启用
     */
    void setDragEnabled(bool enabled);

    /**
     * @brief 设置是否启用上下文菜单
     * @param enabled 是否启用
     */
    void setContextMenuEnabled(bool enabled);

    /**
     * @brief 查找项
     * @param text 要查找的文本
     * @param column 要查找的列
     * @param flags 查找标志
     * @param startItem 开始查找的项，如果为nullptr则从头开始查找
     * @return 找到的项，如果未找到则返回nullptr
     */
    QTreeWidgetItem* findItem(const QString &text, int column = 0, Qt::MatchFlags flags = Qt::MatchExactly, QTreeWidgetItem *startItem = nullptr);

    /**
     * @brief 获取选中项
     * @return 选中项列表
     */
    QList<QTreeWidgetItem*> getSelectedItems() const;

    /**
     * @brief 展开所有项
     */
    void expandAll();

    /**
     * @brief 折叠所有项
     */
    void collapseAll();

Q_SIGNALS:
    /**
     * @brief 项被添加信号
     * @param item 被添加的项
     */
    void itemAdded(QTreeWidgetItem *item);

    /**
     * @brief 项被删除信号
     * @param item 被删除的项
     */
    void itemRemoved(QTreeWidgetItem *item);

    /**
     * @brief 项被移动信号
     * @param item 被移动的项
     * @param newParent 新的父项
     * @param oldParent 旧的父项
     */
    void itemMoved(QTreeWidgetItem *item, QTreeWidgetItem *newParent, QTreeWidgetItem *oldParent);

    /**
     * @brief 项被编辑信号
     * @param item 被编辑的项
     * @param column 被编辑的列
     * @param text 新的文本
     */
    void itemEdited(QTreeWidgetItem *item, int column, const QString &text);

    /**
     * @brief 上下文菜单请求信号
     * @param item 请求上下文菜单的项
     * @param pos 请求位置
     */
    void contextMenuRequested(QTreeWidgetItem *item, const QPoint &pos);

public Q_SLOTS:
    /**
     * @brief 添加项槽
     */
    void onAddItem();

    /**
     * @brief 删除项槽
     */
    void onRemoveItem();

    /**
     * @brief 编辑项槽
     */
    void onEditItem();

    /**
     * @brief 上移项槽
     */
    void onMoveItemUp();

    /**
     * @brief 下移项槽
     */
    void onMoveItemDown();

protected:
    /**
     * @brief 上下文菜单事件处理
     * @param event 事件对象
     */
    void contextMenuEvent(QContextMenuEvent *event) override;

    /**
     * @brief 拖拽进入事件处理
     * @param event 事件对象
     */
    void dragEnterEvent(QDragEnterEvent *event) override;

    /**
     * @brief 拖拽移动事件处理
     * @param event 事件对象
     */
    void dragMoveEvent(QDragMoveEvent *event) override;

    /**
     * @brief 放下事件处理
     * @param event 事件对象
     */
    void dropEvent(QDropEvent *event) override;

    /**
     * @brief 鼠标按下事件处理
     * @param event 事件对象
     */
    void mousePressEvent(QMouseEvent *event) override;

    /**
     * @brief 鼠标移动事件处理
     * @param event 事件对象
     */
    void mouseMoveEvent(QMouseEvent *event) override;

    /**
     * @brief 鼠标释放事件处理
     * @param event 事件对象
     */
    void mouseReleaseEvent(QMouseEvent *event) override;

private Q_SLOTS:
    /**
     * @brief 处理项编辑完成
     * @param item 编辑的项
     * @param column 编辑的列
     */
    void onItemChanged(QTreeWidgetItem *item, int column);

    /**
     * @brief 显示上下文菜单
     * @param pos 菜单位置
     */
    void showContextMenu(const QPoint &pos);

private:
    /**
     * @brief 初始化上下文菜单
     */
    void initContextMenu();

    /**
     * @brief 更新上下文菜单
     * @param item 当前项
     */
    void updateContextMenu(QTreeWidgetItem *item);

    /**
     * @brief 创建并返回MIME数据
     * @return MIME数据
     */
    QMimeData* createMimeData() const;

    /**
     * @brief 检查是否可以放下
     * @param event 拖拽事件
     * @param dropItem 放下位置的项
     * @return 是否可以放下
     */
    bool canDrop(QDropEvent *event, QTreeWidgetItem *dropItem) const;

    /**
     * @brief 处理放下操作
     * @param event 放下事件
     * @param dropItem 放下位置的项
     */
    void handleDrop(QDropEvent *event, QTreeWidgetItem *dropItem);

    QMenu *contextMenu;                     ///< 上下文菜单
    QAction *actionAddItem;                 ///< 添加项动作
    QAction *actionRemoveItem;              ///< 删除项动作
    QAction *actionEditItem;                ///< 编辑项动作
    QAction *actionMoveUp;                  ///< 上移动作
    QAction *actionMoveDown;                ///< 下移动作

    bool dragEnabled;                       ///< 是否启用拖拽
    bool contextMenuEnabled;                ///< 是否启用上下文菜单
    QPoint dragStartPosition;               ///< 拖拽开始位置
    bool isEditing;                         ///< 是否正在编辑
};

#endif // CUSTOMTREEWIDGET_H 
