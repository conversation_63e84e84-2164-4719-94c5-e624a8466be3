# Cursor Rules for Qt5.14.2 Development
# Qt5.14.2 开发环境专用规则配置

## Qt Development Environment

### Qt Version and Compiler
- Use Qt5.14.2_msvc2017_x64
- QMake Path: D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/qmake
- Compiler: MSVC 2017 x64
- Target Platform: Windows 10

### Language Rules
- Code and comments MUST be in English only
- NO Chinese characters in code, variable names, function names, or comments
- Documentation can be in Chinese, but code annotations must be English
- Use English naming conventions for all identifiers

## Qt5.14.2 Specific Rules

### Rule 1: Qt Headers and Includes
Always use Qt5-style includes:
```cpp
#include <QtWidgets/QApplication>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>
#include <QtCore/QObject>
#include <QtCore/QString>
#include <QtCore/QDebug>
#include <QtGui/QPixmap>
#include <QtGui/QIcon>
```

### Rule 2: Qt Object Creation and Memory Management
- Always use parent-child relationship for Qt objects
- Use `new` with parent parameter when creating widgets
- Avoid manual `delete` calls for widgets with parents
- Use smart pointers (QScopedPointer, QSharedPointer) when appropriate

Example:
```cpp
// Correct way
QWidget* widget = new QWidget(this);
QLabel* label = new QLabel("Text", widget);

// For non-widget objects
QScopedPointer<QTimer> timer(new QTimer);
```

### Rule 3: Signal-Slot Connections
Prefer new-style signal-slot syntax (Qt5):
```cpp
// Preferred Qt5 syntax
connect(button, &QPushButton::clicked, this, &MainWindow::onButtonClicked);

// Avoid old syntax unless necessary
// connect(button, SIGNAL(clicked()), this, SLOT(onButtonClicked()));
```

### Rule 4: Qt Container Classes
Use Qt container classes consistently:
```cpp
QList<QString> stringList;
QVector<int> intVector;
QMap<QString, QVariant> dataMap;
QHash<QString, QObject*> objectHash;
```

### Rule 5: Qt String Handling
- Use QString for all string operations
- Use QStringLiteral for string literals when possible
- Use QString::arg() for string formatting

```cpp
QString message = QStringLiteral("Processing item %1 of %2").arg(current).arg(total);
```

### Rule 6: Qt Designer Integration
- Always use .ui files for complex layouts
- Connect UI elements in setupUi() or constructor
- Use meaningful object names in Designer
- Follow naming convention: widgetTypeDescription (e.g., pushButtonSave, lineEditName)

### Rule 7: Qt Resource System
- Use .qrc files for resources
- Prefix resources with descriptive paths
- Example resource usage:
```cpp
QIcon icon(":/icons/application/save.png");
QPixmap pixmap(":/images/backgrounds/main.png");
```

### Rule 8: Qt Model-View Architecture
When using Model-View:
```cpp
// Use Qt's model-view classes
QStandardItemModel* model = new QStandardItemModel(this);
QTableView* view = new QTableView(this);
view->setModel(model);

// Custom models should inherit from appropriate base
class CustomModel : public QAbstractTableModel
```

### Rule 9: Qt Threading
- Use QThread for threading
- Move worker objects to threads using moveToThread()
- Use signals/slots for thread communication
- Never access UI directly from worker threads

```cpp
QThread* workerThread = new QThread(this);
Worker* worker = new Worker;
worker->moveToThread(workerThread);
connect(workerThread, &QThread::started, worker, &Worker::process);
connect(worker, &Worker::finished, workerThread, &QThread::quit);
```

### Rule 10: Qt Debugging and Logging
Use Qt's logging system:
```cpp
#include <QDebug>
#include <QLoggingCategory>

Q_DECLARE_LOGGING_CATEGORY(myCategory)
Q_LOGGING_CATEGORY(myCategory, "app.category")

qDebug() << "Debug message";
qWarning() << "Warning message";
qCritical() << "Critical error";
qCDebug(myCategory) << "Category debug";
```

## Project Structure Rules

### Rule 11: File Organization
```
src/
├── main.cpp
├── ui/
│   ├── MainWindow.h
│   ├── MainWindow.cpp
│   └── MainWindow.ui
├── widgets/
│   ├── CustomWidget.h
│   ├── CustomWidget.cpp
│   └── CustomWidget.ui
├── core/
│   ├── Application.h
│   └── Application.cpp
└── resources/
    ├── icons.qrc
    └── images.qrc
```

### Rule 12: Header File Structure
```cpp
#ifndef CLASSNAME_H
#define CLASSNAME_H

#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE
class QLabel;
class QPushButton;
QT_END_NAMESPACE

class ClassName : public QWidget
{
    Q_OBJECT

public:
    explicit ClassName(QWidget *parent = nullptr);
    ~ClassName();

private slots:
    void onButtonClicked();

private:
    void setupUI();
    void setupConnections();
    
    QLabel *m_label;
    QPushButton *m_button;
};

#endif // CLASSNAME_H
```

### Rule 13: Implementation File Structure
```cpp
#include "ClassName.h"

#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QVBoxLayout>

ClassName::ClassName(QWidget *parent)
    : QWidget(parent)
    , m_label(nullptr)
    , m_button(nullptr)
{
    setupUI();
    setupConnections();
}

ClassName::~ClassName()
{
    // Destructor - Qt handles cleanup for child objects
}

void ClassName::setupUI()
{
    m_label = new QLabel("Hello Qt", this);
    m_button = new QPushButton("Click Me", this);
    
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->addWidget(m_label);
    layout->addWidget(m_button);
}

void ClassName::setupConnections()
{
    connect(m_button, &QPushButton::clicked, this, &ClassName::onButtonClicked);
}

void ClassName::onButtonClicked()
{
    // Handle button click
}
```

## Build System Rules

### Rule 14: QMake Project File (.pro)
```pro
QT += core widgets

CONFIG += c++11

TARGET = ApplicationName
TEMPLATE = app

# Source files
SOURCES += \
    src/main.cpp \
    src/ui/MainWindow.cpp \
    src/widgets/CustomWidget.cpp

# Header files
HEADERS += \
    src/ui/MainWindow.h \
    src/widgets/CustomWidget.h

# UI files
FORMS += \
    src/ui/MainWindow.ui \
    src/widgets/CustomWidget.ui

# Resource files
RESOURCES += \
    resources/icons.qrc \
    resources/images.qrc

# Include paths
INCLUDEPATH += src

# Output directories
DESTDIR = bin
OBJECTS_DIR = build/obj
MOC_DIR = build/moc
UI_DIR = build/ui
RCC_DIR = build/rcc
```

### Rule 15: CMake Alternative (if needed)
```cmake
cmake_minimum_required(VERSION 3.16)
project(ApplicationName)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(Qt5 REQUIRED COMPONENTS Core Widgets)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

add_executable(ApplicationName
    src/main.cpp
    src/ui/MainWindow.cpp
    src/ui/MainWindow.h
    src/ui/MainWindow.ui
)

target_link_libraries(ApplicationName Qt5::Core Qt5::Widgets)
```

## Coding Standards

### Rule 16: Naming Conventions
- Classes: PascalCase (MainWindow, CustomWidget)
- Functions/Methods: camelCase (setupUI, onButtonClicked)
- Variables: camelCase with prefix (m_button, m_layout)
- Constants: UPPER_CASE (MAX_ITEMS, DEFAULT_SIZE)
- Private members: m_ prefix (m_button, m_model)

### Rule 17: Code Formatting
- Indentation: 4 spaces
- Braces: Same line for functions, new line for classes
- Line length: Maximum 100 characters
- Use spaces around operators

### Rule 18: Documentation
Use Qt-style documentation:
```cpp
/*!
 * \brief Brief description of the function
 * \param parameter Description of parameter
 * \return Description of return value
 */
bool functionName(const QString &parameter);
```

## Qt5.14.2 Specific Features

### Rule 19: Available Qt Modules
Commonly used modules in Qt5.14.2:
- QtCore: Core functionality
- QtGui: GUI functionality
- QtWidgets: Widget classes
- QtNetwork: Network programming
- QtSql: Database integration
- QtXml: XML processing
- QtMultimedia: Multimedia support
- QtWebEngine: Web content (if available)

### Rule 20: Qt5.14.2 Deprecation Awareness
Avoid deprecated features:
- Use QStringLiteral instead of QLatin1String where appropriate
- Use nullptr instead of NULL
- Use override keyword for virtual functions
- Be aware of deprecated signal-slot syntax

## Error Handling and Best Practices

### Rule 21: Error Handling
```cpp
// Check for null pointers
if (!widget) {
    qWarning() << "Widget is null";
    return;
}

// Use Q_ASSERT for development
Q_ASSERT(model != nullptr);

// Handle file operations
QFile file("data.txt");
if (!file.open(QIODevice::ReadOnly)) {
    qCritical() << "Cannot open file:" << file.errorString();
    return;
}
```

### Rule 22: Performance Considerations
- Use const references for function parameters
- Reserve space for containers when size is known
- Use QStringBuilder for complex string operations
- Minimize signal-slot connections in loops

### Rule 23: Platform-Specific Code
```cpp
#ifdef Q_OS_WIN
    // Windows-specific code
#elif defined(Q_OS_MAC)
    // macOS-specific code
#elif defined(Q_OS_LINUX)
    // Linux-specific code
#endif
```

## Testing and Debugging

### Rule 24: Unit Testing
Use Qt Test framework:
```cpp
#include <QtTest/QtTest>

class TestClass : public QObject
{
    Q_OBJECT

private slots:
    void testFunction();
};

void TestClass::testFunction()
{
    QCOMPARE(actualValue, expectedValue);
    QVERIFY(condition);
}

QTEST_MAIN(TestClass)
#include "test.moc"
```

### Rule 25: Debug Output
```cpp
// Use appropriate debug levels
qDebug() << "Debug info:" << variable;
qInfo() << "Information:" << status;
qWarning() << "Warning:" << issue;
qCritical() << "Critical error:" << error;
```

## Quick Reference Commands

### Build Commands
```bash
# Generate Makefile
qmake CONFIG+=debug
qmake CONFIG+=release

# Build project
make
# or
nmake (on Windows with MSVC)

# Clean build
make clean
qmake
make
```

### Debugging Commands
```bash
# Run with debug output
export QT_LOGGING_RULES="*.debug=true"
./application

# Memory debugging (Linux)
valgrind --tool=memcheck --leak-check=full ./application
```

---

**规则创建时间：** 2025年06月24日 10:09:39
**最后更新：** 2025年06月24日 10:09:39
**适用版本：** Qt5.14.2 with MSVC2017 x64
**目标平台：** Windows 11