#ifndef IFILEPARSER_H
#define IFILEPARSER_H

#include <QString>
#include <QVariant>
#include <QIODevice>

/**
 * @brief 文件解析器接口
 * 
 * 所有文件解析器都应该实现此接口，以提供统一的文件解析功能。
 */
class IFileParser
{
public:
    /**
     * @brief 解析结果结构
     */
    struct ParseResult {
        bool success;           // 解析是否成功
        QString errorMessage;   // 错误消息
        QVariant data;          // 解析后的数据
        int lineNumber;         // 错误行号（如果有）
        int columnNumber;       // 错误列号（如果有）
        
        ParseResult() : success(false), lineNumber(-1), columnNumber(-1) {}
    };

    /**
     * @brief 虚析构函数
     */
    virtual ~IFileParser() = default;

    /**
     * @brief 解析文件
     * @param filePath 文件路径
     * @return 解析结果
     */
    virtual ParseResult parseFile(const QString& filePath) = 0;

    /**
     * @brief 解析数据流
     * @param device 数据流设备
     * @return 解析结果
     */
    virtual ParseResult parseStream(QIODevice* device) = 0;

    /**
     * @brief 解析字符串
     * @param content 字符串内容
     * @return 解析结果
     */
    virtual ParseResult parseString(const QString& content) = 0;

    /**
     * @brief 获取支持的文件扩展名
     * @return 文件扩展名列表
     */
    virtual QStringList supportedExtensions() const = 0;

    /**
     * @brief 获取解析器名称
     * @return 解析器名称
     */
    virtual QString parserName() const = 0;

    /**
     * @brief 获取解析器版本
     * @return 解析器版本
     */
    virtual QString parserVersion() const = 0;

    /**
     * @brief 检查是否支持指定文件
     * @param filePath 文件路径
     * @return 是否支持
     */
    virtual bool canParse(const QString& filePath) const;

    /**
     * @brief 验证文件格式
     * @param filePath 文件路径
     * @return 验证结果
     */
    virtual ParseResult validateFile(const QString& filePath);

    /**
     * @brief 获取解析器描述
     * @return 解析器描述
     */
    virtual QString parserDescription() const { return QString(); }
};

#endif // IFILEPARSER_H
