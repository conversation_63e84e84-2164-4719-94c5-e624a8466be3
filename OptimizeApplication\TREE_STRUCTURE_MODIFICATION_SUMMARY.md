# 树结构修改总结

## 概述

本次修改将原来的单一Project节点结构改为三个独立的分析类型节点，每个节点都包含自己的输入和输出配置，并对应Dakota的不同method节点。

## 修改内容

### 1. 节点类型扩展

**Common.h 更新：**
- 添加了 `NodeType::Sensitivity` 和 `NodeType::UQ` 枚举值

**新的树结构：**
```
├── Optimization
│   ├── Input
│   └── Output
├── Sensitivity Analysis  
│   ├── Input
│   └── Output
├── Uncertainty Quantification
│   ├── Input
│   └── Output
└── Solver
    ├── Pipes (n)
    ├── Junctions (n)
    ├── Volumes (n)
    └── Branches (n)
```

### 2. CustomTreeWidget 修改

**主要变更：**
- 移除了原来的Project根节点
- 创建三个顶级分析节点：Optimization、Sensitivity Analysis、UQ
- 每个分析节点下都有独立的Input和Output子节点
- 更新了节点类型处理逻辑
- 添加了新节点类型的图标和翻译支持

**关键方法更新：**
- `setupTreeStructure()`: 重新设计树形结构
- `nodeTypeToString()` / `stringToNodeType()`: 支持新节点类型
- `getNodeIcon()`: 为新节点类型添加图标
- 上下文菜单和删除逻辑更新

### 3. 新增参数配置Widget

#### SensitivityWidget
**功能：** 敏感性分析参数配置
**Dakota方法支持：**
- `sampling`: Monte Carlo和Latin Hypercube采样
- `local_reliability`: 基于梯度的FORM/SORM方法
- `polynomial_chaos`: 多项式混沌展开
- `stoch_collocation`: 随机配置方法

**主要参数：**
- 采样大小和类型
- 方差分解选项
- 收敛容差
- 输出选项（相关矩阵、敏感性指数等）

#### UQWidget  
**功能：** 不确定性量化参数配置
**Dakota方法支持：**
- `sampling`: Monte Carlo采样
- `polynomial_chaos`: 多项式混沌展开
- `stoch_collocation`: 随机配置
- `local_reliability` / `global_reliability`: 可靠性分析
- `importance_sampling`: 重要性采样

**主要参数：**
- 采样参数
- 多项式展开设置（阶数、类型）
- 自适应细化
- 输出统计选项

### 4. ParamWidget 集成

**更新内容：**
- 添加了SensitivityWidget和UQWidget的实例
- 新增 `showSensitivityParams()` 和 `showUQParams()` 方法
- 更新了信号连接，支持新Widget的参数变化通知
- 添加了获取新Widget引用的getter方法

### 5. MainWindow 事件处理

**更新内容：**
- `onTreeItemChanged()`: 添加对Sensitivity和UQ节点的处理
- `onTreeItemDoubleClicked()`: 为新节点类型添加详细信息显示
- 更新了节点选择逻辑，支持新的分析类型

### 6. 构建系统更新

**OptimizeApplication.pro 更新：**
- 添加了SensitivityWidget.cpp/.h到SOURCES和HEADERS
- 添加了UQWidget.cpp/.h到SOURCES和HEADERS  
- 添加了对应的.ui文件到FORMS

## Dakota Method 对应关系

### 优化 (Optimization)
```dakota
method
  # 优化算法
  genetic_algorithm
  # 或其他优化方法
```

### 敏感性分析 (Sensitivity Analysis)
```dakota
method
  sampling
    sample_type lhs
    samples 1000
    variance_based_decomp
  # 或
  local_reliability
  # 或  
  polynomial_chaos
```

### 不确定性量化 (UQ)
```dakota
method
  sampling
    sample_type random
    samples 10000
  # 或
  polynomial_chaos
    expansion_order 3
    expansion_type total_order
  # 或
  stoch_collocation
```

## 使用方式

1. **选择分析类型：** 在树结构中选择Optimization、Sensitivity Analysis或UQ节点
2. **配置参数：** 在右侧参数面板中配置对应的Dakota方法参数
3. **设置输入输出：** 在各分析类型下的Input/Output节点中配置具体参数
4. **运行分析：** 系统将根据选择的分析类型生成对应的Dakota输入文件

## 技术特点

- **模块化设计：** 每种分析类型都有独立的参数配置界面
- **Dakota兼容：** 参数设置直接对应Dakota的method配置
- **动态界面：** 根据选择的方法动态显示/隐藏相关参数
- **参数验证：** 实时验证参数的有效性和一致性
- **扩展性：** 易于添加新的分析方法和参数选项

## 后续扩展

1. **添加更多Dakota方法：** 可以继续添加其他Dakota支持的方法
2. **参数模板：** 为常用配置提供预设模板
3. **结果可视化：** 为不同分析类型提供专门的结果展示
4. **配置导入导出：** 支持保存和加载分析配置
5. **批量分析：** 支持同时运行多种分析类型 