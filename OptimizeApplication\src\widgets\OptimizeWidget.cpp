#include "OptimizeWidget.h"
#include "ui_OptimizeWidget.h"

OptimizeWidget::OptimizeWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::OptimizeWidget)
{
    ui->setupUi(this);
    setupConnections();
    updateMethodDescription();
    updateParameterVisibility();
}

OptimizeWidget::~OptimizeWidget()
{
    delete ui;
}

void OptimizeWidget::setupConnections()
{
    // Method selection
    connect(ui->methodCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &OptimizeWidget::onMethodChanged);
    
    // Convergence parameters
    connect(ui->maxIterationsSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->maxFunctionEvalsSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->convergenceToleranceSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &OptimizeWidget::onParameterChanged);
    
    // SOGA parameters
    connect(ui->populationSizeSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->mutationRateSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->crossoverRateSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->replacementTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &OptimizeWidget::onParameterChanged);
    
    // Pareto Set parameters
    connect(ui->multiObjectiveWeightSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->randomWeightSetsCheck, &QCheckBox::toggled,
            this, &OptimizeWidget::onParameterChanged);
    
    // CONMIN parameters
    connect(ui->gradientToleranceSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->maxStepSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->speculativeGradientCheck, &QCheckBox::toggled,
            this, &OptimizeWidget::onParameterChanged);
    
    // Output settings
    connect(ui->verboseOutputCheck, &QCheckBox::toggled,
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->debugOutputCheck, &QCheckBox::toggled,
            this, &OptimizeWidget::onParameterChanged);
    connect(ui->quietOutputCheck, &QCheckBox::toggled,
            this, &OptimizeWidget::onParameterChanged);
}

// Getter methods for Dakota parameters
QString OptimizeWidget::getDakotaMethod() const
{
    return ui->methodCombo->currentText();
}

int OptimizeWidget::getMaxIterations() const
{
    return ui->maxIterationsSpin->value();
}

int OptimizeWidget::getMaxFunctionEvaluations() const
{
    return ui->maxFunctionEvalsSpin->value();
}

double OptimizeWidget::getConvergenceTolerance() const
{
    return ui->convergenceToleranceSpin->value();
}

// SOGA parameters
int OptimizeWidget::getPopulationSize() const
{
    return ui->populationSizeSpin->value();
}

double OptimizeWidget::getMutationRate() const
{
    return ui->mutationRateSpin->value();
}

double OptimizeWidget::getCrossoverRate() const
{
    return ui->crossoverRateSpin->value();
}

QString OptimizeWidget::getReplacementType() const
{
    return ui->replacementTypeCombo->currentText();
}

// Pareto Set parameters
int OptimizeWidget::getMultiObjectiveWeightSets() const
{
    return ui->multiObjectiveWeightSpin->value();
}

bool OptimizeWidget::isRandomWeightSetsEnabled() const
{
    return ui->randomWeightSetsCheck->isChecked();
}

// CONMIN parameters
double OptimizeWidget::getGradientTolerance() const
{
    return ui->gradientToleranceSpin->value();
}

double OptimizeWidget::getMaxStepSize() const
{
    return ui->maxStepSpin->value();
}

bool OptimizeWidget::isSpeculativeGradientEnabled() const
{
    return ui->speculativeGradientCheck->isChecked();
}

// Output settings
bool OptimizeWidget::isVerboseOutputEnabled() const
{
    return ui->verboseOutputCheck->isChecked();
}

bool OptimizeWidget::isDebugOutputEnabled() const
{
    return ui->debugOutputCheck->isChecked();
}

bool OptimizeWidget::isQuietModeEnabled() const
{
    return ui->quietOutputCheck->isChecked();
}

// Setter methods
void OptimizeWidget::setDakotaMethod(const QString &method)
{
    int index = ui->methodCombo->findText(method);
    if (index >= 0) {
        ui->methodCombo->setCurrentIndex(index);
    }
}

void OptimizeWidget::setMaxIterations(int iterations)
{
    ui->maxIterationsSpin->setValue(iterations);
}

void OptimizeWidget::setMaxFunctionEvaluations(int evaluations)
{
    ui->maxFunctionEvalsSpin->setValue(evaluations);
}

void OptimizeWidget::setConvergenceTolerance(double tolerance)
{
    ui->convergenceToleranceSpin->setValue(tolerance);
}

void OptimizeWidget::setPopulationSize(int size)
{
    ui->populationSizeSpin->setValue(size);
}

void OptimizeWidget::setMutationRate(double rate)
{
    ui->mutationRateSpin->setValue(rate);
}

void OptimizeWidget::setCrossoverRate(double rate)
{
    ui->crossoverRateSpin->setValue(rate);
}

void OptimizeWidget::setReplacementType(const QString &type)
{
    int index = ui->replacementTypeCombo->findText(type);
    if (index >= 0) {
        ui->replacementTypeCombo->setCurrentIndex(index);
    }
}

void OptimizeWidget::setMultiObjectiveWeightSets(int sets)
{
    ui->multiObjectiveWeightSpin->setValue(sets);
}

void OptimizeWidget::setRandomWeightSetsEnabled(bool enabled)
{
    ui->randomWeightSetsCheck->setChecked(enabled);
}

void OptimizeWidget::setGradientTolerance(double tolerance)
{
    ui->gradientToleranceSpin->setValue(tolerance);
}

void OptimizeWidget::setMaxStepSize(double stepSize)
{
    ui->maxStepSpin->setValue(stepSize);
}

void OptimizeWidget::setSpeculativeGradientEnabled(bool enabled)
{
    ui->speculativeGradientCheck->setChecked(enabled);
}

void OptimizeWidget::setVerboseOutputEnabled(bool enabled)
{
    ui->verboseOutputCheck->setChecked(enabled);
}

void OptimizeWidget::setDebugOutputEnabled(bool enabled)
{
    ui->debugOutputCheck->setChecked(enabled);
}

void OptimizeWidget::setQuietModeEnabled(bool enabled)
{
    ui->quietOutputCheck->setChecked(enabled);
}

// Slots
void OptimizeWidget::onMethodChanged()
{
    updateMethodDescription();
    updateParameterVisibility();
    emit methodChanged(getDakotaMethod());
    emit parametersChanged();
}

void OptimizeWidget::onParameterChanged()
{
    emit parametersChanged();
}



void OptimizeWidget::updateMethodDescription()
{
    QString method = getDakotaMethod();
    QString description;
    
    if (method == "pareto_set") {
        description = "Pareto Set: Multi-objective optimization using Pareto frontier analysis";
    } else if (method == "conmin_frcg") {
        description = "CONMIN FRCG: Fletcher-Reeves conjugate gradient method";
    } else if (method == "conmin_mfd") {
        description = "CONMIN MFD: Method of feasible directions";
    } else if (method == "soga") {
        description = "SOGA: Single-Objective Genetic Algorithm";
    }
    
    ui->methodDescription->setText(description);
}

void OptimizeWidget::updateParameterVisibility()
{
    QString method = getDakotaMethod();
    
    // Show/hide parameter groups based on selected method
    ui->sogaGroup->setVisible(method == "soga");
    ui->paretoGroup->setVisible(method == "pareto_set");
    ui->conminGroup->setVisible(method == "conmin_frcg" || method == "conmin_mfd");
    }

 