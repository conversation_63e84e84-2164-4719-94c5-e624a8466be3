#include "Utils.h"
#include <QRandomGenerator>
#include <QLocale>

QString Utils::generateTimestampedFilename(const QString &baseName, const QString &extension)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    return QString("%1_%2.%3").arg(baseName, timestamp, extension);
}

bool Utils::ensureDirectoryExists(const QString &dirPath)
{
    QDir dir(dirPath);
    if (!dir.exists()) {
        return dir.mkpath(".");
    }
    return true;
}

bool Utils::fileExists(const QString &filePath)
{
    return QFileInfo::exists(filePath) && QFileInfo(filePath).isFile();
}

QString Utils::getHumanReadableFileSize(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        return "0 B";
    }
    
    qint64 bytes = fileInfo.size();
    
    QLocale locale;
    
    if (bytes < 1024) {
        return locale.toString(bytes) + " B";
    } else if (bytes < 1024 * 1024) {
        return locale.toString(bytes / 1024.0, 'f', 1) + " KB";
    } else if (bytes < 1024 * 1024 * 1024) {
        return locale.toString(bytes / (1024.0 * 1024.0), 'f', 1) + " MB";
    } else {
        return locale.toString(bytes / (1024.0 * 1024.0 * 1024.0), 'f', 1) + " GB";
    }
}

QStringList Utils::splitIntoLines(const QString &text)
{
    return text.split(QRegExp("[\r\n]"), QString::SkipEmptyParts);
}

QString Utils::formatDateTime(const QDateTime &dateTime, const QString &format)
{
    return dateTime.toString(format);
}

QString Utils::generateRandomString(int length)
{
    const QString possibleCharacters("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789");
    
    QString randomString;
    for (int i = 0; i < length; ++i) {
        int index = QRandomGenerator::global()->bounded(possibleCharacters.length());
        randomString.append(possibleCharacters.at(index));
    }
    
    return randomString;
} 