#ifndef RIBBONOPTIMIZEWIDGET_H
#define RIBBONOPTIMIZEWIDGET_H

#include <QWidget>
#include <QActionGroup>
#include "../SARibbonBar/SARibbonBar.h"
#include "../SARibbonBar/SARibbonCategory.h"
#include "../SARibbonBar/SARibbonPannel.h"
#include "../SARibbonBar/SARibbonToolButton.h"
#include "../SARibbonBar/SARibbonGallery.h"
#include "../SARibbonBar/SARibbonGalleryGroup.h"
#include "../SARibbonBar/SARibbonComboBox.h"
#include "../SARibbonBar/SARibbonLineEdit.h"
#include "../SARibbonBar/SARibbonCheckBox.h"

/**
 * @brief The RibbonOptimizeWidget class provides ribbon-based interface for optimization tasks
 */
class RibbonOptimizeWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent widget
     */
    explicit RibbonOptimizeWidget(QWidget *parent = nullptr);
    
    /**
     * @brief Destructor
     */
    ~RibbonOptimizeWidget();
    
    /**
     * @brief Create an optimization category in the ribbon
     * @param ribbon The ribbon where the category will be added
     * @return The created category
     */
    SARibbonCategory* createRibbonCategory(SARibbonBar* ribbon);

private Q_SLOTS:
    /**
     * @brief Handle optimization method selection
     * @param action The selected action
     */
    void onOptimizationMethodSelected(QAction* action);
    
    /**
     * @brief Handle sensitivity method selection
     * @param action The selected action
     */
    void onSensitivityMethodSelected(QAction* action);
    
    /**
     * @brief Handle uncertainty method selection
     * @param action The selected action
     */
    void onUncertaintyMethodSelected(QAction* action);
    
    /**
     * @brief Run the optimization
     */
    void runOptimization();
    
    /**
     * @brief Stop the optimization
     */
    void stopOptimization();
    
    /**
     * @brief Reset the optimization
     */
    void resetOptimization();

private:
    /**
     * @brief Create the algorithm panel
     * @param category The parent category
     */
    void createAlgorithmPanel(SARibbonCategory* category);
    
    /**
     * @brief Create the parameters panel
     * @param category The parent category
     */
    void createParametersPanel(SARibbonCategory* category);
    
    /**
     * @brief Create the constraints panel
     * @param category The parent category
     */
    void createConstraintsPanel(SARibbonCategory* category);
    
    /**
     * @brief Create the run panel
     * @param category The parent category
     */
    void createRunPanel(SARibbonCategory* category);
    
    /**
     * @brief Create actions for optimization methods
     */
    void createOptimizationMethodActions();
    
    /**
     * @brief Create actions for sensitivity methods
     */
    void createSensitivityMethodActions();
    
    /**
     * @brief Create actions for uncertainty methods
     */
    void createUncertaintyMethodActions();

private:
    // Action groups for different methods
    QActionGroup* optimizationMethodGroup;
    QActionGroup* sensitivityMethodGroup;
    QActionGroup* uncertaintyMethodGroup;
    
    // Actions for optimization methods
    QAction* gradientDescentAction;
    QAction* newtonMethodAction;
    QAction* quasiNewtonMethodAction;
    QAction* geneticAlgorithmAction;
    QAction* particleSwarmAction;
    
    // Actions for sensitivity methods
    QAction* localSensitivityAction;
    QAction* globalSensitivityAction;
    QAction* sobolIndicesAction;
    QAction* morrisMethodAction;
    
    // Actions for uncertainty methods
    QAction* monteCarloAction;
    QAction* latinHypercubeAction;
    QAction* quasiMonteCarloAction;
    QAction* polynomialChaosAction;
    
    // Run actions
    QAction* runAction;
    QAction* stopAction;
    QAction* resetAction;
};

#endif // RIBBONOPTIMIZEWIDGET_H 