#include "ThemeManager.h"
#include <QApplication>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QStandardPaths>

ThemeManager::ThemeManager()
    : QObject(nullptr)
    , m_currentTheme("light")
    , m_currentThemeType(ThemeType::Light)
{
}

ThemeManager::~ThemeManager()
{
}

bool ThemeManager::initialize()
{
    loadBuiltinThemes();
    loadCustomThemes();
    
    // 设置默认主题
    setTheme("light");
    
    return true;
}

bool ThemeManager::setTheme(const QString& themeName)
{
    if (!m_themeStyleSheets.contains(themeName)) {
        return false;
    }

    m_currentTheme = themeName;
    
    // 根据主题名称确定主题类型
    if (themeName == "light") {
        m_currentThemeType = ThemeType::Light;
    } else if (themeName == "dark") {
        m_currentThemeType = ThemeType::Dark;
    } else if (themeName == "system") {
        m_currentThemeType = ThemeType::System;
    } else {
        m_currentThemeType = ThemeType::Custom;
    }

    applyTheme();
    emit themeChanged(themeName);
    
    return true;
}

bool ThemeManager::setThemeType(ThemeType type)
{
    QString themeName;
    switch (type) {
    case ThemeType::Light:
        themeName = "light";
        break;
    case ThemeType::Dark:
        themeName = "dark";
        break;
    case ThemeType::System:
        themeName = "system";
        break;
    default:
        return false;
    }

    return setTheme(themeName);
}

QString ThemeManager::currentTheme() const
{
    return m_currentTheme;
}

ThemeType ThemeManager::currentThemeType() const
{
    return m_currentThemeType;
}

QStringList ThemeManager::availableThemes() const
{
    return m_themeStyleSheets.keys();
}

bool ThemeManager::loadTheme(const QString& themeName)
{
    QString themePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/themes/" + themeName + ".qss";
    QFile themeFile(themePath);
    
    if (!themeFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream in(&themeFile);
    QString styleSheet = in.readAll();
    m_themeStyleSheets[themeName] = styleSheet;
    
    return true;
}

bool ThemeManager::saveTheme(const QString& themeName)
{
    if (!m_themeStyleSheets.contains(themeName)) {
        return false;
    }

    QString themeDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/themes";
    QDir().mkpath(themeDir);
    
    QString themePath = themeDir + "/" + themeName + ".qss";
    QFile themeFile(themePath);
    
    if (!themeFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream out(&themeFile);
    out << m_themeStyleSheets[themeName];
    
    return true;
}

bool ThemeManager::createCustomTheme(const QString& themeName, const QString& baseTheme)
{
    if (!m_themeStyleSheets.contains(baseTheme)) {
        return false;
    }

    m_themeStyleSheets[themeName] = m_themeStyleSheets[baseTheme];
    return saveTheme(themeName);
}

bool ThemeManager::deleteCustomTheme(const QString& themeName)
{
    if (themeName == "light" || themeName == "dark" || themeName == "system") {
        return false; // 不能删除内置主题
    }

    m_themeStyleSheets.remove(themeName);
    
    QString themePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/themes/" + themeName + ".qss";
    return QFile::remove(themePath);
}

void ThemeManager::setThemeColor(QPalette::ColorRole role, const QColor& color)
{
    m_currentPalette.setColor(role, color);
}

QColor ThemeManager::themeColor(QPalette::ColorRole role) const
{
    return m_currentPalette.color(role);
}

void ThemeManager::applyTheme()
{
    switch (m_currentThemeType) {
    case ThemeType::Light:
        applyLightTheme();
        break;
    case ThemeType::Dark:
        applyDarkTheme();
        break;
    case ThemeType::System:
        applySystemTheme();
        break;
    case ThemeType::Custom:
        applyCustomTheme(m_currentTheme);
        break;
    }
}

void ThemeManager::loadBuiltinThemes()
{
    // 亮色主题
    m_themeStyleSheets["light"] = R"(
        QWidget {
            background-color: #ffffff;
            color: #000000;
        }
        QMenuBar {
            background-color: #f0f0f0;
            border-bottom: 1px solid #d0d0d0;
        }
        QToolBar {
            background-color: #f8f8f8;
            border: 1px solid #d0d0d0;
        }
    )";

    // 暗色主题
    m_themeStyleSheets["dark"] = R"(
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QMenuBar {
            background-color: #3c3c3c;
            border-bottom: 1px solid #555555;
        }
        QToolBar {
            background-color: #404040;
            border: 1px solid #555555;
        }
    )";

    // 系统主题
    m_themeStyleSheets["system"] = "";
}

void ThemeManager::loadCustomThemes()
{
    QString themeDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/themes";
    QDir dir(themeDir);
    
    if (!dir.exists()) {
        return;
    }

    QStringList themeFiles = dir.entryList(QStringList() << "*.qss", QDir::Files);
    for (const QString& fileName : themeFiles) {
        QString themeName = fileName.left(fileName.lastIndexOf('.'));
        loadTheme(themeName);
    }
}

void ThemeManager::applyLightTheme()
{
    QPalette lightPalette;
    lightPalette.setColor(QPalette::Window, QColor(255, 255, 255));
    lightPalette.setColor(QPalette::WindowText, QColor(0, 0, 0));
    lightPalette.setColor(QPalette::Base, QColor(255, 255, 255));
    lightPalette.setColor(QPalette::AlternateBase, QColor(245, 245, 245));
    lightPalette.setColor(QPalette::Text, QColor(0, 0, 0));
    lightPalette.setColor(QPalette::Button, QColor(240, 240, 240));
    lightPalette.setColor(QPalette::ButtonText, QColor(0, 0, 0));
    
    m_currentPalette = lightPalette;
    qApp->setPalette(lightPalette);
    qApp->setStyleSheet(getThemeStyleSheet("light"));
}

void ThemeManager::applyDarkTheme()
{
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(43, 43, 43));
    darkPalette.setColor(QPalette::WindowText, QColor(255, 255, 255));
    darkPalette.setColor(QPalette::Base, QColor(35, 35, 35));
    darkPalette.setColor(QPalette::AlternateBase, QColor(60, 60, 60));
    darkPalette.setColor(QPalette::Text, QColor(255, 255, 255));
    darkPalette.setColor(QPalette::Button, QColor(64, 64, 64));
    darkPalette.setColor(QPalette::ButtonText, QColor(255, 255, 255));
    
    m_currentPalette = darkPalette;
    qApp->setPalette(darkPalette);
    qApp->setStyleSheet(getThemeStyleSheet("dark"));
}

void ThemeManager::applySystemTheme()
{
    m_currentPalette = qApp->style()->standardPalette();
    qApp->setPalette(m_currentPalette);
    qApp->setStyleSheet("");
}

void ThemeManager::applyCustomTheme(const QString& themeName)
{
    qApp->setStyleSheet(getThemeStyleSheet(themeName));
}

QString ThemeManager::getThemeStyleSheet(const QString& themeName) const
{
    return m_themeStyleSheets.value(themeName, "");
}
