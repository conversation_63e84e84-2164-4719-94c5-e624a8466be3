#include "ApplicationCore.h"
#include "../config/ConfigManager.h"
#include "../theme/ThemeManager.h"
#include "../../utils/logging/LogManager.h"
#include "EventBus.h"
#include "PluginManager.h"
#include "ServiceLocator.h"

#include <QApplication>
#include <QDir>
#include <QStandardPaths>

ApplicationCore* ApplicationCore::s_instance = nullptr;

ApplicationCore::ApplicationCore()
    : QObject(nullptr)
    , m_initialized(false)
    , m_version("1.0.0")
    , m_applicationName("OptimizeApplication")
{
}

ApplicationCore::~ApplicationCore()
{
    cleanup();
}

ApplicationCore* ApplicationCore::instance()
{
    if (!s_instance) {
        s_instance = new ApplicationCore();
    }
    return s_instance;
}

bool ApplicationCore::initialize()
{
    if (m_initialized) {
        return true;
    }

    try {
        // 初始化服务定位器
        m_serviceLocator = std::make_unique<ServiceLocator>();

        // 初始化日志管理器
        m_logManager = std::make_unique<LogManager>();
        if (!m_logManager->initialize()) {
            return false;
        }
        m_serviceLocator->registerService<LogManager>(m_logManager.get());

        // 初始化配置管理器
        QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
        QDir().mkpath(configPath);
        m_configManager = std::make_unique<ConfigManager>(configPath + "/config.ini");
        if (!m_configManager->load()) {
            m_logManager->logWarning("Failed to load configuration, using defaults");
        }
        m_serviceLocator->registerService<ConfigManager>(m_configManager.get());

        // 初始化主题管理器
        m_themeManager = std::make_unique<ThemeManager>();
        m_themeManager->initialize();
        m_serviceLocator->registerService<ThemeManager>(m_themeManager.get());

        // 初始化事件总线
        m_eventBus = std::make_unique<EventBus>();
        m_serviceLocator->registerService<EventBus>(m_eventBus.get());

        // 初始化插件管理器
        m_pluginManager = std::make_unique<PluginManager>();
        m_pluginManager->initialize();
        m_serviceLocator->registerService<PluginManager>(m_pluginManager.get());

        // 连接信号槽
        connect(qApp, &QApplication::aboutToQuit, this, &ApplicationCore::aboutToQuit);

        m_initialized = true;
        emit initialized();

        m_logManager->logInfo("ApplicationCore initialized successfully");
        return true;

    } catch (const std::exception& e) {
        if (m_logManager) {
            m_logManager->logError(QString("Failed to initialize ApplicationCore: %1").arg(e.what()));
        }
        return false;
    }
}

void ApplicationCore::cleanup()
{
    if (!m_initialized) {
        return;
    }

    if (m_logManager) {
        m_logManager->logInfo("ApplicationCore cleanup started");
    }

    // 清理插件管理器
    if (m_pluginManager) {
        m_pluginManager->cleanup();
        m_pluginManager.reset();
    }

    // 清理事件总线
    if (m_eventBus) {
        m_eventBus.reset();
    }

    // 保存配置
    if (m_configManager) {
        m_configManager->save();
        m_configManager.reset();
    }

    // 清理主题管理器
    if (m_themeManager) {
        m_themeManager.reset();
    }

    // 清理服务定位器
    if (m_serviceLocator) {
        m_serviceLocator->cleanup();
        m_serviceLocator.reset();
    }

    // 最后清理日志管理器
    if (m_logManager) {
        m_logManager->logInfo("ApplicationCore cleanup completed");
        m_logManager.reset();
    }

    m_initialized = false;
}

ConfigManager* ApplicationCore::configManager() const
{
    return m_configManager.get();
}

ThemeManager* ApplicationCore::themeManager() const
{
    return m_themeManager.get();
}

LogManager* ApplicationCore::logManager() const
{
    return m_logManager.get();
}

EventBus* ApplicationCore::eventBus() const
{
    return m_eventBus.get();
}

PluginManager* ApplicationCore::pluginManager() const
{
    return m_pluginManager.get();
}

bool ApplicationCore::isInitialized() const
{
    return m_initialized;
}

QString ApplicationCore::version() const
{
    return m_version;
}

QString ApplicationCore::applicationName() const
{
    return m_applicationName;
}
