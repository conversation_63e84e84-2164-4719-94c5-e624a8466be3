#include "ThemeManager.h"
#include "../utils/Logger.h"
#include <QApplication>
#include <QPalette>
#include <QStyle>
#include <QFile>
#include <QTextStream>

// Initialize static instance
ThemeManager* ThemeManager::_instance = nullptr;

ThemeManager* ThemeManager::instance()
{
    if (!_instance) {
        _instance = new ThemeManager();
    }
    return _instance;
}

ThemeManager::ThemeManager()
    : currentTheme(ThemeType::Light)
{
    LOG_INFO("Initializing ThemeManager", "ThemeManager");
    
    // Load the default light theme
    loadLightTheme();
}

ThemeManager::~ThemeManager()
{
    LOG_INFO("Destroying ThemeManager", "ThemeManager");
    
    if (this == _instance) {
        _instance = nullptr;
    }
}

void ThemeManager::setTheme(ThemeType theme)
{
    if (currentTheme == theme) {
        return;
    }
    
    LOG_INFO(QString("Changing theme to: %1")
             .arg(theme == ThemeType::Light ? "Light" : 
                  theme == ThemeType::Dark ? "Dark" : "System"), 
             "ThemeManager");
    
    // Update theme
    currentTheme = theme;
    
    // Load the appropriate theme
    switch (theme) {
    case ThemeType::Light:
        loadLightTheme();
        break;
    case ThemeType::Dark:
        loadDarkTheme();
        break;
    case ThemeType::System:
        loadSystemTheme();
        break;
    }
    
    // Apply the stylesheet to the application
    qApp->setStyleSheet(styleSheet);
    
    // Emit signal for theme change
    Q_EMIT themeChanged(theme);
}

ThemeType ThemeManager::getCurrentTheme() const
{
    return currentTheme;
}

QColor ThemeManager::getColor(const QString &colorName) const
{
    return colors.value(colorName, Qt::black);
}

QString ThemeManager::getStyleSheet() const
{
    return styleSheet;
}

void ThemeManager::loadLightTheme()
{
    LOG_INFO("Loading light theme", "ThemeManager");
    
    // Clear previous theme
    colors.clear();
    
    // Set light theme colors
    colors["background"] = QColor(255, 255, 255);
    colors["foreground"] = QColor(0, 0, 0);
    colors["primary"] = QColor(42, 130, 218);
    colors["secondary"] = QColor(88, 88, 88);
    colors["accent"] = QColor(255, 151, 0);
    colors["success"] = QColor(76, 175, 80);
    colors["warning"] = QColor(255, 152, 0);
    colors["error"] = QColor(244, 67, 54);
    colors["info"] = QColor(33, 150, 243);
    
    // Create stylesheet
    styleSheet = QString(
        "QWidget { "
        "    background-color: #FFFFFF; "
        "    color: #000000; "
        "} "
        "QPushButton { "
        "    background-color: #2A82DA; "
        "    color: #FFFFFF; "
        "    border: none; "
        "    padding: 5px; "
        "    border-radius: 3px; "
        "} "
        "QPushButton:hover { "
        "    background-color: #3A92EA; "
        "} "
        "QPushButton:pressed { "
        "    background-color: #1A72CA; "
        "} "
        "QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox { "
        "    border: 1px solid #CCCCCC; "
        "    border-radius: 3px; "
        "    padding: 2px; "
        "} "
        "QTabWidget::pane { "
        "    border: 1px solid #CCCCCC; "
        "} "
        "QTabBar::tab { "
        "    background-color: #F0F0F0; "
        "    border: 1px solid #CCCCCC; "
        "    padding: 5px 10px; "
        "} "
        "QTabBar::tab:selected { "
        "    background-color: #FFFFFF; "
        "    border-bottom-color: #FFFFFF; "
        "} "
        "QTableView, QTreeView, QListView { "
        "    border: 1px solid #CCCCCC; "
        "    alternate-background-color: #F5F5F5; "
        "} "
        "QHeaderView::section { "
        "    background-color: #E0E0E0; "
        "    padding: 4px; "
        "    border: 1px solid #CCCCCC; "
        "} "
    );
}

void ThemeManager::loadDarkTheme()
{
    LOG_INFO("Loading dark theme", "ThemeManager");
    
    // Clear previous theme
    colors.clear();
    
    // Set dark theme colors
    colors["background"] = QColor(45, 45, 45);
    colors["foreground"] = QColor(255, 255, 255);
    colors["primary"] = QColor(0, 150, 255);
    colors["secondary"] = QColor(180, 180, 180);
    colors["accent"] = QColor(255, 171, 0);
    colors["success"] = QColor(76, 175, 80);
    colors["warning"] = QColor(255, 152, 0);
    colors["error"] = QColor(244, 67, 54);
    colors["info"] = QColor(33, 150, 243);
    
    // Create stylesheet
    styleSheet = QString(
        "QWidget { "
        "    background-color: #2D2D2D; "
        "    color: #FFFFFF; "
        "} "
        "QPushButton { "
        "    background-color: #0096FF; "
        "    color: #FFFFFF; "
        "    border: none; "
        "    padding: 5px; "
        "    border-radius: 3px; "
        "} "
        "QPushButton:hover { "
        "    background-color: #00A6FF; "
        "} "
        "QPushButton:pressed { "
        "    background-color: #0086EF; "
        "} "
        "QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox { "
        "    background-color: #3D3D3D; "
        "    border: 1px solid #555555; "
        "    border-radius: 3px; "
        "    padding: 2px; "
        "    color: #FFFFFF; "
        "} "
        "QTabWidget::pane { "
        "    border: 1px solid #555555; "
        "} "
        "QTabBar::tab { "
        "    background-color: #3D3D3D; "
        "    border: 1px solid #555555; "
        "    padding: 5px 10px; "
        "} "
        "QTabBar::tab:selected { "
        "    background-color: #2D2D2D; "
        "    border-bottom-color: #2D2D2D; "
        "} "
        "QTableView, QTreeView, QListView { "
        "    border: 1px solid #555555; "
        "    alternate-background-color: #353535; "
        "} "
        "QHeaderView::section { "
        "    background-color: #3D3D3D; "
        "    padding: 4px; "
        "    border: 1px solid #555555; "
        "} "
    );
}

void ThemeManager::loadSystemTheme()
{
    LOG_INFO("Loading system theme", "ThemeManager");
    
    // Determine if system is using dark mode
    bool isDarkMode = false;
    
    // Get system palette
    QPalette systemPalette = QApplication::palette();
    QColor windowColor = systemPalette.color(QPalette::Window);
    
    // If window color is dark, use dark theme
    if (windowColor.red() + windowColor.green() + windowColor.blue() < 382) { // 382 = 255*3/2
        isDarkMode = true;
    }
    
    // Load appropriate theme
    if (isDarkMode) {
        loadDarkTheme();
    } else {
        loadLightTheme();
    }
} 