#include "RibbonOptimizeWidget.h"
#include "../utils/Logger.h"
#include <QAction>
#include <QIcon>

RibbonOptimizeWidget::RibbonOptimizeWidget(QWidget *parent)
    : QWidget(parent)
    , optimizationMethodGroup(new QActionGroup(this))
    , sensitivityMethodGroup(new QActionGroup(this))
    , uncertaintyMethodGroup(new QActionGroup(this))
{
    LOG_INFO("Creating RibbonOptimizeWidget", "RibbonOptimizeWidget");
    
    // Create actions for different methods
    createOptimizationMethodActions();
    createSensitivityMethodActions();
    createUncertaintyMethodActions();
    
    // Configure action groups
    optimizationMethodGroup->setExclusive(true);
    sensitivityMethodGroup->setExclusive(true);
    uncertaintyMethodGroup->setExclusive(true);
    
    // Create run actions
    runAction = new QAction(QIcon(":/icons/run.png"), tr("Run"), this);
    runAction->setToolTip(tr("Run the optimization"));
    connect(runAction, &QAction::triggered, this, &RibbonOptimizeWidget::runOptimization);
    
    stopAction = new QAction(QIcon(":/icons/stop.png"), tr("Stop"), this);
    stopAction->setToolTip(tr("Stop the optimization"));
    stopAction->setEnabled(false);
    connect(stopAction, &QAction::triggered, this, &RibbonOptimizeWidget::stopOptimization);
    
    resetAction = new QAction(QIcon(":/icons/reset.png"), tr("Reset"), this);
    resetAction->setToolTip(tr("Reset the optimization parameters"));
    connect(resetAction, &QAction::triggered, this, &RibbonOptimizeWidget::resetOptimization);
    
    LOG_INFO("RibbonOptimizeWidget created", "RibbonOptimizeWidget");
}

RibbonOptimizeWidget::~RibbonOptimizeWidget()
{
    LOG_INFO("Destroying RibbonOptimizeWidget", "RibbonOptimizeWidget");
}

SARibbonCategory* RibbonOptimizeWidget::createRibbonCategory(SARibbonBar* ribbon)
{
    LOG_INFO("Creating optimization ribbon category", "RibbonOptimizeWidget");
    
    // Create ribbon category
    SARibbonCategory* category = ribbon->addCategoryPage(tr("Optimization"));
    
    // Create panels
    createAlgorithmPanel(category);
    createParametersPanel(category);
    createConstraintsPanel(category);
    createRunPanel(category);
    
    return category;
}

void RibbonOptimizeWidget::onOptimizationMethodSelected(QAction* action)
{
    LOG_INFO(QString("Selected optimization method: %1").arg(action->text()), "RibbonOptimizeWidget");
    
    // TODO: Handle selection of optimization method
}

void RibbonOptimizeWidget::onSensitivityMethodSelected(QAction* action)
{
    LOG_INFO(QString("Selected sensitivity method: %1").arg(action->text()), "RibbonOptimizeWidget");
    
    // TODO: Handle selection of sensitivity method
}

void RibbonOptimizeWidget::onUncertaintyMethodSelected(QAction* action)
{
    LOG_INFO(QString("Selected uncertainty method: %1").arg(action->text()), "RibbonOptimizeWidget");
    
    // TODO: Handle selection of uncertainty method
}

void RibbonOptimizeWidget::runOptimization()
{
    LOG_INFO("Running optimization", "RibbonOptimizeWidget");
    
    // TODO: Implement optimization execution
    
    // Update UI
    runAction->setEnabled(false);
    stopAction->setEnabled(true);
}

void RibbonOptimizeWidget::stopOptimization()
{
    LOG_INFO("Stopping optimization", "RibbonOptimizeWidget");
    
    // TODO: Implement optimization stopping
    
    // Update UI
    runAction->setEnabled(true);
    stopAction->setEnabled(false);
}

void RibbonOptimizeWidget::resetOptimization()
{
    LOG_INFO("Resetting optimization", "RibbonOptimizeWidget");
    
    // TODO: Implement optimization reset
    
    // Update UI
    runAction->setEnabled(true);
    stopAction->setEnabled(false);
}

void RibbonOptimizeWidget::createAlgorithmPanel(SARibbonCategory* category)
{
    LOG_INFO("Creating algorithm panel", "RibbonOptimizeWidget");
    
    SARibbonPannel* panel = category->addPannel(tr("Algorithm"));
    
    // Optimization methods
    panel->addSeparator();
    panel->addSmallWidget(new QLabel(tr("Optimization:"), this));
    
    SARibbonGallery* optimizationGallery = panel->addGallery();
    SARibbonGalleryGroup* optimizationGroup = optimizationGallery->addGalleryGroup();
    // Note: SARibbonGalleryGroup doesn't have setTitle method, 
    // and we need to use addActionItem instead of addItem for actions
    optimizationGroup->setGroupTitle(tr("Optimization Methods"));
    optimizationGroup->addActionItem(gradientDescentAction);
    optimizationGroup->addActionItem(newtonMethodAction);
    optimizationGroup->addActionItem(quasiNewtonMethodAction);
    optimizationGroup->addActionItem(geneticAlgorithmAction);
    optimizationGroup->addActionItem(particleSwarmAction);
    
    // Sensitivity methods
    panel->addSeparator();
    panel->addSmallWidget(new QLabel(tr("Sensitivity:"), this));
    
    SARibbonGallery* sensitivityGallery = panel->addGallery();
    SARibbonGalleryGroup* sensitivityGroup = sensitivityGallery->addGalleryGroup();
    sensitivityGroup->setGroupTitle(tr("Sensitivity Methods"));
    sensitivityGroup->addActionItem(localSensitivityAction);
    sensitivityGroup->addActionItem(globalSensitivityAction);
    sensitivityGroup->addActionItem(sobolIndicesAction);
    sensitivityGroup->addActionItem(morrisMethodAction);
    
    // Uncertainty methods
    panel->addSeparator();
    panel->addSmallWidget(new QLabel(tr("Uncertainty:"), this));
    
    SARibbonGallery* uncertaintyGallery = panel->addGallery();
    SARibbonGalleryGroup* uncertaintyGroup = uncertaintyGallery->addGalleryGroup();
    uncertaintyGroup->setGroupTitle(tr("Uncertainty Methods"));
    uncertaintyGroup->addActionItem(monteCarloAction);
    uncertaintyGroup->addActionItem(latinHypercubeAction);
    uncertaintyGroup->addActionItem(quasiMonteCarloAction);
    uncertaintyGroup->addActionItem(polynomialChaosAction);
}

void RibbonOptimizeWidget::createParametersPanel(SARibbonCategory* category)
{
    LOG_INFO("Creating parameters panel", "RibbonOptimizeWidget");
    
    SARibbonPannel* panel = category->addPannel(tr("Parameters"));
    
    // Add common parameters
    panel->addSmallWidget(new QLabel(tr("Max Iterations:"), this));
    SARibbonLineEdit* maxIterationsEdit = new SARibbonLineEdit(this);
    maxIterationsEdit->setText("100");
    panel->addSmallWidget(maxIterationsEdit);
    
    panel->addSmallWidget(new QLabel(tr("Tolerance:"), this));
    SARibbonLineEdit* toleranceEdit = new SARibbonLineEdit(this);
    toleranceEdit->setText("0.001");
    panel->addSmallWidget(toleranceEdit);
    
    panel->addSmallWidget(new QLabel(tr("Population Size:"), this));
    SARibbonLineEdit* populationSizeEdit = new SARibbonLineEdit(this);
    populationSizeEdit->setText("50");
    panel->addSmallWidget(populationSizeEdit);
    
    // Add checkboxes for options
    panel->addSeparator();
    SARibbonCheckBox* normalizeCheck = new SARibbonCheckBox(tr("Normalize Data"), this);
    panel->addSmallWidget(normalizeCheck);
    
    SARibbonCheckBox* parallelCheck = new SARibbonCheckBox(tr("Parallel Processing"), this);
    panel->addSmallWidget(parallelCheck);
    
    SARibbonCheckBox* cacheCheck = new SARibbonCheckBox(tr("Cache Results"), this);
    panel->addSmallWidget(cacheCheck);
}

void RibbonOptimizeWidget::createConstraintsPanel(SARibbonCategory* category)
{
    LOG_INFO("Creating constraints panel", "RibbonOptimizeWidget");
    
    SARibbonPannel* panel = category->addPannel(tr("Constraints"));
    
    // Add constraint type selector
    panel->addSmallWidget(new QLabel(tr("Constraint Type:"), this));
    SARibbonComboBox* constraintTypeCombo = new SARibbonComboBox(this);
    constraintTypeCombo->addItem(tr("Equality"));
    constraintTypeCombo->addItem(tr("Inequality"));
    constraintTypeCombo->addItem(tr("Bound"));
    panel->addSmallWidget(constraintTypeCombo);
    
    // Add constraint actions
    QAction* addConstraintAction = new QAction(QIcon(":/icons/add.png"), tr("Add"), this);
    addConstraintAction->setToolTip(tr("Add a new constraint"));
    panel->addMediumAction(addConstraintAction);
    
    QAction* editConstraintAction = new QAction(QIcon(":/icons/edit.png"), tr("Edit"), this);
    editConstraintAction->setToolTip(tr("Edit selected constraint"));
    panel->addMediumAction(editConstraintAction);
    
    QAction* removeConstraintAction = new QAction(QIcon(":/icons/remove.png"), tr("Remove"), this);
    removeConstraintAction->setToolTip(tr("Remove selected constraint"));
    panel->addMediumAction(removeConstraintAction);
}

void RibbonOptimizeWidget::createRunPanel(SARibbonCategory* category)
{
    LOG_INFO("Creating run panel", "RibbonOptimizeWidget");
    
    SARibbonPannel* panel = category->addPannel(tr("Run"));
    
    // Add run, stop, and reset actions
    panel->addLargeAction(runAction);
    panel->addLargeAction(stopAction);
    panel->addLargeAction(resetAction);
}

void RibbonOptimizeWidget::createOptimizationMethodActions()
{
    LOG_INFO("Creating optimization method actions", "RibbonOptimizeWidget");
    
    gradientDescentAction = new QAction(QIcon(":/icons/gradient_descent.png"), tr("Gradient Descent"), this);
    gradientDescentAction->setCheckable(true);
    gradientDescentAction->setChecked(true); // Default selection
    gradientDescentAction->setToolTip(tr("First-order optimization algorithm"));
    optimizationMethodGroup->addAction(gradientDescentAction);
    
    newtonMethodAction = new QAction(QIcon(":/icons/newton.png"), tr("Newton Method"), this);
    newtonMethodAction->setCheckable(true);
    newtonMethodAction->setToolTip(tr("Second-order optimization algorithm"));
    optimizationMethodGroup->addAction(newtonMethodAction);
    
    quasiNewtonMethodAction = new QAction(QIcon(":/icons/quasi_newton.png"), tr("Quasi-Newton"), this);
    quasiNewtonMethodAction->setCheckable(true);
    quasiNewtonMethodAction->setToolTip(tr("BFGS and similar methods"));
    optimizationMethodGroup->addAction(quasiNewtonMethodAction);
    
    geneticAlgorithmAction = new QAction(QIcon(":/icons/genetic.png"), tr("Genetic Algorithm"), this);
    geneticAlgorithmAction->setCheckable(true);
    geneticAlgorithmAction->setToolTip(tr("Evolutionary optimization algorithm"));
    optimizationMethodGroup->addAction(geneticAlgorithmAction);
    
    particleSwarmAction = new QAction(QIcon(":/icons/particle_swarm.png"), tr("Particle Swarm"), this);
    particleSwarmAction->setCheckable(true);
    particleSwarmAction->setToolTip(tr("Swarm intelligence based optimization"));
    optimizationMethodGroup->addAction(particleSwarmAction);
    
    connect(optimizationMethodGroup, &QActionGroup::triggered, this, &RibbonOptimizeWidget::onOptimizationMethodSelected);
}

void RibbonOptimizeWidget::createSensitivityMethodActions()
{
    LOG_INFO("Creating sensitivity method actions", "RibbonOptimizeWidget");
    
    localSensitivityAction = new QAction(QIcon(":/icons/local_sensitivity.png"), tr("Local Sensitivity"), this);
    localSensitivityAction->setCheckable(true);
    localSensitivityAction->setChecked(true); // Default selection
    localSensitivityAction->setToolTip(tr("Parameter perturbation method"));
    sensitivityMethodGroup->addAction(localSensitivityAction);
    
    globalSensitivityAction = new QAction(QIcon(":/icons/global_sensitivity.png"), tr("Global Sensitivity"), this);
    globalSensitivityAction->setCheckable(true);
    globalSensitivityAction->setToolTip(tr("Analysis over the entire parameter space"));
    sensitivityMethodGroup->addAction(globalSensitivityAction);
    
    sobolIndicesAction = new QAction(QIcon(":/icons/sobol.png"), tr("Sobol Indices"), this);
    sobolIndicesAction->setCheckable(true);
    sobolIndicesAction->setToolTip(tr("Variance-based sensitivity analysis"));
    sensitivityMethodGroup->addAction(sobolIndicesAction);
    
    morrisMethodAction = new QAction(QIcon(":/icons/morris.png"), tr("Morris Method"), this);
    morrisMethodAction->setCheckable(true);
    morrisMethodAction->setToolTip(tr("Elementary effects screening method"));
    sensitivityMethodGroup->addAction(morrisMethodAction);
    
    connect(sensitivityMethodGroup, &QActionGroup::triggered, this, &RibbonOptimizeWidget::onSensitivityMethodSelected);
}

void RibbonOptimizeWidget::createUncertaintyMethodActions()
{
    LOG_INFO("Creating uncertainty method actions", "RibbonOptimizeWidget");
    
    monteCarloAction = new QAction(QIcon(":/icons/monte_carlo.png"), tr("Monte Carlo"), this);
    monteCarloAction->setCheckable(true);
    monteCarloAction->setChecked(true); // Default selection
    monteCarloAction->setToolTip(tr("Random sampling method"));
    uncertaintyMethodGroup->addAction(monteCarloAction);
    
    latinHypercubeAction = new QAction(QIcon(":/icons/latin_hypercube.png"), tr("Latin Hypercube"), this);
    latinHypercubeAction->setCheckable(true);
    latinHypercubeAction->setToolTip(tr("Stratified sampling method"));
    uncertaintyMethodGroup->addAction(latinHypercubeAction);
    
    quasiMonteCarloAction = new QAction(QIcon(":/icons/quasi_monte_carlo.png"), tr("Quasi-Monte Carlo"), this);
    quasiMonteCarloAction->setCheckable(true);
    quasiMonteCarloAction->setToolTip(tr("Low-discrepancy sequence sampling"));
    uncertaintyMethodGroup->addAction(quasiMonteCarloAction);
    
    polynomialChaosAction = new QAction(QIcon(":/icons/polynomial_chaos.png"), tr("Polynomial Chaos"), this);
    polynomialChaosAction->setCheckable(true);
    polynomialChaosAction->setToolTip(tr("Spectral method for uncertainty quantification"));
    uncertaintyMethodGroup->addAction(polynomialChaosAction);
    
    connect(uncertaintyMethodGroup, &QActionGroup::triggered, this, &RibbonOptimizeWidget::onUncertaintyMethodSelected);
} 