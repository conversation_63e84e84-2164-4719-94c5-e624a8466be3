# PowerShell脚本：将项目文件的换行符转换为Windows格式（CRLF）
# 使用方法：在项目根目录运行 .\convert_line_endings.ps1

Write-Host "开始转换项目文件的换行符为Windows格式（CRLF）..."

# 定义要处理的文件扩展名
$extensions = @("*.cpp", "*.h", "*.pro", "*.ui", "*.qrc", "*.md", "*.txt")

# 获取所有匹配的文件
$files = Get-ChildItem -Path . -Recurse -Include $extensions

$convertedCount = 0
$totalCount = $files.Count

Write-Host "找到 $totalCount 个文件需要检查..."

foreach ($file in $files) {
    try {
        # 读取文件内容
        $content = Get-Content $file.FullName -Raw -ErrorAction Stop
        
        # 检查是否包含Unix换行符但不包含Windows换行符
        if ($content -match "`n" -and $content -notmatch "`r`n") {
            # 转换换行符：先将所有LF转换为CRLF，然后处理可能的重复CR
            $content = $content -replace "`n", "`r`n"
            $content = $content -replace "`r`r`n", "`r`n"
            
            # 写回文件
            Set-Content $file.FullName -Value $content -NoNewline -ErrorAction Stop
            
            Write-Host "已转换: $($file.FullName)" -ForegroundColor Green
            $convertedCount++
        }
    }
    catch {
        Write-Host "转换失败: $($file.FullName) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n转换完成！"
Write-Host "总文件数: $totalCount"
Write-Host "已转换: $convertedCount"
Write-Host "跳过: $($totalCount - $convertedCount)"

if ($convertedCount -gt 0) {
    Write-Host "`n建议重新编译项目以确保更改生效。" -ForegroundColor Yellow
} 