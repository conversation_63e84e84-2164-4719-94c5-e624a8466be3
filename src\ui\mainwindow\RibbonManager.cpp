#include "RibbonManager.h"
#include "../../../3rdparty/SARibbonBar/SARibbonBar.h"
#include "../../../3rdparty/SARibbonBar/SARibbonCategory.h"
#include "../../../3rdparty/SARibbonBar/SARibbonPannel.h"
#include "../../../3rdparty/SARibbonBar/SARibbonContextCategory.h"
#include <QAction>

RibbonManager::RibbonManager(SARibbonBar* ribbonBar)
    : QObject(nullptr)
    , m_ribbonBar(ribbonBar)
{
}

RibbonManager::~RibbonManager()
{
}

void RibbonManager::initialize()
{
    if (!m_ribbonBar) {
        return;
    }

    // 创建标准标签页
    createHomeTab();
    createEditTab();
    createViewTab();
    createToolsTab();
    createHelpTab();
}

SARibbonCategory* RibbonManager::createHomeTab()
{
    SARibbonCategory* homeCategory = m_ribbonBar->addCategoryPage(tr("开始"));
    m_categories["home"] = homeCategory;

    // 文件面板
    SARibbonPannel* filePannel = homeCategory->addPannel(tr("文件"));
    m_pannels["home"]["file"] = filePannel;

    // 项目面板
    SARibbonPannel* projectPannel = homeCategory->addPannel(tr("项目"));
    m_pannels["home"]["project"] = projectPannel;

    return homeCategory;
}

SARibbonCategory* RibbonManager::createEditTab()
{
    SARibbonCategory* editCategory = m_ribbonBar->addCategoryPage(tr("编辑"));
    m_categories["edit"] = editCategory;

    // 剪贴板面板
    SARibbonPannel* clipboardPannel = editCategory->addPannel(tr("剪贴板"));
    m_pannels["edit"]["clipboard"] = clipboardPannel;

    // 撤销面板
    SARibbonPannel* undoPannel = editCategory->addPannel(tr("撤销"));
    m_pannels["edit"]["undo"] = undoPannel;

    return editCategory;
}

SARibbonCategory* RibbonManager::createViewTab()
{
    SARibbonCategory* viewCategory = m_ribbonBar->addCategoryPage(tr("视图"));
    m_categories["view"] = viewCategory;

    // 缩放面板
    SARibbonPannel* zoomPannel = viewCategory->addPannel(tr("缩放"));
    m_pannels["view"]["zoom"] = zoomPannel;

    // 布局面板
    SARibbonPannel* layoutPannel = viewCategory->addPannel(tr("布局"));
    m_pannels["view"]["layout"] = layoutPannel;

    return viewCategory;
}

SARibbonCategory* RibbonManager::createToolsTab()
{
    SARibbonCategory* toolsCategory = m_ribbonBar->addCategoryPage(tr("工具"));
    m_categories["tools"] = toolsCategory;

    // 图像处理面板
    SARibbonPannel* imagePannel = toolsCategory->addPannel(tr("图像处理"));
    m_pannels["tools"]["image"] = imagePannel;

    // 分析面板
    SARibbonPannel* analysisPannel = toolsCategory->addPannel(tr("分析"));
    m_pannels["tools"]["analysis"] = analysisPannel;

    return toolsCategory;
}

SARibbonCategory* RibbonManager::createHelpTab()
{
    SARibbonCategory* helpCategory = m_ribbonBar->addCategoryPage(tr("帮助"));
    m_categories["help"] = helpCategory;

    // 支持面板
    SARibbonPannel* supportPannel = helpCategory->addPannel(tr("支持"));
    m_pannels["help"]["support"] = supportPannel;

    // 设置面板
    SARibbonPannel* settingsPannel = helpCategory->addPannel(tr("设置"));
    m_pannels["help"]["settings"] = settingsPannel;

    return helpCategory;
}

SARibbonCategory* RibbonManager::addCategory(const QString& categoryName)
{
    if (m_categories.contains(categoryName)) {
        return m_categories[categoryName];
    }

    SARibbonCategory* category = m_ribbonBar->addCategoryPage(categoryName);
    m_categories[categoryName] = category;
    
    return category;
}

bool RibbonManager::removeCategory(const QString& categoryName)
{
    if (!m_categories.contains(categoryName)) {
        return false;
    }

    SARibbonCategory* category = m_categories[categoryName];
    m_ribbonBar->removeCategory(category);
    m_categories.remove(categoryName);
    m_pannels.remove(categoryName);

    return true;
}

SARibbonPannel* RibbonManager::addPannel(const QString& categoryName, const QString& pannelName)
{
    SARibbonCategory* category = getCategory(categoryName);
    if (!category) {
        return nullptr;
    }

    SARibbonPannel* pannel = category->addPannel(pannelName);
    m_pannels[categoryName][pannelName] = pannel;

    return pannel;
}

void RibbonManager::addActionToPannel(const QString& categoryName, const QString& pannelName, QAction* action)
{
    SARibbonPannel* pannel = getPannel(categoryName, pannelName);
    if (pannel && action) {
        pannel->addLargeAction(action);
    }
}

SARibbonContextCategory* RibbonManager::addContextCategory(const QString& contextName, const QColor& color)
{
    if (m_contextCategories.contains(contextName)) {
        return m_contextCategories[contextName];
    }

    SARibbonContextCategory* contextCategory = m_ribbonBar->addContextCategory(contextName, color);
    m_contextCategories[contextName] = contextCategory;

    return contextCategory;
}

void RibbonManager::setContextCategoryVisible(const QString& contextName, bool visible)
{
    SARibbonContextCategory* contextCategory = getContextCategory(contextName);
    if (contextCategory) {
        m_ribbonBar->showContextCategory(contextCategory, visible);
    }
}

SARibbonCategory* RibbonManager::getCategory(const QString& categoryName) const
{
    return m_categories.value(categoryName, nullptr);
}

SARibbonPannel* RibbonManager::getPannel(const QString& categoryName, const QString& pannelName) const
{
    if (!m_pannels.contains(categoryName)) {
        return nullptr;
    }

    return m_pannels[categoryName].value(pannelName, nullptr);
}

SARibbonContextCategory* RibbonManager::getContextCategory(const QString& contextName) const
{
    return m_contextCategories.value(contextName, nullptr);
}

void RibbonManager::setRibbonStyle(int style)
{
    if (m_ribbonBar) {
        m_ribbonBar->setRibbonStyle(static_cast<SARibbonBar::RibbonStyle>(style));
    }
}

void RibbonManager::setCompactMode(bool compact)
{
    if (m_ribbonBar) {
        // 实现紧凑模式切换逻辑
        // 这里需要根据SARibbonBar的具体API来实现
    }
}
