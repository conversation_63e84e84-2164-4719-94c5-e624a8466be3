/****************************************************************************
** Meta object code from reading C++ file 'SARibbonBar.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/SARibbonBar/SARibbonBar.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'SARibbonBar.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SARibbonBar_t {
    QByteArrayData data[60];
    char stringdata0[1029];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SARibbonBar_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SARibbonBar_t qt_meta_stringdata_SARibbonBar = {
    {
QT_MOC_LITERAL(0, 0, 11), // "SARibbonBar"
QT_MOC_LITERAL(1, 12, 24), // "applicationButtonClicked"
QT_MOC_LITERAL(2, 37, 0), // ""
QT_MOC_LITERAL(3, 38, 23), // "currentRibbonTabChanged"
QT_MOC_LITERAL(4, 62, 5), // "index"
QT_MOC_LITERAL(5, 68, 17), // "ribbonModeChanged"
QT_MOC_LITERAL(6, 86, 23), // "SARibbonBar::RibbonMode"
QT_MOC_LITERAL(7, 110, 8), // "nowState"
QT_MOC_LITERAL(8, 119, 18), // "ribbonStyleChanged"
QT_MOC_LITERAL(9, 138, 25), // "SARibbonBar::RibbonStyles"
QT_MOC_LITERAL(10, 164, 8), // "nowStyle"
QT_MOC_LITERAL(11, 173, 21), // "titleBarHeightChanged"
QT_MOC_LITERAL(12, 195, 9), // "oldHeight"
QT_MOC_LITERAL(13, 205, 9), // "newHeight"
QT_MOC_LITERAL(14, 215, 15), // "actionTriggered"
QT_MOC_LITERAL(15, 231, 8), // "QAction*"
QT_MOC_LITERAL(16, 240, 6), // "action"
QT_MOC_LITERAL(17, 247, 20), // "onWindowTitleChanged"
QT_MOC_LITERAL(18, 268, 5), // "title"
QT_MOC_LITERAL(19, 274, 19), // "onWindowIconChanged"
QT_MOC_LITERAL(20, 294, 1), // "i"
QT_MOC_LITERAL(21, 296, 28), // "onCategoryWindowTitleChanged"
QT_MOC_LITERAL(22, 325, 18), // "onStackWidgetHided"
QT_MOC_LITERAL(23, 344, 25), // "onCurrentRibbonTabChanged"
QT_MOC_LITERAL(24, 370, 25), // "onCurrentRibbonTabClicked"
QT_MOC_LITERAL(25, 396, 31), // "onCurrentRibbonTabDoubleClicked"
QT_MOC_LITERAL(26, 428, 27), // "onContextsCategoryPageAdded"
QT_MOC_LITERAL(27, 456, 17), // "SARibbonCategory*"
QT_MOC_LITERAL(28, 474, 8), // "category"
QT_MOC_LITERAL(29, 483, 37), // "onContextsCategoryCategoryNam..."
QT_MOC_LITERAL(30, 521, 10), // "onTabMoved"
QT_MOC_LITERAL(31, 532, 4), // "from"
QT_MOC_LITERAL(32, 537, 2), // "to"
QT_MOC_LITERAL(33, 540, 15), // "addCategoryPage"
QT_MOC_LITERAL(34, 556, 8), // "QWidget*"
QT_MOC_LITERAL(35, 565, 11), // "ribbonStyle"
QT_MOC_LITERAL(36, 577, 12), // "RibbonStyles"
QT_MOC_LITERAL(37, 590, 11), // "minimumMode"
QT_MOC_LITERAL(38, 602, 17), // "minimumModeButton"
QT_MOC_LITERAL(39, 620, 20), // "windowTitleTextColor"
QT_MOC_LITERAL(40, 641, 19), // "tabBarBaseLineColor"
QT_MOC_LITERAL(41, 661, 19), // "windowTitleAligment"
QT_MOC_LITERAL(42, 681, 13), // "Qt::Alignment"
QT_MOC_LITERAL(43, 695, 14), // "enableWordWrap"
QT_MOC_LITERAL(44, 710, 21), // "enableShowPannelTitle"
QT_MOC_LITERAL(45, 732, 10), // "tabOnTitle"
QT_MOC_LITERAL(46, 743, 16), // "pannelLayoutMode"
QT_MOC_LITERAL(47, 760, 32), // "SARibbonPannel::PannelLayoutMode"
QT_MOC_LITERAL(48, 793, 15), // "RibbonStyleFlag"
QT_MOC_LITERAL(49, 809, 16), // "RibbonStyleLoose"
QT_MOC_LITERAL(50, 826, 18), // "RibbonStyleCompact"
QT_MOC_LITERAL(51, 845, 19), // "RibbonStyleThreeRow"
QT_MOC_LITERAL(52, 865, 17), // "RibbonStyleTwoRow"
QT_MOC_LITERAL(53, 883, 24), // "RibbonStyleLooseThreeRow"
QT_MOC_LITERAL(54, 908, 26), // "RibbonStyleCompactThreeRow"
QT_MOC_LITERAL(55, 935, 22), // "RibbonStyleLooseTwoRow"
QT_MOC_LITERAL(56, 958, 24), // "RibbonStyleCompactTwoRow"
QT_MOC_LITERAL(57, 983, 10), // "RibbonMode"
QT_MOC_LITERAL(58, 994, 17), // "MinimumRibbonMode"
QT_MOC_LITERAL(59, 1012, 16) // "NormalRibbonMode"

    },
    "SARibbonBar\0applicationButtonClicked\0"
    "\0currentRibbonTabChanged\0index\0"
    "ribbonModeChanged\0SARibbonBar::RibbonMode\0"
    "nowState\0ribbonStyleChanged\0"
    "SARibbonBar::RibbonStyles\0nowStyle\0"
    "titleBarHeightChanged\0oldHeight\0"
    "newHeight\0actionTriggered\0QAction*\0"
    "action\0onWindowTitleChanged\0title\0"
    "onWindowIconChanged\0i\0"
    "onCategoryWindowTitleChanged\0"
    "onStackWidgetHided\0onCurrentRibbonTabChanged\0"
    "onCurrentRibbonTabClicked\0"
    "onCurrentRibbonTabDoubleClicked\0"
    "onContextsCategoryPageAdded\0"
    "SARibbonCategory*\0category\0"
    "onContextsCategoryCategoryNameChanged\0"
    "onTabMoved\0from\0to\0addCategoryPage\0"
    "QWidget*\0ribbonStyle\0RibbonStyles\0"
    "minimumMode\0minimumModeButton\0"
    "windowTitleTextColor\0tabBarBaseLineColor\0"
    "windowTitleAligment\0Qt::Alignment\0"
    "enableWordWrap\0enableShowPannelTitle\0"
    "tabOnTitle\0pannelLayoutMode\0"
    "SARibbonPannel::PannelLayoutMode\0"
    "RibbonStyleFlag\0RibbonStyleLoose\0"
    "RibbonStyleCompact\0RibbonStyleThreeRow\0"
    "RibbonStyleTwoRow\0RibbonStyleLooseThreeRow\0"
    "RibbonStyleCompactThreeRow\0"
    "RibbonStyleLooseTwoRow\0RibbonStyleCompactTwoRow\0"
    "RibbonMode\0MinimumRibbonMode\0"
    "NormalRibbonMode"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SARibbonBar[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      17,   14, // methods
      10,  152, // properties
       3,  182, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   99,    2, 0x06 /* Public */,
       3,    1,  100,    2, 0x06 /* Public */,
       5,    1,  103,    2, 0x06 /* Public */,
       8,    1,  106,    2, 0x06 /* Public */,
      11,    2,  109,    2, 0x06 /* Public */,
      14,    1,  114,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      17,    1,  117,    2, 0x09 /* Protected */,
      19,    1,  120,    2, 0x09 /* Protected */,
      21,    1,  123,    2, 0x09 /* Protected */,
      22,    0,  126,    2, 0x09 /* Protected */,
      23,    1,  127,    2, 0x09 /* Protected */,
      24,    1,  130,    2, 0x09 /* Protected */,
      25,    1,  133,    2, 0x09 /* Protected */,
      26,    1,  136,    2, 0x09 /* Protected */,
      29,    2,  139,    2, 0x09 /* Protected */,
      30,    2,  144,    2, 0x09 /* Protected */,

 // methods: name, argc, parameters, tag, flags
      33,    1,  149,    2, 0x02 /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    4,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, 0x80000000 | 9,   10,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   12,   13,
    QMetaType::Void, 0x80000000 | 15,   16,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,   18,
    QMetaType::Void, QMetaType::QIcon,   20,
    QMetaType::Void, QMetaType::QString,   18,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,    4,
    QMetaType::Void, QMetaType::Int,    4,
    QMetaType::Void, QMetaType::Int,    4,
    QMetaType::Void, 0x80000000 | 27,   28,
    QMetaType::Void, 0x80000000 | 27, QMetaType::QString,   28,   18,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   31,   32,

 // methods: parameters
    QMetaType::Void, 0x80000000 | 34,   28,

 // properties: name, type, flags
      35, 0x80000000 | 36, 0x0009510b,
      37, QMetaType::Bool, 0x00095103,
      38, QMetaType::Bool, 0x00095003,
      39, QMetaType::QColor, 0x00095103,
      40, QMetaType::QColor, 0x00095103,
      41, 0x80000000 | 42, 0x0009510b,
      43, QMetaType::Bool, 0x00095103,
      44, QMetaType::Bool, 0x00095103,
      45, QMetaType::Bool, 0x00095103,
      46, 0x80000000 | 47, 0x0009510b,

 // enums: name, alias, flags, count, data
      48,   48, 0x0,    8,  197,
      36,   48, 0x1,    8,  213,
      57,   57, 0x0,    2,  229,

 // enum data: key, value
      49, uint(SARibbonBar::RibbonStyleLoose),
      50, uint(SARibbonBar::RibbonStyleCompact),
      51, uint(SARibbonBar::RibbonStyleThreeRow),
      52, uint(SARibbonBar::RibbonStyleTwoRow),
      53, uint(SARibbonBar::RibbonStyleLooseThreeRow),
      54, uint(SARibbonBar::RibbonStyleCompactThreeRow),
      55, uint(SARibbonBar::RibbonStyleLooseTwoRow),
      56, uint(SARibbonBar::RibbonStyleCompactTwoRow),
      49, uint(SARibbonBar::RibbonStyleLoose),
      50, uint(SARibbonBar::RibbonStyleCompact),
      51, uint(SARibbonBar::RibbonStyleThreeRow),
      52, uint(SARibbonBar::RibbonStyleTwoRow),
      53, uint(SARibbonBar::RibbonStyleLooseThreeRow),
      54, uint(SARibbonBar::RibbonStyleCompactThreeRow),
      55, uint(SARibbonBar::RibbonStyleLooseTwoRow),
      56, uint(SARibbonBar::RibbonStyleCompactTwoRow),
      58, uint(SARibbonBar::MinimumRibbonMode),
      59, uint(SARibbonBar::NormalRibbonMode),

       0        // eod
};

void SARibbonBar::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SARibbonBar *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->applicationButtonClicked(); break;
        case 1: _t->currentRibbonTabChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 2: _t->ribbonModeChanged((*reinterpret_cast< SARibbonBar::RibbonMode(*)>(_a[1]))); break;
        case 3: _t->ribbonStyleChanged((*reinterpret_cast< SARibbonBar::RibbonStyles(*)>(_a[1]))); break;
        case 4: _t->titleBarHeightChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 5: _t->actionTriggered((*reinterpret_cast< QAction*(*)>(_a[1]))); break;
        case 6: _t->onWindowTitleChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->onWindowIconChanged((*reinterpret_cast< const QIcon(*)>(_a[1]))); break;
        case 8: _t->onCategoryWindowTitleChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 9: _t->onStackWidgetHided(); break;
        case 10: _t->onCurrentRibbonTabChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 11: _t->onCurrentRibbonTabClicked((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 12: _t->onCurrentRibbonTabDoubleClicked((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 13: _t->onContextsCategoryPageAdded((*reinterpret_cast< SARibbonCategory*(*)>(_a[1]))); break;
        case 14: _t->onContextsCategoryCategoryNameChanged((*reinterpret_cast< SARibbonCategory*(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 15: _t->onTabMoved((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 16: _t->addCategoryPage((*reinterpret_cast< QWidget*(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 5:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAction* >(); break;
            }
            break;
        case 13:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< SARibbonCategory* >(); break;
            }
            break;
        case 14:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< SARibbonCategory* >(); break;
            }
            break;
        case 16:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QWidget* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SARibbonBar::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SARibbonBar::applicationButtonClicked)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SARibbonBar::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SARibbonBar::currentRibbonTabChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SARibbonBar::*)(SARibbonBar::RibbonMode );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SARibbonBar::ribbonModeChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (SARibbonBar::*)(SARibbonBar::RibbonStyles );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SARibbonBar::ribbonStyleChanged)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (SARibbonBar::*)(int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SARibbonBar::titleBarHeightChanged)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (SARibbonBar::*)(QAction * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SARibbonBar::actionTriggered)) {
                *result = 5;
                return;
            }
        }
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty) {
        auto *_t = static_cast<SARibbonBar *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<int*>(_v) = QFlag(_t->currentRibbonStyle()); break;
        case 1: *reinterpret_cast< bool*>(_v) = _t->isMinimumMode(); break;
        case 2: *reinterpret_cast< bool*>(_v) = _t->haveShowMinimumModeButton(); break;
        case 3: *reinterpret_cast< QColor*>(_v) = _t->windowTitleTextColor(); break;
        case 4: *reinterpret_cast< QColor*>(_v) = _t->tabBarBaseLineColor(); break;
        case 5: *reinterpret_cast< Qt::Alignment*>(_v) = _t->windowTitleAligment(); break;
        case 6: *reinterpret_cast< bool*>(_v) = _t->isEnableWordWrap(); break;
        case 7: *reinterpret_cast< bool*>(_v) = _t->isEnableShowPannelTitle(); break;
        case 8: *reinterpret_cast< bool*>(_v) = _t->isTabOnTitle(); break;
        case 9: *reinterpret_cast< SARibbonPannel::PannelLayoutMode*>(_v) = _t->pannelLayoutMode(); break;
        default: break;
        }
    } else if (_c == QMetaObject::WriteProperty) {
        auto *_t = static_cast<SARibbonBar *>(_o);
        Q_UNUSED(_t)
        void *_v = _a[0];
        switch (_id) {
        case 0: _t->setRibbonStyle(QFlag(*reinterpret_cast<int*>(_v))); break;
        case 1: _t->setMinimumMode(*reinterpret_cast< bool*>(_v)); break;
        case 2: _t->showMinimumModeButton(*reinterpret_cast< bool*>(_v)); break;
        case 3: _t->setWindowTitleTextColor(*reinterpret_cast< QColor*>(_v)); break;
        case 4: _t->setTabBarBaseLineColor(*reinterpret_cast< QColor*>(_v)); break;
        case 5: _t->setWindowTitleAligment(*reinterpret_cast< Qt::Alignment*>(_v)); break;
        case 6: _t->setEnableWordWrap(*reinterpret_cast< bool*>(_v)); break;
        case 7: _t->setEnableShowPannelTitle(*reinterpret_cast< bool*>(_v)); break;
        case 8: _t->setTabOnTitle(*reinterpret_cast< bool*>(_v)); break;
        case 9: _t->setPannelLayoutMode(*reinterpret_cast< SARibbonPannel::PannelLayoutMode*>(_v)); break;
        default: break;
        }
    } else if (_c == QMetaObject::ResetProperty) {
    }
#endif // QT_NO_PROPERTIES
}

static const QMetaObject::SuperData qt_meta_extradata_SARibbonBar[] = {
    QMetaObject::SuperData::link<SARibbonPannel::staticMetaObject>(),
    nullptr
};

QT_INIT_METAOBJECT const QMetaObject SARibbonBar::staticMetaObject = { {
    QMetaObject::SuperData::link<QMenuBar::staticMetaObject>(),
    qt_meta_stringdata_SARibbonBar.data,
    qt_meta_data_SARibbonBar,
    qt_static_metacall,
    qt_meta_extradata_SARibbonBar,
    nullptr
} };


const QMetaObject *SARibbonBar::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SARibbonBar::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SARibbonBar.stringdata0))
        return static_cast<void*>(this);
    return QMenuBar::qt_metacast(_clname);
}

int SARibbonBar::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMenuBar::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 17)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 17;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 17)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 17;
    }
#ifndef QT_NO_PROPERTIES
    else if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyDesignable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyScriptable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyStored) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyEditable) {
        _id -= 10;
    } else if (_c == QMetaObject::QueryPropertyUser) {
        _id -= 10;
    }
#endif // QT_NO_PROPERTIES
    return _id;
}

// SIGNAL 0
void SARibbonBar::applicationButtonClicked()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void SARibbonBar::currentRibbonTabChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void SARibbonBar::ribbonModeChanged(SARibbonBar::RibbonMode _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void SARibbonBar::ribbonStyleChanged(SARibbonBar::RibbonStyles _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void SARibbonBar::titleBarHeightChanged(int _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void SARibbonBar::actionTriggered(QAction * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
