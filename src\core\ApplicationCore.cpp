#include "ApplicationCore.h"
#include "../utils/Logger.h"
#include "../utils/Common.h"
#include <QDir>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QDebug>

// Initialize static instance
ApplicationCore* ApplicationCore::_instance = nullptr;

ApplicationCore* ApplicationCore::instance()
{
    if (!_instance) {
        _instance = new ApplicationCore();
    }
    return _instance;
}

ApplicationCore::ApplicationCore()
    : configManager(nullptr)
    , debugMode(false)
{
    // Initialize app data directory
    appDataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    
    // Ensure directory exists
    QDir dir(appDataDir);
    if (!dir.exists()) {
        dir.mkpath(".");
    }
}

ApplicationCore::~ApplicationCore()
{
    shutdown();
}

bool ApplicationCore::initialize()
{
    // Use qDebug instead of LOG_INFO before logger is initialized
    qDebug() << "Initializing application core";
    
    // Initialize logger first
    if (!initializeLogger()) {
        return false;
    }
    
    // Now that logger is initialized, we can use it
    LOG_INFO("Logger initialized successfully", "ApplicationCore");
    
    // Initialize config manager
    configManager = new ConfigManager();
    if (!configManager->initialize()) {
        LOG_ERROR("Failed to initialize configuration manager", "ApplicationCore");
        return false;
    }
    
    // Read debug mode setting
    debugMode = configManager->getSetting("app/debugMode", false).toBool();
    
    LOG_INFO("Application core initialized successfully", "ApplicationCore");
    return true;
}

void ApplicationCore::shutdown()
{
    if (initialized) {
        LOG_INFO("Shutting down application core", "ApplicationCore");
    } else {
        qDebug() << "Shutting down application core";
    }
    
    // Clean up config manager
    if (configManager) {
        delete configManager;
        configManager = nullptr;
    }
    
    // Clean up self (singleton)
    if (this == _instance) {
        _instance = nullptr;
    }
}

ConfigManager* ApplicationCore::getConfigManager() const
{
    return configManager;
}

bool ApplicationCore::isDebugMode() const
{
    return debugMode;
}

QString ApplicationCore::getAppDataDir() const
{
    return appDataDir;
}

bool ApplicationCore::initializeLogger()
{
    QString logPath = QDir(appDataDir).filePath("application.log");
    if (!Logger::initialize(logPath)) {
        // If we can't initialize the logger, use qDebug
        qDebug() << "Failed to initialize logger at:" << logPath;
        return false;
    }
    initialized = true;
    return true;
} 