#include "ApplicationCore.h"
#include "ConfigManager.h"
#include "../utils/Logger.h"
#include <QDir>
#include <QCoreApplication>
#include <QStyleFactory>

ApplicationCore* ApplicationCore::m_instance = nullptr;

ApplicationCore* ApplicationCore::instance()
{
    if (!m_instance) {
        m_instance = new ApplicationCore();
    }
    return m_instance;
}

ApplicationCore::ApplicationCore(QObject *parent)
    : QObject(parent)
    , m_configManager(nullptr)
    , m_logger(nullptr)
    , m_translator(nullptr)
    , m_currentLanguage("zh_CN")
    , m_currentTheme("default")
    , m_initialized(false)
{
}

ApplicationCore::~ApplicationCore()
{
    shutdown();
}

bool ApplicationCore::initialize()
{
    if (m_initialized) {
        return true;
    }
    
    // Initialize configuration manager
    initializeConfiguration();
    
    // Initialize logging system
    initializeLogging();
    
    // Load application settings
    loadApplicationSettings();
    
    m_initialized = true;
    
    LOG_INFO("Application initialized successfully", "ApplicationCore");
    emit applicationInitialized();
    
    return true;
}

void ApplicationCore::shutdown()
{
    if (!m_initialized) {
        return;
    }
    
    LOG_INFO("Application shutting down", "ApplicationCore");
    
    // Save application settings
    saveApplicationSettings();
    
    // Clean up resources
    if (m_translator) {
        QCoreApplication::removeTranslator(m_translator);
        delete m_translator;
        m_translator = nullptr;
    }
    
    m_initialized = false;
    emit applicationShutdown();
}

void ApplicationCore::initializeConfiguration()
{
    m_configManager = ConfigManager::instance();
}

void ApplicationCore::initializeLogging()
{
    m_logger = Logger::instance();
    
    // Load log settings from configuration
    if (m_configManager) {
        LogLevel logLevel = m_configManager->getLogLevel();
        bool logToFile = m_configManager->isLogToFileEnabled();
        
        m_logger->setLogLevel(logLevel);
        m_logger->setLogToFile(logToFile);
    }
}

void ApplicationCore::loadApplicationSettings()
{
    if (!m_configManager) {
        return;
    }
    
    // Load language settings
    QString language = m_configManager->getLanguage();
    loadLanguage(language);
    
    // Load theme settings
    QString theme = m_configManager->getTheme();
    loadTheme(theme);
}

void ApplicationCore::saveApplicationSettings()
{
    if (!m_configManager) {
        return;
    }
    
    // Save current settings
    m_configManager->setLanguage(m_currentLanguage);
    m_configManager->setTheme(m_currentTheme);
}

bool ApplicationCore::loadLanguage(const QString& language)
{
    if (m_currentLanguage == language) {
        return true;
    }
    
    // Remove old translator
    if (m_translator) {
        QCoreApplication::removeTranslator(m_translator);
        delete m_translator;
        m_translator = nullptr;
    }
    
    // Load new translation file
    m_translator = new QTranslator(this);
    QString translationFile = QString(":/translations/%1.qm").arg(language);
    
    if (m_translator->load(translationFile)) {
        QCoreApplication::installTranslator(m_translator);
        m_currentLanguage = language;
        
        LOG_INFO(QString("Language changed to: %1").arg(language), "ApplicationCore");
        emit languageChanged(language);
        return true;
    } else {
        LOG_WARNING(QString("Failed to load translation file: %1").arg(translationFile), "ApplicationCore");
        delete m_translator;
        m_translator = nullptr;
        return false;
    }
}

QStringList ApplicationCore::getAvailableLanguages() const
{
    return QStringList() << "zh_CN" << "en_US";
}

QString ApplicationCore::getCurrentLanguage() const
{
    return m_currentLanguage;
}

bool ApplicationCore::loadTheme(const QString& theme)
{
    if (m_currentTheme == theme) {
        return true;
    }
    
    // Here we can load different stylesheets or themes
    // Currently just recording the theme name
    m_currentTheme = theme;
    
    LOG_INFO(QString("Theme changed to: %1").arg(theme), "ApplicationCore");
    emit themeChanged(theme);
    
    return true;
}

QStringList ApplicationCore::getAvailableThemes() const
{
    return QStringList() << "default" << "dark" << "light";
}

QString ApplicationCore::getCurrentTheme() const
{
    return m_currentTheme;
}

QString ApplicationCore::getAppName() const
{
    return AppConstants::APP_NAME;
}

QString ApplicationCore::getAppVersion() const
{
    return AppConstants::APP_VERSION;
}

QString ApplicationCore::getOrganizationName() const
{
    return AppConstants::ORGANIZATION_NAME;
}

ConfigManager* ApplicationCore::getConfigManager() const
{
    return m_configManager;
}

Logger* ApplicationCore::getLogger() const
{
    return m_logger;
} 