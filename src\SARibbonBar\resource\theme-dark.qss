﻿/* 
start ribbon set
dark style theme
*/

/*SARibbonBar*/
SARibbonBar{
  background-color: #2b2b2b;
  border: solid #2b2b2b;
  color:#b2b2b2;
}

/*SARibbonQuickAccessBar*/
SARibbonQuickAccessBar{
   background-color: transparent;
}


/*SARibbonCtrlContainer*/
SARibbonCtrlContainer {
  background-color: transparent;
}

/*
SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
*/
SARibbonSeparatorWidget {
  color: #9b9b9b;
}

/*SARibbonCategory*/
SARibbonCategory {
  background-color: #b2b2b2;
}
SARibbonCategory:focus {
  outline: none;
}

/*SARibbonPannel*/

SARibbonPannel {
  background-color: transparent;
  border: none;
}

/*SARibbonPannelLabel Pannel下标题栏*/

SARibbonPannelLabel {
    background-color: transparent;
    color: #242424;
}

/*SARibbonPannelOptionButton*/
SARibbonPannelOptionButton {
  border:none;
  background-color: transparent;
  color:#242424;
}

SARibbonPannelOptionButton:hover {
  background-color:#a0a0a0;
}

/*SARibbonButtonGroupWidget*/
SARibbonButtonGroupWidget{
  background-color: transparent;
  color: #242424;
}

SARibbonPannel > SARibbonButtonGroupWidget {
  border: 1px solid #888888;
  background-color: transparent;
}


/*SARibbonStackedWidget*/
SARibbonStackedWidget{
  border-top-width: 0px;
}
SARibbonStackedWidget:focus{
  outline: none;

}
/*SARibbonApplicationButton*/
SARibbonApplicationButton{
  color:#242424;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  background-color: #b2b2b2;
}

SARibbonApplicationButton:hover{
  background-color: #a0a0a0;
}

SARibbonApplicationButton:pressed{
  background-color: #858585;
}

SARibbonApplicationButton:focus{
  outline: none;
}

SARibbonApplicationButton::menu-indicator {
  /*subcontrol-position: right;*/
  width:0px;
}

/*SARibbonTabBar*/
SARibbonTabBar{
    background-color: transparent;
}

SARibbonTabBar::tab {
  color: #f0f1f2;
  border:1px solid transparent;/*加上边框，否则选中后加入边框后会有1px的偏移*/
  background: transparent;
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 5px;
  margin-bottom: 0px;
  min-width: 50px;
  padding-left: 5px;
  padding-right: 5px;
}

SARibbonTabBar::tab:selected {
  color: #242424;
  background-color: #b2b2b2;
  border: 1px solid #737373;
}

SARibbonTabBar::tab:hover:!selected {
  border:1px solid #a6a6a6;
  color: white;
}

SARibbonTabBar::tab:!selected {
  color: #a0a0a0;
  border:1px solid transparent;
  background: transparent;
}

/*SARibbonCheckBox*/
SARibbonCheckBox{
    color:#242424;
    background-color:transparent;
}

/*SARibbonToolButton*/

SARibbonToolButton{
    border:none;
    color:#242424;
    background-color:#b2b2b2;
} 

SARibbonToolButton:focus {
  border: 1px solid #858585;
  color: #242424;
  background-color: #a0a0a0;
}
SARibbonToolButton:pressed{
    color:#242424;
    background-color: #858585;
}
SARibbonToolButton:checked{
    color:#242424;
    background-color: #9b9b9b;
}
SARibbonToolButton:hover {
    color:#242424;
    background-color: #a0a0a0;
}

/*SARibbonControlToolButton*/

SARibbonControlToolButton {
  background-color: transparent;/*{ControlToolButton.BKColor}*/
  color: #a0a0a0;
  border: 1px solid transparent;
}

SARibbonControlToolButton:pressed {
  background-color: #858585;/*{ControlToolButton.BKColor:pressed}*/
}

SARibbonControlToolButton:checked {
  border: 1px solid #888888;/*{ControlToolButton.BorderColor:checked}*/
  background-color: #9b9b9b;/*{ControlToolButton.BKColor:checked}*/
}

SARibbonControlToolButton:hover {
  color: black;
  border: 1px solid #888888;/*{ControlToolButton.BorderColor:hover}*/
  background-color: #a0a0a0;/*{ControlToolButton.BKColor:hover}*/
}

/*SARibbonControlButton*/
SARibbonControlButton{
  background-color:transparent;
  border: none;
  color:#242424;
}
SARibbonControlButton:pressed{
  background-color: #858585;
}
SARibbonControlButton:checked{
  border: 1px solid #888888;
  background-color: #9b9b9b;
}
SARibbonControlButton:hover {
  border: 1px solid #888888;
  background-color: #a0a0a0;
 }

SARibbonGalleryButton{
  background-color:#b2b2b2;
  border: 1px solid #888888;
  color: #242424;
}


/*SARibbonMenu*/
SARibbonMenu {
  color:#242424;
  background-color: #b2b2b2;

}
SARibbonMenu::item {
  padding: 5px;
  background-color: transparent;
}
SARibbonMenu::item:selected {
  background-color: #9b9b9b;
}
SARibbonMenu::item:hover {
    color:#242424;
    border: 1px solid #fcfcfc;
}
SARibbonMenu::icon{
  margin-left: 1px;
}

/*SARibbonGallery*/
SARibbonGallery {
  background-color: #cccccc;
  color: #242424;
  border: 1px solid #888888;
}

/*SARibbonGalleryGroup*/
SARibbonGalleryGroup {
  show-decoration-selected: 1;
  background-color: transparent;
  color: #242424;
}
SARibbonGalleryGroup::item:selected {
  background-color: #9b9b9b;
}
SARibbonGalleryGroup::item:hover {
  border: 1px solid #a0a0a0;
}
SARibbonGalleryGroup::item {
  color: #242424;
}

/*RibbonGalleryViewport*/

SARibbonGalleryViewport {
  background-color: #ffffff;
}

/*SARibbonLineEdit*/
SARibbonLineEdit {
  border:1px solid #888888;
  background-color: white;
  color: #242424;
  selection-background-color: #6a9eff;
  selection-color: white;
}


/*SARibbonComboBox*/
SARibbonComboBox {
  border: 1px  solid #888888;
  background-color: white;
}

SARibbonComboBox:hover{
  border: 1px solid #cfcecc;
  color : #242424;
}

SARibbonComboBox:editable {
  color : #242424;
  background-color: white;
  selection-background-color: #6a9eff;
  selection-color: white;
}

SARibbonComboBox::drop-down {
  subcontrol-origin: padding;
  subcontrol-position: top right;
  width: 1em;
  border: none;
}

SARibbonComboBox::drop-down:hover {
  border: none;
  background-color: #a0a0a0;
}

SARibbonComboBox::down-arrow {
  image: url(:/SARibbon/image/resource/ArrowDown.png);
}



/*SARibbonCategoryScrollButton*/
SARibbonCategoryScrollButton {
  border: 1px solid #888888;
  color: #242424;
  background-color: #b2b2b2;
}

SARibbonCategoryScrollButton[arrowType="3"] {
  border-right-width: 1px;
}

SARibbonCategoryScrollButton[arrowType="4"] {
  border-left-width: 1px;
}
/*SARibbonSystemToolButton*/

SARibbonSystemToolButton {
  background-color: transparent;
  border:none;
}

SARibbonSystemToolButton:focus {
  outline: none;
}

/* 深色模式的系统按钮设置 */
/*Min*/
SARibbonSystemToolButton#SAMinimizeWindowButton {
  background-position:center;
  background-repeat: no-repeat;
  background-image: url(:/SARibbon/image/resource/Titlebar_Min_Hover.svg);
}
SARibbonSystemToolButton#SAMinimizeWindowButton:hover{
  background-color: #b2b2b2;
}
SARibbonSystemToolButton#SAMinimizeWindowButton:pressed{
  background-color: #cacacb;
}
/*Max*/
SARibbonSystemToolButton#SAMaximizeWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/SARibbon/image/resource/Titlebar_Max_Hover.svg);
}
SARibbonSystemToolButton#SAMaximizeWindowButton:hover {
    background-color: #b2b2b2;
}
SARibbonSystemToolButton#SAMaximizeWindowButton:checked {
    background-image: url(:/SARibbon/image/resource/Titlebar_Normal_Hover.svg) center;
}


/*Close*/
SARibbonSystemToolButton#SACloseWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/SARibbon/image/resource/Titlebar_Close_Hover.svg);
}
SARibbonSystemToolButton#SACloseWindowButton:hover {
  background-color: #e81123;
}
SARibbonSystemToolButton#SACloseWindowButton:pressed {
    background-color: #f1707a;
}
