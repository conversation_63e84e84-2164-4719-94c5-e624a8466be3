#include "IFileExample.h"
#include "../utils/Logger.h"
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QDebug>

IFileExample::IFileExample(QObject *parent)
    : QObject(parent)
{
}

IFileExample::~IFileExample()
{
}

bool IFileExample::loadIFile(const QString &filePath)
{
    LOG_INFO("Loading .i file: " + filePath, "IFileExample");
    
    m_errors.clear();
    
    if (!QFile::exists(filePath)) {
        m_errors << "File does not exist: " + filePath;
        LOG_ERROR("File does not exist: " + filePath, "IFileExample");
        return false;
    }
    
    if (!m_inputData.loadFromFile(filePath)) {
        m_errors = m_inputData.getErrors();
        LOG_ERROR("Failed to load .i file: " + filePath, "IFileExample");
        return false;
    }
    
    LOG_INFO("Successfully loaded .i file: " + filePath, "IFileExample");
    return true;
}

bool IFileExample::saveIFile(const QString &filePath)
{
    LOG_INFO("Saving .i file: " + filePath, "IFileExample");
    
    m_errors.clear();
    
    if (!m_inputData.isValid()) {
        m_errors << "Cannot save invalid input data";
        LOG_ERROR("Cannot save invalid input data", "IFileExample");
        return false;
    }
    
    if (!m_inputData.saveToFile(filePath)) {
        m_errors << "Failed to save file: " + filePath;
        LOG_ERROR("Failed to save .i file: " + filePath, "IFileExample");
        return false;
    }
    
    LOG_INFO("Successfully saved .i file: " + filePath, "IFileExample");
    return true;
}

bool IFileExample::createSampleIFile(InputFileType problemType)
{
    LOG_INFO("Creating sample .i file for problem type: " + QString::number(static_cast<int>(problemType)), "IFileExample");
    
    m_errors.clear();
    m_inputData.clear();
    
    switch (problemType) {
        case InputFileType::NC_WELANDER:
            createNCWelanderSample();
            break;
        case InputFileType::DISCHARGE_AIR:
            createDischargeAirSample();
            break;
        case InputFileType::CRITICAL_FLOW_AIR:
        case InputFileType::CRITICAL_FLOW_WATER:
            createCriticalFlowSample();
            break;
        default:
            m_errors << "Unsupported problem type";
            LOG_ERROR("Unsupported problem type", "IFileExample");
            return false;
    }
    
    // Add common components and settings
    addStandardControlCards();
    addStandardComponents();
    
    LOG_INFO("Successfully created sample .i file", "IFileExample");
    return true;
}

int IFileExample::batchProcessIFiles(const QString &directoryPath, bool recursive)
{
    LOG_INFO("Batch processing .i files in directory: " + directoryPath, "IFileExample");
    
    m_errors.clear();
    
    QDir dir(directoryPath);
    if (!dir.exists()) {
        m_errors << "Directory does not exist: " + directoryPath;
        LOG_ERROR("Directory does not exist: " + directoryPath, "IFileExample");
        return 0;
    }
    
    QStringList filters;
    filters << "*.i";
    dir.setNameFilters(filters);
    
    QFileInfoList fileList;
    if (recursive) {
        dir.setFilter(QDir::Files | QDir::NoDotAndDotDot | QDir::NoSymLinks | QDir::Readable | QDir::Dirs);
        fileList = dir.entryInfoList();
    } else {
        dir.setFilter(QDir::Files | QDir::NoDotAndDotDot | QDir::NoSymLinks | QDir::Readable);
        fileList = dir.entryInfoList();
    }
    
    int successCount = 0;
    for (const QFileInfo &fileInfo : fileList) {
        if (fileInfo.isDir() && recursive) {
            // Recursive call for subdirectories
            successCount += batchProcessIFiles(fileInfo.absoluteFilePath(), recursive);
        } else if (fileInfo.isFile() && fileInfo.suffix().toLower() == "i") {
            // Process .i file
            if (loadIFile(fileInfo.absoluteFilePath())) {
                // Perform some processing here if needed
                successCount++;
            }
        }
    }
    
    LOG_INFO("Successfully processed " + QString::number(successCount) + " .i files", "IFileExample");
    return successCount;
}

QStringList IFileExample::validateIFile(const QString &filePath)
{
    LOG_INFO("Validating .i file: " + filePath, "IFileExample");
    
    m_errors.clear();
    QStringList validationErrors;
    
    if (!QFile::exists(filePath)) {
        m_errors << "File does not exist: " + filePath;
        LOG_ERROR("File does not exist: " + filePath, "IFileExample");
        return m_errors;
    }
    
    // Load the file
    if (!m_inputData.loadFromFile(filePath)) {
        validationErrors = m_inputData.getErrors();
        LOG_ERROR("Failed to load .i file: " + filePath, "IFileExample");
        return validationErrors;
    }
    
    // Validate the structure
    if (!m_inputData.validateIFileStructure()) {
        validationErrors.append(m_inputData.getErrors());
    }
    
    // Validate component references
    if (!m_inputData.validateComponentReferences()) {
        validationErrors.append(m_inputData.getErrors());
    }
    
    // Validate heat structure references
    if (!m_inputData.validateHeatStructureReferences()) {
        validationErrors.append(m_inputData.getErrors());
    }
    
    // Check for consistency issues
    QStringList consistencyIssues = m_inputData.checkIFileConsistency();
    if (!consistencyIssues.isEmpty()) {
        validationErrors.append(consistencyIssues);
    }
    
    if (validationErrors.isEmpty()) {
        LOG_INFO("File is valid: " + filePath, "IFileExample");
    } else {
        LOG_WARNING("File has validation issues: " + filePath, "IFileExample");
        for (const QString &error : validationErrors) {
            LOG_WARNING("  - " + error, "IFileExample");
        }
    }
    
    return validationErrors;
}

const InputData& IFileExample::getInputData() const
{
    return m_inputData;
}

QStringList IFileExample::getErrors() const
{
    return m_errors;
}

void IFileExample::clear()
{
    m_inputData.clear();
    m_errors.clear();
}

void IFileExample::createNCWelanderSample()
{
    // Set problem title
    m_inputData.problemTitle = "NC Welander Sample Problem";
    m_inputData.fileType = InputFileType::NC_WELANDER;
    
    // Create a pipe component
    PipeComponent pipe;
    pipe.componentId = "1000000";
    pipe.componentName = "Welander Pipe";
    pipe.componentType = "pipe";
    pipe.numberOfVolumes = 10;
    
    // Initialize arrays
    pipe.volumes.resize(pipe.numberOfVolumes);
    pipe.lengths.resize(pipe.numberOfVolumes);
    pipe.elevations.resize(pipe.numberOfVolumes);
    pipe.roughness.resize(pipe.numberOfVolumes);
    pipe.hydraulicDiameters.resize(pipe.numberOfVolumes);
    pipe.angles.resize(pipe.numberOfVolumes);
    pipe.initialConditions.resize(pipe.numberOfVolumes);
    
    // Set properties for all volumes
    for (int i = 0; i < pipe.numberOfVolumes; ++i) {
        pipe.volumes[i] = 0.01;
        pipe.lengths[i] = 0.1;
        pipe.elevations[i] = i * 0.1;
        pipe.roughness[i] = 1.0e-5;
        pipe.hydraulicDiameters[i] = 0.1;
        pipe.angles[i] = 90.0;
        
        pipe.initialConditions[i].thermodynamicState = 1;
        pipe.initialConditions[i].pressure = 1.0e5;
        pipe.initialConditions[i].temperature = 300.0;
        pipe.initialConditions[i].quality = 0.0;
        pipe.initialConditions[i].velocity = 0.0;
        pipe.initialConditions[i].boronDensity = 0.0;
    }
    
    m_inputData.pipes.append(pipe);
    
    // Add heat structure
    HeatStructure hs;
    hs.heatStructureId = "1500000";
    hs.numberOfAxialNodes = 10;
    hs.numberOfRadialNodes = 5;
    hs.geometryType = 1;
    hs.steadyStateFlag = 0;
    hs.leftBoundary = 0.0;
    hs.rightBoundary = 0.1;
    
    m_inputData.heatStructures.append(hs);
}

void IFileExample::createDischargeAirSample()
{
    // Set problem title
    m_inputData.problemTitle = "Discharge Air Sample Problem";
    m_inputData.fileType = InputFileType::DISCHARGE_AIR;
    
    // Create a single volume
    SingleVolumeComponent volume;
    volume.componentId = "2000000";
    volume.componentName = "Air Tank";
    volume.componentType = "tmdpvol";
    volume.volume = 1.0;
    volume.length = 1.0;
    volume.elevation = 0.0;
    volume.angle = 0.0;
    volume.roughness = 1.0e-5;
    volume.hydraulicDiameter = 1.0;
    volume.thermodynamicState = 1;
    volume.pressure = 5.0e5;
    volume.temperature = 300.0;
    volume.quality = 1.0;
    
    m_inputData.volumes.append(volume);
    
    // Create a junction
    JunctionComponent junction;
    junction.componentId = "3000000";
    junction.componentName = "Discharge Junction";
    junction.componentType = "valve";
    junction.fromComponent = "2000000";
    junction.toComponent = "4000000";
    junction.area = 0.01;
    junction.forwardLoss = 0.0;
    junction.reverseLoss = 0.0;
    junction.flags = 0;
    junction.velocityFlag = 0;
    junction.velocity = 0.0;
    junction.interfaceVelocity = 0.0;
    junction.quality = 1.0;
    
    m_inputData.junctions.append(junction);
    
    // Create a boundary volume
    SingleVolumeComponent boundary;
    boundary.componentId = "4000000";
    boundary.componentName = "Atmosphere";
    boundary.componentType = "tmdpvol";
    boundary.volume = 1000.0;
    boundary.length = 10.0;
    boundary.elevation = 0.0;
    boundary.angle = 0.0;
    boundary.roughness = 1.0e-5;
    boundary.hydraulicDiameter = 10.0;
    boundary.thermodynamicState = 1;
    boundary.pressure = 1.0e5;
    boundary.temperature = 300.0;
    boundary.quality = 1.0;
    
    m_inputData.volumes.append(boundary);
}

void IFileExample::createCriticalFlowSample()
{
    // Set problem title
    m_inputData.problemTitle = "Critical Flow Sample Problem";
    m_inputData.fileType = InputFileType::CRITICAL_FLOW_AIR;
    
    // Create a single volume
    SingleVolumeComponent volume;
    volume.componentId = "2000000";
    volume.componentName = "High Pressure Tank";
    volume.componentType = "tmdpvol";
    volume.volume = 1.0;
    volume.length = 1.0;
    volume.elevation = 0.0;
    volume.angle = 0.0;
    volume.roughness = 1.0e-5;
    volume.hydraulicDiameter = 1.0;
    volume.thermodynamicState = 1;
    volume.pressure = 10.0e5;
    volume.temperature = 300.0;
    volume.quality = 0.0;
    
    m_inputData.volumes.append(volume);
    
    // Create a pipe with a nozzle
    PipeComponent pipe;
    pipe.componentId = "1000000";
    pipe.componentName = "Nozzle Pipe";
    pipe.componentType = "pipe";
    pipe.numberOfVolumes = 5;
    
    // Initialize arrays
    pipe.volumes.resize(pipe.numberOfVolumes);
    pipe.lengths.resize(pipe.numberOfVolumes);
    pipe.elevations.resize(pipe.numberOfVolumes);
    pipe.roughness.resize(pipe.numberOfVolumes);
    pipe.hydraulicDiameters.resize(pipe.numberOfVolumes);
    pipe.angles.resize(pipe.numberOfVolumes);
    pipe.initialConditions.resize(pipe.numberOfVolumes);
    
    // Set properties with decreasing diameter
    double startDiameter = 0.1;
    double endDiameter = 0.01;
    for (int i = 0; i < pipe.numberOfVolumes; ++i) {
        double fraction = static_cast<double>(i) / (pipe.numberOfVolumes - 1);
        double diameter = startDiameter - fraction * (startDiameter - endDiameter);
        double area = 3.14159 * diameter * diameter / 4.0;
        
        pipe.volumes[i] = area * 0.1;
        pipe.lengths[i] = 0.1;
        pipe.elevations[i] = 0.0;
        pipe.roughness[i] = 1.0e-5;
        pipe.hydraulicDiameters[i] = diameter;
        pipe.angles[i] = 0.0;
        
        pipe.initialConditions[i].thermodynamicState = 1;
        pipe.initialConditions[i].pressure = 10.0e5 - fraction * 9.0e5;
        pipe.initialConditions[i].temperature = 300.0;
        pipe.initialConditions[i].quality = 0.0;
        pipe.initialConditions[i].velocity = 10.0 + fraction * 90.0;
        pipe.initialConditions[i].boronDensity = 0.0;
    }
    
    m_inputData.pipes.append(pipe);
    
    // Create two junctions
    JunctionComponent inletJunction;
    inletJunction.componentId = "3000000";
    inletJunction.componentName = "Inlet Junction";
    inletJunction.componentType = "sngljun";
    inletJunction.fromComponent = "2000000";
    inletJunction.toComponent = "1000000";
    inletJunction.area = 3.14159 * startDiameter * startDiameter / 4.0;
    inletJunction.forwardLoss = 0.0;
    inletJunction.reverseLoss = 0.0;
    inletJunction.flags = 0;
    inletJunction.velocityFlag = 0;
    inletJunction.velocity = 10.0;
    inletJunction.interfaceVelocity = 0.0;
    inletJunction.quality = 0.0;
    
    m_inputData.junctions.append(inletJunction);
    
    // Create a boundary volume
    SingleVolumeComponent boundary;
    boundary.componentId = "4000000";
    boundary.componentName = "Atmosphere";
    boundary.componentType = "tmdpvol";
    boundary.volume = 1000.0;
    boundary.length = 10.0;
    boundary.elevation = 0.0;
    boundary.angle = 0.0;
    boundary.roughness = 1.0e-5;
    boundary.hydraulicDiameter = 10.0;
    boundary.thermodynamicState = 1;
    boundary.pressure = 1.0e5;
    boundary.temperature = 300.0;
    boundary.quality = 0.0;
    
    m_inputData.volumes.append(boundary);
    
    // Outlet junction
    JunctionComponent outletJunction;
    outletJunction.componentId = "3100000";
    outletJunction.componentName = "Outlet Junction";
    outletJunction.componentType = "sngljun";
    outletJunction.fromComponent = "1000000";
    outletJunction.toComponent = "4000000";
    outletJunction.area = 3.14159 * endDiameter * endDiameter / 4.0;
    outletJunction.forwardLoss = 0.0;
    outletJunction.reverseLoss = 0.0;
    outletJunction.flags = 0;
    outletJunction.velocityFlag = 0;
    outletJunction.velocity = 100.0;
    outletJunction.interfaceVelocity = 0.0;
    outletJunction.quality = 0.0;
    
    m_inputData.junctions.append(outletJunction);
}

void IFileExample::addStandardComponents()
{
    // This method would add standard components needed in most problems
    // For example, standard time tables, power tables, etc.
    
    // Add a table
    TableData table;
    table.tableId = "202000000";
    table.tableType = "temp";
    
    // Add some data points
    table.dataPoints.append(qMakePair(0.0, 300.0));
    table.dataPoints.append(qMakePair(10.0, 310.0));
    table.dataPoints.append(qMakePair(20.0, 320.0));
    table.dataPoints.append(qMakePair(30.0, 330.0));
    table.dataPoints.append(qMakePair(40.0, 340.0));
    table.dataPoints.append(qMakePair(50.0, 350.0));
    
    m_inputData.tables.append(table);
    
    // Add some plot variables
    PlotVariable pvar1;
    pvar1.variableId = 301;
    pvar1.variableType = "tempf";
    pvar1.componentId = "1000000";
    pvar1.plotFlag = 1;
    m_inputData.plotVariables.append(pvar1);
    
    PlotVariable pvar2;
    pvar2.variableId = 302;
    pvar2.variableType = "p";
    pvar2.componentId = "1000000";
    pvar2.plotFlag = 1;
    m_inputData.plotVariables.append(pvar2);
    
    PlotVariable pvar3;
    pvar3.variableId = 303;
    pvar3.variableType = "mflowj";
    pvar3.componentId = "3000000";
    pvar3.plotFlag = 1;
    m_inputData.plotVariables.append(pvar3);
}

void IFileExample::addStandardControlCards()
{
    // Set control cards
    m_inputData.controlCard.problemNumber = 100;
    m_inputData.controlCard.problemType = "new";
    m_inputData.controlCard.analysisType = "transnt";
    m_inputData.controlCard.endTime = 100.0;
    m_inputData.controlCard.minTimeStep = 1.0e-6;
    m_inputData.controlCard.maxTimeStep = 0.1;
    m_inputData.controlCard.controlOption = 3;
    m_inputData.controlCard.minorEdit = 10;
    m_inputData.controlCard.majorEdit = 100;
    m_inputData.controlCard.restartFreq = 1000;
    
    // Set unit system
    m_inputData.unitSystem.inputUnits = "si";
    m_inputData.unitSystem.outputUnits = "si";
    m_inputData.unitSystem.workingFluid = "h2o";
    m_inputData.unitSystem.scaleFactor = 1.0;
} 