#ifndef FILEREPOSITORY_H
#define FILEREPOSITORY_H

#include "IRepository.h"
#include "../models/BaseModel.h"
#include <QMap>
#include <QString>
#include <QMutex>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

/**
 * @brief 文件数据仓库类
 * @tparam T 数据模型类型
 * 
 * 基于文件的数据仓库实现，支持JSON格式的数据持久化。
 */
template<typename T>
class FileRepository : public IRepository<T>
{
public:
    /**
     * @brief 构造函数
     * @param filePath 文件路径
     */
    explicit FileRepository(const QString& filePath)
        : m_filePath(filePath)
        , m_autoSave(true)
        , m_loaded(false)
    {
    }

    /**
     * @brief 析构函数
     */
    virtual ~FileRepository()
    {
        if (m_autoSave) {
            save();
        }
    }

    // IRepository接口实现
    bool add(const T& item) override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            load();
        }
        
        QString id = item.id();
        if (m_items.contains(id)) {
            return false; // 已存在
        }
        
        m_items[id] = item;
        
        if (m_autoSave) {
            return save();
        }
        
        return true;
    }

    bool update(const T& item) override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            load();
        }
        
        QString id = item.id();
        if (!m_items.contains(id)) {
            return false; // 不存在
        }
        
        T updatedItem = item;
        updatedItem.setUpdatedAt(QDateTime::currentDateTime());
        m_items[id] = updatedItem;
        
        if (m_autoSave) {
            return save();
        }
        
        return true;
    }

    bool remove(const QString& id) override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            load();
        }
        
        if (!m_items.contains(id)) {
            return false;
        }
        
        m_items.remove(id);
        
        if (m_autoSave) {
            return save();
        }
        
        return true;
    }

    T get(const QString& id) const override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            const_cast<FileRepository*>(this)->load();
        }
        
        return m_items.value(id, T());
    }

    QList<T> getAll() const override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            const_cast<FileRepository*>(this)->load();
        }
        
        return m_items.values();
    }

    QList<T> find(const std::function<bool(const T&)>& predicate) const override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            const_cast<FileRepository*>(this)->load();
        }
        
        QList<T> result;
        for (const T& item : m_items.values()) {
            if (predicate(item)) {
                result.append(item);
            }
        }
        
        return result;
    }

    bool exists(const QString& id) const override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            const_cast<FileRepository*>(this)->load();
        }
        
        return m_items.contains(id);
    }

    int count() const override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            const_cast<FileRepository*>(this)->load();
        }
        
        return m_items.size();
    }

    void clear() override
    {
        QMutexLocker locker(&m_mutex);
        
        m_items.clear();
        
        if (m_autoSave) {
            save();
        }
    }

    int addBatch(const QList<T>& items) override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            load();
        }
        
        int addedCount = 0;
        for (const T& item : items) {
            QString id = item.id();
            if (!m_items.contains(id)) {
                m_items[id] = item;
                addedCount++;
            }
        }
        
        if (m_autoSave && addedCount > 0) {
            save();
        }
        
        return addedCount;
    }

    int updateBatch(const QList<T>& items) override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            load();
        }
        
        int updatedCount = 0;
        for (const T& item : items) {
            QString id = item.id();
            if (m_items.contains(id)) {
                T updatedItem = item;
                updatedItem.setUpdatedAt(QDateTime::currentDateTime());
                m_items[id] = updatedItem;
                updatedCount++;
            }
        }
        
        if (m_autoSave && updatedCount > 0) {
            save();
        }
        
        return updatedCount;
    }

    int removeBatch(const QList<QString>& ids) override
    {
        QMutexLocker locker(&m_mutex);
        
        if (!m_loaded) {
            load();
        }
        
        int removedCount = 0;
        for (const QString& id : ids) {
            if (m_items.remove(id) > 0) {
                removedCount++;
            }
        }
        
        if (m_autoSave && removedCount > 0) {
            save();
        }
        
        return removedCount;
    }

    bool load() override
    {
        QMutexLocker locker(&m_mutex);
        
        QFile file(m_filePath);
        if (!file.open(QIODevice::ReadOnly)) {
            m_loaded = true; // 标记为已加载，即使文件不存在
            return false;
        }
        
        QByteArray data = file.readAll();
        file.close();
        
        QJsonDocument doc = QJsonDocument::fromJson(data);
        if (!doc.isObject()) {
            m_loaded = true;
            return false;
        }
        
        QJsonObject rootObj = doc.object();
        QJsonArray itemsArray = rootObj["items"].toArray();
        
        m_items.clear();
        for (const QJsonValue& value : itemsArray) {
            if (value.isObject()) {
                T item;
                item.fromJson(value.toObject());
                m_items[item.id()] = item;
            }
        }
        
        m_loaded = true;
        return true;
    }

    bool save() override
    {
        QMutexLocker locker(&m_mutex);
        
        QJsonArray itemsArray;
        for (const T& item : m_items.values()) {
            itemsArray.append(item.toJson());
        }
        
        QJsonObject rootObj;
        rootObj["items"] = itemsArray;
        rootObj["version"] = "1.0";
        rootObj["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
        
        QJsonDocument doc(rootObj);
        
        QFile file(m_filePath);
        if (!file.open(QIODevice::WriteOnly)) {
            return false;
        }
        
        qint64 bytesWritten = file.write(doc.toJson());
        file.close();
        
        return bytesWritten > 0;
    }

    /**
     * @brief 设置自动保存
     * @param autoSave 是否自动保存
     */
    void setAutoSave(bool autoSave)
    {
        m_autoSave = autoSave;
    }

    /**
     * @brief 获取文件路径
     * @return 文件路径
     */
    QString filePath() const
    {
        return m_filePath;
    }

private:
    QString m_filePath;
    QMap<QString, T> m_items;
    mutable QMutex m_mutex;
    bool m_autoSave;
    bool m_loaded;
};

#endif // FILEREPOSITORY_H
