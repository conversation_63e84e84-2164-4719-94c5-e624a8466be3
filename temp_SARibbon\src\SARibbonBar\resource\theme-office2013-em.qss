﻿/*
这是模仿office2013的白色风格主题
start ribbon set
*/

/*SARibbonBar*/
SARibbonBar {
  background-color: white;
  border: solid #707070;
  color:#b2b2b2;
  /* border-width: 0.02em 0.02em 0em 0.02em; */
}

/*SARibbonButtonGroupWidget*/
SARibbonButtonGroupWidget {
  background-color: transparent;
}


/*SARibbonQuickAccessBar*/
SARibbonQuickAccessBar {
  background-color: #cee8fc;
}

/*SARibbonCtrlContainer*/
SARibbonCtrlContainer {
  background-color: transparent;
}

/*SARibbonCategory*/
SARibbonCategory:focus {
  outline: none;
}

SARibbonCategory {
  background-color: #fcfdfe;
}

/*SARibbonStackedWidget*/
SARibbonStackedWidget {
  background-color: white;
  border: 1pt solid #c5d2e0;
  border-top-width: 0px;
}

SARibbonStackedWidget:focus {
  outline: none;
}

/*SARibbonApplicationButton*/
SARibbonApplicationButton {
  color: white;
  border-top-left-radius: 0.1em;
  border-top-right-radius: 0.1em;
  background-color: #2b579a;
}

SARibbonApplicationButton:hover {
  background-color: #5888d0;
}

SARibbonApplicationButton:pressed {
  background-color: #3369b9;
}

SARibbonApplicationButton:focus {
  outline: none;
}

SARibbonApplicationButton::menu-indicator {
  subcontrol-position: right;
}

/*SARibbonTabBar*/
SARibbonTabBar {
  background-color: transparent;
}


SARibbonTabBar::tab {
  color: #5779af;
  border: none;
  background: transparent;
  margin-top: 0em;
  margin-right: 0em;
  margin-left: 0.2em;
  margin-bottom: 0em;
  min-width: 3em;
  max-width: 10em;
  min-height: 1.2em;
  max-height: 1.2em;
  padding-left: 0.2em;
  padding-right: 0.2em;
  padding-top: 0.1em;
  padding-bottom: 0.2em;
}

SARibbonTabBar::tab:selected, SARibbonTabBar::tab:hover {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

SARibbonTabBar::tab:selected {
  color: #5779af;
  border: 0.05em solid #c5d2e0;
  background: white;
  border-bottom: none;
}

SARibbonTabBar::tab:hover:!selected {
  border:0.05em solid #c5d2e0;
  border-bottom: none;
  color: #5779af;
}

SARibbonTabBar::tab:!selected {
  margin-top: 0px;
}

/*SARibbonToolButton*/
/* 
SARibbonToolButton的背景颜色必须和pannel或者category的颜色一致
不能设置为transparent，否则在ActionMenu模式下，上下区分的按钮无法显示 
*/
SARibbonToolButton {
  border: none;
  color: #333;
  background-color: white;
}

SARibbonToolButton:focus {
  border: 0.05em solid #b9defa;
  color: #333;
  background-color: #fcfdfe;
}

SARibbonToolButton:pressed {
  color: #000;
  border: 0.05em solid #269bf4;
  background-color: #9ed2f9;
}

SARibbonToolButton:checked {
  color: #333;
  border: 0.05em solid #b9defa;
  background-color: #cee8fc;
}

SARibbonToolButton:hover {
  color: #000;
  border: 0.05em solid #badffa;
  background-color: #cee7fc;
}

/*SARibbonControlButton*/

SARibbonControlButton {
  background-color: transparent;
  border: 0.05em solid transparent;
  color: #333;
}

SARibbonControlButton:pressed {
  border: 0.05em solid #269bf4;
  background-color: #9ed2f9;
}

SARibbonControlButton:checked {
  border: 0.05em solid #b9defa;
  background-color: #cee8fc;
}

SARibbonControlButton:hover {
  border: 0.05em solid #badffa;
  background-color: #cee7fc;
}
SARibbonControlButton#SARibbonGalleryButtonUp, #SARibbonGalleryButtonDown, #SARibbonGalleryButtonMore {
  border: 0.05em solid #cee8fc;
}

SARibbonControlButton#SARibbonBarHidePannelButton {
  border: 0.05em solid transparent;
}



/*SARibbonMenu*/

SARibbonMenu {
  color: #333;
  background-color: #FCFCFC;
  border: 0.05em solid #c2d0df;
}

SARibbonMenu::item {
  padding: 5px 5px 5px 5px;
  background-color: transparent;
}

SARibbonMenu::item:selected {
  background-color: #cee8fc;
}

SARibbonMenu::item:hover {
  color: #000;
  background-color: #cee7fc;
  border: 0.05em solid #badffa;
}

SARibbonMenu::icon {
  margin-left: 0.05em;
}

/*SARibbonPannelOptionButton*/

SARibbonPannelOptionButton {
  background-color: transparent;
  color: #333;
}

SARibbonPannelOptionButton:hover {
  background-color: #cee7fc;
  border: 0px;
}

/*SARibbonPannel*/

SARibbonPannel {
  background-color: white;
  border: 0px;
  color: #666666;
}

SARibbonPannel > SARibbonButtonGroupWidget {
  border: 0.05em solid #c2d0df;
}

/*SARibbonGallery*/

SARibbonGallery {
  border: 0.05em solid #c2d0df;
  background-color: transparent;
  color: #333;
}

/*SARibbonGalleryGroup*/

SARibbonGalleryGroup {
  show-decoration-selected: 1;
  background-color: transparent;
  color: #333;
  border: 0.05em solid #c2d0df;
}

SARibbonGalleryGroup::item:selected {
  background-color: #9ed2f9;
  color: black;
}

SARibbonGalleryGroup::item:hover {
  border: 0.1em solid #badffa;
  background-color: #cee7fc;
}

/*RibbonGalleryViewport*/

SARibbonGalleryViewport {
  background-color: #ffffff;
}

/*SARibbonLineEdit*/

SARibbonLineEdit {
  border: 0.05em solid #C0C2C4;
  background: #FFF;
  selection-background-color: #9BBBF7;
  selection-color: #000;
}

/*SARibbonComboBox*/

SARibbonComboBox {
  border: 0.05em solid #c2d0df;
  background-color: white;
}

SARibbonComboBox:hover {
  border: 0.05em solid #269bf4;
  color: #000;
}

SARibbonComboBox:editable {
  color: #000;
  background: white;
  selection-background-color: #9BBBF7;
  selection-color: #000;
}

SARibbonComboBox::drop-down {
  subcontrol-origin: padding;
  subcontrol-position: top right;
  width: 1em;
  border: none;
}

SARibbonComboBox::drop-down:hover {
  border: 0.05em solid #FDEEB3;
  background-color: #9ed2f9;
}

SARibbonComboBox::down-arrow {
  image: url(:/image/resource/ArrowDown.png);
}

/*SARibbonSeparatorWidget*/

SARibbonSeparatorWidget {
  /*background-color: transparent;*/
  background-color: white;
}

SARibbonCategoryScrollButton {
  border: 0.05em solid #c5d2e0;
  color: #333;
  background-color: white;
}

SARibbonCategoryScrollButton[arrowType="3"] {
  border-right-width: 0.05em;
}

SARibbonCategoryScrollButton[arrowType="4"] {
  border-left-width: 0.05em;
}

SARibbonCategoryScrollButton:hover {
  color: #000000;
  background-color: #cee7fc;
}

SAWindowToolButton { 
  background-color: transparent; 
  border:none;
}

SAWindowToolButton:focus {
  outline: none;
}

SAWindowToolButton#SAMinimizeWindowButton {
  image: url(:/image/resource/Titlebar_Min.png);
}

SAWindowToolButton#SAMaximizeWindowButton:checked {
  image:url(:/image/resource/Titlebar_Normal.png);
}

SAWindowToolButton#SAMaximizeWindowButton {
  image:url(:/image/resource/Titlebar_Max.png);
}

SAWindowToolButton#SAMinimizeWindowButton:hover,#SAMaximizeWindowButton:hover {
  background-color: #e5e5e5;
}

SAWindowToolButton#SAMinimizeWindowButton:pressed,#SAMaximizeWindowButton:pressed {
  background-color: #cacacb;
}

SAWindowToolButton#SACloseWindowButton {
  image: url(:/image/resource/Titlebar_Close.png);
}

SAWindowToolButton#SACloseWindowButton:hover {
  background-color: #e81123;
  image: url(:/image/resource/Titlebar_Close_Hover.png);
}

SAWindowToolButton#SACloseWindowButton:pressed {
  background-color: #f1707a;
  image: url(:/image/resource/Titlebar_Close_Hover.png);
}

SAWindowToolButton#SARibbonBarHidePannelButton {
  titlebar-shade-icon: url(:/image/resource/Titlebar_Shade.png);
  titlebar-unshade-icon: url(:/image/resource/Titlebar_Unshade.png);
}
