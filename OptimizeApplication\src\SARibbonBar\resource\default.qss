﻿
/* start ribbon set*/

SARibbonMainWindow {
  background-color: white;
  border: 1px solid #707070;
}

/*SARibbonBar*/

SARibbonBar{
  background-color: #E3E6E8;
  border: solid #707070;
  border-width: 1px 1px 0px 1px;
}

/*SARibbonButtonGroupWidget*/
SARibbonButtonGroupWidget{
background-color: transparent;
}

SARibbonPannel > SARibbonButtonGroupWidget {
  border: 1px solid #c2d0df;
}

/*SARibbonQuickAccessBar*/
SARibbonQuickAccessBar{
    background-color: #FF0000;
}


/*SARibbonCtrlContainer*/
SARibbonCtrlContainer{
    background-color: transparent;
}

/*SARibbonCategory*/
SARibbonCategory:focus{
  outline: none;
}
SARibbonCategory{
  background-color: white;
}

/*SARibbonStackedWidget*/
SARibbonStackedWidget{
  background-color: white;
  border: 1px solid #c5d2e0;
  border-top-width: 0px;
}
SARibbonStackedWidget:focus{
  outline: none;
}
/*SARibbonApplicationButton*/
SARibbonApplicationButton{
  color:white;
  border: 1px solid #416ABD;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #467FBD, stop:0.5 #2A5FAC,stop:0.51 #1A4088,
stop:1 #419ACF);
}

SARibbonApplicationButton::hover{
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #7BB2EB, stop:0.5 #477ECD,stop:0.51 #114ECF,
stop:1 #80E1FF);
}

SARibbonApplicationButton::pressed{
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #467BBB, stop:0.5 #2F63AE,stop:0.51 #1C438A,
stop:1 #358BC9);
}

SARibbonApplicationButton:focus{
  outline: none;
}

/*SARibbonTabBar*/
SARibbonTabBar{
    background-color: transparent;
}

SARibbonTabBar::tab {
    color:#444444;
    border:none;
    background: transparent;
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 6px;
    margin-bottom: 0px;
    min-width:60px;
    max-width:200px;
    min-height:30px;
    max-height:30px;
    padding-left:1px;
    padding-right:1px;
    padding-top:1px;
    padding-bottom:1px;
}

SARibbonTabBar::tab:selected, SARibbonTabBar::tab:hover {
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}

SARibbonTabBar::tab:selected{
    color:#000000;
    border: 1px solid #BAC9DB;
    background: white;
    border-bottom-color: #FFFFFF;
}

SARibbonTabBar::tab:hover:!selected{
    border: 1px solid #ECBC3D;
    color: #000000;
}

SARibbonTabBar::tab:!selected {
    margin-top: 0px;
}

/*SARibbonLineEdit*/
SARibbonLineEdit {
    border: 1px solid #C0C2C4;
    background: #FFF;
    selection-background-color: #9BBBF7;
    selection-color: #000;
}

/*SARibbonToolButton*/
SARibbonToolButton{
    border:1px solid transparent;
    color:#444444;
    background-color:transparent;
}
SARibbonToolButton::pressed{
    color:#444444;
    border: 1px solid #FCBF21;
    background-color: #FCD364;
}
SARibbonToolButton::checked{
    color:#444444;
    border: 1px solid #FCD364;
    background-color: #FCD364;
}
SARibbonToolButton::hover {
    color:#000000;
    border: 1px solid #FCD364;
    background-color: #FCDE89;
}

/*SARibbonControlButton*/
SARibbonControlButton{
  background-color:transparent;
  border: 1px solid transparent;
  color:#444444;
}
SARibbonControlButton#SARibbonGalleryButtonUp,#SARibbonGalleryButtonDown,#SARibbonGalleryButtonMore{
  border: 1px solid #C0C2C4;
}
SARibbonControlButton#SARibbonBarHidePannelButton{
  border: 1px solid transparent;
}
SARibbonControlButton::pressed{
  border: 1px solid #FCD364;
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #FDEEB3, stop:0.1282 #FDE38A,stop:0.8333
#FCE58C, stop:1 #FDFDEB);
}
SARibbonControlButton::checked{
  border: 1px solid #FCD364;
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #FDEEB3, stop:0.1282 #FDE38A,stop:0.8333
#FCE58C, stop:1 #FDFDEB);
}
SARibbonControlButton::hover {
  border: 1px solid #FCD364;
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #FDEEB3, stop:0.1282 #FDE38A,stop:0.8333
#FCE58C, stop:1 #FDFDEB);
 }


/*SARibbonMenu*/
SARibbonMenu {
  color:#444444;
  background-color: #FCFCFC;
  border: 1px solid #8492A6;
}
SARibbonMenu::item {
  padding: 5px 25px 5px 25px;
  background-color: transparent;
}
SARibbonMenu::item:selected {
  background-color: #FCDE89;
}
SARibbonMenu::item:hover {
    color:#000;
    border: 1px solid #FCD364;
}
SARibbonMenu::icon{
  margin-left: 1px;
}

/*SARibbonPannelOptionButton*/
SARibbonPannelOptionButton {
  background-color: transparent;
  color:#444444;
}

SARibbonPannelOptionButton::hover {
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #FDEEB3, stop:0.1282 #FDE38A,stop:0.8333
#FCE58C, stop:1 #FDFDEB);
  border: 0px;
}


SARibbonPannel {
  background-color: #FFFFFF;
  border: 0px;
}
/*SARibbonPannel*/
SARibbonPannel {
  background-color: #FFFFFF;
  border: 0px;
}
/*SARibbonGallery*/
SARibbonGallery {
  background-color: transparent;
  color: #444444;
}

/*SARibbonGalleryGroup*/
SARibbonGalleryGroup {
  show-decoration-selected: 1;
  background-color: transparent;
  color: #444444;
  border: 1px solid #C0C2C4;
}
SARibbonGalleryGroup::item:selected {
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #FDEEB3, stop:0.1282 #FDE38A,stop:0.8333
#FCE58C, stop:1 #FDFDEB);
  color: black;
}
SARibbonGalleryGroup::item:hover {
  border: 2px solid #FDEEB3;
}

/*RibbonGalleryViewport*/

RibbonGalleryViewport {
  background-color: white;
}

/*SARibbonComboBox*/
SARibbonComboBox {
  border: 1px solid #C0C2C4;

}

SARibbonComboBox:hover{
  border: 1px solid #FDEEB3;
  color : #000;
}

SARibbonComboBox:editable {
  color : #000;
  background: white;
  selection-background-color: #9BBBF7;
  selection-color: #000;
}

SARibbonComboBox::drop-down {
  subcontrol-origin: padding;
  subcontrol-position: top right;
  width: 15px;
  border-left: none;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

SARibbonComboBox::drop-down:hover {
  border: 1px solid #FDEEB3;
  background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1,stop:0 #FDEEB3, stop:0.1282 #FDE38A,stop:0.8333
#FCE58C, stop:1 #FDFDEB);
}
SARibbonComboBox::down-arrow {
  image: url(:/image/resource/ArrowDown.png);
}


/*SARibbonSeparatorWidget*/
SARibbonSeparatorWidget{
    /*background-color: transparent;*/
    background-color: #D3D3D3;
}

SARibbonCategoryScrollButton {
  border: 0px solid #c5d2e0;
  color: #333;
  background-color: white;
}

SARibbonCategoryScrollButton[arrowType="3"] {
  border-right-width: 1px;
}

SARibbonCategoryScrollButton[arrowType="4"] {
  border-left-width: 1px;
}

SARibbonCategoryScrollButton::hover {
  color: #000000;
  background-color: #cee7fc;
}

SAWindowToolButton { 
  background-color: transparent; 
  border:none;
}

SAWindowToolButton:focus {
  outline: none;
}

#SAMinimizeWindowButton {
  image: url(:/image/resource/Titlebar_Min.png);
}

#SAMaximizeWindowButton:checked {
  image:url(:/image/resource/Titlebar_Normal.png);
}

#SAMaximizeWindowButton {
  image:url(:/image/resource/Titlebar_Max.png);
}

#SAMinimizeWindowButton:hover,#SAMaximizeWindowButton:hover {
  background-color: #e5e5e5;
}

#SAMinimizeWindowButton:pressed,#SAMaximizeWindowButton:pressed {
  background-color: #cacacb;
}

#SACloseWindowButton {
  image: url(:/image/resource/Titlebar_Close.png);
}

#SACloseWindowButton:hover {
  background-color: #e81123;
  image: url(:/image/resource/Titlebar_Close_Hover.png);
}

#SACloseWindowButton:pressed {
  background-color: #f1707a;
  image: url(:/image/resource/Titlebar_Close_Hover.png);
}

#SARibbonBarHidePannelButton {
  titlebar-shade-icon: url(:/image/resource/Titlebar_Shade.png);
  titlebar-unshade-icon: url(:/image/resource/Titlebar_Unshade.png);
}
