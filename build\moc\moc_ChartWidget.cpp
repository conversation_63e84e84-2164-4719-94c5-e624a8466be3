/****************************************************************************
** Meta object code from reading C++ file 'ChartWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/widgets/ChartWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ChartWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ChartWidget_t {
    QByteArrayData data[21];
    char stringdata0[243];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ChartWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ChartWidget_t qt_meta_stringdata_ChartWidget = {
    {
QT_MOC_LITERAL(0, 0, 11), // "ChartWidget"
QT_MOC_LITERAL(1, 12, 9), // "saveChart"
QT_MOC_LITERAL(2, 22, 0), // ""
QT_MOC_LITERAL(3, 23, 8), // "filePath"
QT_MOC_LITERAL(4, 32, 10), // "printChart"
QT_MOC_LITERAL(5, 43, 8), // "setTheme"
QT_MOC_LITERAL(6, 52, 18), // "QChart::ChartTheme"
QT_MOC_LITERAL(7, 71, 5), // "theme"
QT_MOC_LITERAL(8, 77, 10), // "zoomToData"
QT_MOC_LITERAL(9, 88, 9), // "resetZoom"
QT_MOC_LITERAL(10, 98, 18), // "onChartTypeChanged"
QT_MOC_LITERAL(11, 117, 5), // "index"
QT_MOC_LITERAL(12, 123, 14), // "onThemeChanged"
QT_MOC_LITERAL(13, 138, 13), // "onExportChart"
QT_MOC_LITERAL(14, 152, 11), // "onAddSeries"
QT_MOC_LITERAL(15, 164, 12), // "onEditSeries"
QT_MOC_LITERAL(16, 177, 14), // "onRemoveSeries"
QT_MOC_LITERAL(17, 192, 15), // "onLegendClicked"
QT_MOC_LITERAL(18, 208, 17), // "onPieSliceClicked"
QT_MOC_LITERAL(19, 226, 10), // "QPieSlice*"
QT_MOC_LITERAL(20, 237, 5) // "slice"

    },
    "ChartWidget\0saveChart\0\0filePath\0"
    "printChart\0setTheme\0QChart::ChartTheme\0"
    "theme\0zoomToData\0resetZoom\0"
    "onChartTypeChanged\0index\0onThemeChanged\0"
    "onExportChart\0onAddSeries\0onEditSeries\0"
    "onRemoveSeries\0onLegendClicked\0"
    "onPieSliceClicked\0QPieSlice*\0slice"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ChartWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   84,    2, 0x0a /* Public */,
       1,    0,   87,    2, 0x2a /* Public | MethodCloned */,
       4,    0,   88,    2, 0x0a /* Public */,
       5,    1,   89,    2, 0x0a /* Public */,
       8,    0,   92,    2, 0x0a /* Public */,
       9,    0,   93,    2, 0x0a /* Public */,
      10,    1,   94,    2, 0x08 /* Private */,
      12,    1,   97,    2, 0x08 /* Private */,
      13,    0,  100,    2, 0x08 /* Private */,
      14,    0,  101,    2, 0x08 /* Private */,
      15,    0,  102,    2, 0x08 /* Private */,
      16,    0,  103,    2, 0x08 /* Private */,
      17,    0,  104,    2, 0x08 /* Private */,
      18,    1,  105,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void, QMetaType::Int,   11,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 19,   20,

       0        // eod
};

void ChartWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ChartWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->saveChart((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->saveChart(); break;
        case 2: _t->printChart(); break;
        case 3: _t->setTheme((*reinterpret_cast< QChart::ChartTheme(*)>(_a[1]))); break;
        case 4: _t->zoomToData(); break;
        case 5: _t->resetZoom(); break;
        case 6: _t->onChartTypeChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 7: _t->onThemeChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 8: _t->onExportChart(); break;
        case 9: _t->onAddSeries(); break;
        case 10: _t->onEditSeries(); break;
        case 11: _t->onRemoveSeries(); break;
        case 12: _t->onLegendClicked(); break;
        case 13: _t->onPieSliceClicked((*reinterpret_cast< QPieSlice*(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 13:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QPieSlice* >(); break;
            }
            break;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ChartWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_ChartWidget.data,
    qt_meta_data_ChartWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ChartWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ChartWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ChartWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ChartWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
