#ifndef PARAMETERWIDGETBASE_H
#define PARAMETERWIDGETBASE_H

#include "IWidget.h"
#include <QWidget>
#include <QVBoxLayout>
#include <QVariantMap>

/**
 * @brief 参数控件基类
 * 
 * 所有参数控件都应该继承此基类，以提供统一的参数管理功能。
 */
class ParameterWidgetBase : public QWidget, public IWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父控件
     */
    explicit ParameterWidgetBase(QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    virtual ~ParameterWidgetBase();

    /**
     * @brief 获取参数
     * @return 参数映射
     */
    virtual QVariantMap getParameters() const = 0;

    /**
     * @brief 设置参数
     * @param params 参数映射
     */
    virtual void setParameters(const QVariantMap& params) = 0;

    /**
     * @brief 清空参数
     */
    virtual void clearParameters();

    /**
     * @brief 重置参数为默认值
     */
    virtual void resetParameters();

    /**
     * @brief 验证参数
     * @return 参数是否有效
     */
    virtual bool validateParameters() const;

    /**
     * @brief 获取参数描述
     * @return 参数描述映射
     */
    virtual QVariantMap getParameterDescriptions() const;

    /**
     * @brief 获取参数默认值
     * @return 参数默认值映射
     */
    virtual QVariantMap getDefaultParameters() const;

    /**
     * @brief 获取参数范围
     * @return 参数范围映射
     */
    virtual QVariantMap getParameterRanges() const;

    /**
     * @brief 获取参数单位
     * @return 参数单位映射
     */
    virtual QVariantMap getParameterUnits() const;

    // IWidget接口实现
    void saveState(QSettings& settings) override;
    void restoreState(const QSettings& settings) override;

signals:
    /**
     * @brief 参数变更信号
     */
    void parametersChanged();

    /**
     * @brief 参数验证信号
     * @param isValid 参数是否有效
     */
    void parametersValidated(bool isValid);

protected:
    QVBoxLayout* m_mainLayout;
    QVariantMap m_defaultParameters;
    QVariantMap m_parameterRanges;
    QVariantMap m_parameterUnits;
    QVariantMap m_parameterDescriptions;
};

#endif // PARAMETERWIDGETBASE_H
