<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PipeWidget</class>
 <widget class="QWidget" name="PipeWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Pipe Component Editor</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <item>
    <widget class="QGroupBox" name="basicGroup">
     <property name="title">
      <string>Basic Information</string>
     </property>
     <layout class="QFormLayout" name="basicLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="componentIdLabel">
        <property name="text">
         <string>Component ID:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="componentIdEdit">
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="componentNameLabel">
        <property name="text">
         <string>Component Name:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="componentNameEdit"/>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="numberOfVolumesLabel">
        <property name="text">
         <string>Number of Volumes:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QSpinBox" name="numberOfVolumesSpin">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>1000</number>
        </property>
        <property name="value">
         <number>10</number>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="componentTypeLabel">
        <property name="text">
         <string>Component Type:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QComboBox" name="componentTypeCombo">
        <item>
         <property name="text">
          <string>pipe</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>annulus</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="volumeGroup">
     <property name="title">
      <string>Volume Data</string>
     </property>
     <layout class="QVBoxLayout" name="volumeLayout">
      <item>
       <widget class="QTableWidget" name="volumeTable">
        <property name="alternatingRowColors">
         <bool>true</bool>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
        <property name="sortingEnabled">
         <bool>false</bool>
        </property>
        <column>
         <property name="text">
          <string>Volume (m³)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Length (m)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Elevation (m)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Roughness</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Hydraulic Diameter (m)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Angle (deg)</string>
         </property>
        </column>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="volumeButtonLayout">
        <item>
         <widget class="QPushButton" name="addVolumeBtn">
          <property name="text">
           <string>Add Volume</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="removeVolumeBtn">
          <property name="text">
           <string>Remove Volume</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="volumeButtonSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="controlFlagsGroup">
     <property name="title">
      <string>Control Flags</string>
     </property>
     <layout class="QGridLayout" name="controlFlagsLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="volumeControlLabel">
        <property name="text">
         <string>Volume Control Flags (tlpvbfe):</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="volumeControlEdit">
        <property name="text">
         <string>00</string>
        </property>
        <property name="maxLength">
         <number>7</number>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="junctionControlLabel">
        <property name="text">
         <string>Junction Control Flags (jefvcahs):</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="junctionControlEdit">
        <property name="text">
         <string>00000000</string>
        </property>
        <property name="maxLength">
         <number>8</number>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="initialConditionFlagLabel">
        <property name="text">
         <string>Initial Condition Flag (εbt):</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QSpinBox" name="initialConditionFlagSpin">
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>6</number>
        </property>
        <property name="value">
         <number>3</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="initialConditionGroup">
     <property name="title">
      <string>Initial Conditions</string>
     </property>
     <layout class="QVBoxLayout" name="initialConditionLayout">
      <item>
       <widget class="QTableWidget" name="initialConditionTable">
        <property name="alternatingRowColors">
         <bool>true</bool>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
        <property name="sortingEnabled">
         <bool>false</bool>
        </property>
        <column>
         <property name="text">
          <string>Thermo State</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Pressure (Pa)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Temperature (K)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Quality</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Velocity (m/s)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Boron Density</string>
         </property>
        </column>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="operationsGroup">
     <property name="title">
      <string>Operations</string>
     </property>
     <layout class="QHBoxLayout" name="operationsLayout">
      <item>
       <widget class="QPushButton" name="validateBtn">
        <property name="text">
         <string>Validate Data</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="generateCardsBtn">
        <property name="text">
         <string>Generate RELAP5 Cards</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="exportCSVBtn">
        <property name="text">
         <string>Export CSV</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="importCSVBtn">
        <property name="text">
         <string>Import CSV</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="operationsHorizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 