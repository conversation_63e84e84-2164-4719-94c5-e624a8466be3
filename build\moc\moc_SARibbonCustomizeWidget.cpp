/****************************************************************************
** Meta object code from reading C++ file 'SARibbonCustomizeWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/SARibbonBar/SARibbonCustomizeWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'SARibbonCustomizeWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SARibbonCustomizeWidget_t {
    QByteArrayData data[23];
    char stringdata0[461];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SARibbonCustomizeWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SARibbonCustomizeWidget_t qt_meta_stringdata_SARibbonCustomizeWidget = {
    {
QT_MOC_LITERAL(0, 0, 23), // "SARibbonCustomizeWidget"
QT_MOC_LITERAL(1, 24, 40), // "onComboBoxActionIndexCurrentI..."
QT_MOC_LITERAL(2, 65, 0), // ""
QT_MOC_LITERAL(3, 66, 5), // "index"
QT_MOC_LITERAL(4, 72, 31), // "onRadioButtonGroupButtonClicked"
QT_MOC_LITERAL(5, 104, 16), // "QAbstractButton*"
QT_MOC_LITERAL(6, 121, 1), // "b"
QT_MOC_LITERAL(7, 123, 30), // "onPushButtonNewCategoryClicked"
QT_MOC_LITERAL(8, 154, 28), // "onPushButtonNewPannelClicked"
QT_MOC_LITERAL(9, 183, 25), // "onPushButtonRenameClicked"
QT_MOC_LITERAL(10, 209, 22), // "onPushButtonAddClicked"
QT_MOC_LITERAL(11, 232, 25), // "onPushButtonDeleteClicked"
QT_MOC_LITERAL(12, 258, 23), // "onListViewSelectClicked"
QT_MOC_LITERAL(13, 282, 11), // "QModelIndex"
QT_MOC_LITERAL(14, 294, 23), // "onTreeViewResultClicked"
QT_MOC_LITERAL(15, 318, 21), // "onToolButtonUpClicked"
QT_MOC_LITERAL(16, 340, 23), // "onToolButtonDownClicked"
QT_MOC_LITERAL(17, 364, 13), // "onItemChanged"
QT_MOC_LITERAL(18, 378, 14), // "QStandardItem*"
QT_MOC_LITERAL(19, 393, 4), // "item"
QT_MOC_LITERAL(20, 398, 32), // "onLineEditSearchActionTextEdited"
QT_MOC_LITERAL(21, 431, 4), // "text"
QT_MOC_LITERAL(22, 436, 24) // "onPushButtonResetClicked"

    },
    "SARibbonCustomizeWidget\0"
    "onComboBoxActionIndexCurrentIndexChanged\0"
    "\0index\0onRadioButtonGroupButtonClicked\0"
    "QAbstractButton*\0b\0onPushButtonNewCategoryClicked\0"
    "onPushButtonNewPannelClicked\0"
    "onPushButtonRenameClicked\0"
    "onPushButtonAddClicked\0onPushButtonDeleteClicked\0"
    "onListViewSelectClicked\0QModelIndex\0"
    "onTreeViewResultClicked\0onToolButtonUpClicked\0"
    "onToolButtonDownClicked\0onItemChanged\0"
    "QStandardItem*\0item\0"
    "onLineEditSearchActionTextEdited\0text\0"
    "onPushButtonResetClicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SARibbonCustomizeWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   84,    2, 0x08 /* Private */,
       4,    1,   87,    2, 0x08 /* Private */,
       7,    0,   90,    2, 0x08 /* Private */,
       8,    0,   91,    2, 0x08 /* Private */,
       9,    0,   92,    2, 0x08 /* Private */,
      10,    0,   93,    2, 0x08 /* Private */,
      11,    0,   94,    2, 0x08 /* Private */,
      12,    1,   95,    2, 0x08 /* Private */,
      14,    1,   98,    2, 0x08 /* Private */,
      15,    0,  101,    2, 0x08 /* Private */,
      16,    0,  102,    2, 0x08 /* Private */,
      17,    1,  103,    2, 0x08 /* Private */,
      20,    1,  106,    2, 0x08 /* Private */,
      22,    0,  109,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, 0x80000000 | 5,    6,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 13,    3,
    QMetaType::Void, 0x80000000 | 13,    3,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 18,   19,
    QMetaType::Void, QMetaType::QString,   21,
    QMetaType::Void,

       0        // eod
};

void SARibbonCustomizeWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SARibbonCustomizeWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onComboBoxActionIndexCurrentIndexChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 1: _t->onRadioButtonGroupButtonClicked((*reinterpret_cast< QAbstractButton*(*)>(_a[1]))); break;
        case 2: _t->onPushButtonNewCategoryClicked(); break;
        case 3: _t->onPushButtonNewPannelClicked(); break;
        case 4: _t->onPushButtonRenameClicked(); break;
        case 5: _t->onPushButtonAddClicked(); break;
        case 6: _t->onPushButtonDeleteClicked(); break;
        case 7: _t->onListViewSelectClicked((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        case 8: _t->onTreeViewResultClicked((*reinterpret_cast< const QModelIndex(*)>(_a[1]))); break;
        case 9: _t->onToolButtonUpClicked(); break;
        case 10: _t->onToolButtonDownClicked(); break;
        case 11: _t->onItemChanged((*reinterpret_cast< QStandardItem*(*)>(_a[1]))); break;
        case 12: _t->onLineEditSearchActionTextEdited((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 13: _t->onPushButtonResetClicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 1:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAbstractButton* >(); break;
            }
            break;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject SARibbonCustomizeWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_SARibbonCustomizeWidget.data,
    qt_meta_data_SARibbonCustomizeWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *SARibbonCustomizeWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SARibbonCustomizeWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SARibbonCustomizeWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int SARibbonCustomizeWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
