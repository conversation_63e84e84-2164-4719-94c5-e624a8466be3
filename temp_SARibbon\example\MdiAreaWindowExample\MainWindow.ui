<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="documentMode">
   <bool>false</bool>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QMdiArea" name="mdiArea"/>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <property name="allowedAreas">
    <set>Qt::LeftToolBarArea|Qt::RightToolBarArea</set>
   </property>
   <property name="orientation">
    <enum>Qt::Vertical</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>LeftToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionadd_window"/>
  </widget>
  <widget class="QDockWidget" name="dockWidget">
   <attribute name="dockWidgetArea">
    <number>2</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents"/>
  </widget>
  <action name="actionadd_window">
   <property name="icon">
    <iconset resource="icon.qrc">
     <normaloff>:/mdiWindowApp/icon/add.svg</normaloff>:/mdiWindowApp/icon/add.svg</iconset>
   </property>
   <property name="text">
    <string>add window</string>
   </property>
  </action>
 </widget>
 <resources>
  <include location="icon.qrc"/>
 </resources>
 <connections/>
</ui>
