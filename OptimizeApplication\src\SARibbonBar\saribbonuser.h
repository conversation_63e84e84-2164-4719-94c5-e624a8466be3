#ifndef SARIBBONUSER_H
#define SARIBBONUSER_H

#include <QWidget>
#include "SARibbonGlobal.h"

class SARibbonUserPrivate;

class SARibbonUser : public QWidget
{
    Q_OBJECT
public:
    SARibbonUser(QWidget *parent = nullptr);
    ~SARibbonUser();

signals:
    void showWindow();
    void hideWindow();
    void hideEvent(QHideEvent *e) override;
    void showEvent(QShowEvent *event) override;

private:
    SARibbonUserPrivate *m_d;
};

#endif // SARIBBONUSER_H
