#include "ApplicationIntegrationTest.h"
#include "../../src/core/application/ServiceLocator.h"
#include "../../src/core/application/EventBus.h"
#include "../../src/core/application/PluginManager.h"
#include "../../src/core/config/ConfigManager.h"
#include "../../src/core/theme/ThemeManager.h"
#include "../../src/utils/logging/LogManager.h"
#include "../../src/data/models/OptimizationParameters.h"
#include "../../src/data/repositories/FileRepository.h"
#include "../../src/utils/common/ErrorHandler.h"
#include "../../src/utils/common/PerformanceProfiler.h"

#include <QSignalSpy>
#include <QThread>
#include <QFile>
#include <QElapsedTimer>
#include <QCoreApplication>
#include <QJsonDocument>
#include <QJsonObject>
#include <QtConcurrent>

void ApplicationIntegrationTest::initTestCase()
{
    // 创建临时目录
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    
    // 设置环境变量
    qputenv("OPTIMIZE_APP_CONFIG_DIR", m_tempDir->path().toUtf8());
    qputenv("OPTIMIZE_APP_DATA_DIR", m_tempDir->path().toUtf8());
    qputenv("OPTIMIZE_APP_LOG_DIR", m_tempDir->path().toUtf8());
    
    // 创建ApplicationCore实例
    m_appCore = ApplicationCore::instance();
    QVERIFY(m_appCore != nullptr);
    
    // 创建事件接收器
    m_eventReceiver = new TestEventReceiver();
}

void ApplicationIntegrationTest::cleanupTestCase()
{
    // 清理资源
    m_appCore->cleanup();
    delete m_eventReceiver;
    delete m_tempDir;
}

void ApplicationIntegrationTest::init()
{
    // 初始化应用程序核心
    QVERIFY(m_appCore->initialize());
}

void ApplicationIntegrationTest::cleanup()
{
    // 清理应用程序核心
    m_appCore->cleanup();
}

void ApplicationIntegrationTest::testApplicationInitialization()
{
    // 验证应用程序核心已初始化
    QVERIFY(m_appCore->isInitialized());
    
    // 验证核心服务已创建
    QVERIFY(m_appCore->configManager() != nullptr);
    QVERIFY(m_appCore->themeManager() != nullptr);
    QVERIFY(m_appCore->logManager() != nullptr);
    QVERIFY(m_appCore->eventBus() != nullptr);
    QVERIFY(m_appCore->pluginManager() != nullptr);
    
    // 验证应用程序信息
    QVERIFY(!m_appCore->applicationName().isEmpty());
    QVERIFY(!m_appCore->version().isEmpty());
}

void ApplicationIntegrationTest::testServiceLocator()
{
    // 获取服务定位器
    ServiceLocator* serviceLocator = m_appCore->serviceLocator();
    QVERIFY(serviceLocator != nullptr);
    
    // 验证服务注册
    QVERIFY(serviceLocator->hasService<ConfigManager>());
    QVERIFY(serviceLocator->hasService<ThemeManager>());
    QVERIFY(serviceLocator->hasService<LogManager>());
    QVERIFY(serviceLocator->hasService<EventBus>());
    QVERIFY(serviceLocator->hasService<PluginManager>());
    
    // 验证服务获取
    QVERIFY(serviceLocator->getService<ConfigManager>() != nullptr);
    QVERIFY(serviceLocator->getService<ThemeManager>() != nullptr);
    QVERIFY(serviceLocator->getService<LogManager>() != nullptr);
    QVERIFY(serviceLocator->getService<EventBus>() != nullptr);
    QVERIFY(serviceLocator->getService<PluginManager>() != nullptr);
    
    // 验证服务一致性
    QCOMPARE(serviceLocator->getService<ConfigManager>(), m_appCore->configManager());
    QCOMPARE(serviceLocator->getService<ThemeManager>(), m_appCore->themeManager());
    QCOMPARE(serviceLocator->getService<LogManager>(), m_appCore->logManager());
    QCOMPARE(serviceLocator->getService<EventBus>(), m_appCore->eventBus());
    QCOMPARE(serviceLocator->getService<PluginManager>(), m_appCore->pluginManager());
}

void ApplicationIntegrationTest::testConfigurationIntegration()
{
    // 获取配置管理器
    ConfigManager* configManager = m_appCore->configManager();
    QVERIFY(configManager != nullptr);
    
    // 设置配置
    configManager->setValue("test_key", "test_value");
    
    // 验证配置
    QCOMPARE(configManager->getValue("test_key").toString(), QString("test_value"));
    
    // 保存配置
    QVERIFY(configManager->save());
    
    // 清空配置
    configManager->clear();
    QVERIFY(!configManager->containsKey("test_key"));
    
    // 加载配置
    QVERIFY(configManager->load());
    
    // 验证加载的配置
    QCOMPARE(configManager->getValue("test_key").toString(), QString("test_value"));
}

void ApplicationIntegrationTest::testThemeManagementIntegration()
{
    // 获取主题管理器和配置管理器
    ThemeManager* themeManager = m_appCore->themeManager();
    ConfigManager* configManager = m_appCore->configManager();
    QVERIFY(themeManager != nullptr);
    QVERIFY(configManager != nullptr);
    
    // 监听主题变更信号
    QSignalSpy spy(themeManager, SIGNAL(themeChanged(const QString&)));
    
    // 设置主题
    QVERIFY(themeManager->setTheme("dark"));
    
    // 验证信号
    QCOMPARE(spy.count(), 1);
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), QString("dark"));
    
    // 验证当前主题
    QCOMPARE(themeManager->currentTheme(), QString("dark"));
    QCOMPARE(themeManager->currentThemeType(), ThemeType::Dark);
    
    // 验证配置已更新
    QCOMPARE(configManager->getValue("theme").toString(), QString("dark"));
    
    // 设置主题类型
    QVERIFY(themeManager->setThemeType(ThemeType::Light));
    
    // 验证信号
    QCOMPARE(spy.count(), 1);
    arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), QString("light"));
    
    // 验证当前主题
    QCOMPARE(themeManager->currentTheme(), QString("light"));
    QCOMPARE(themeManager->currentThemeType(), ThemeType::Light);
    
    // 验证配置已更新
    QCOMPARE(configManager->getValue("theme").toString(), QString("light"));
}

void ApplicationIntegrationTest::testLogManagementIntegration()
{
    // 获取日志管理器
    LogManager* logManager = m_appCore->logManager();
    QVERIFY(logManager != nullptr);
    
    // 设置日志级别
    logManager->setLogLevel(LogLevel::Debug);
    QCOMPARE(logManager->logLevel(), LogLevel::Debug);
    
    // 记录日志
    logManager->logInfo("Test info message");
    logManager->logWarning("Test warning message");
    logManager->logError("Test error message");
    
    // 验证日志文件存在
    QString logFilePath = logManager->getLogFilePath();
    QVERIFY(!logFilePath.isEmpty());
    QVERIFY(QFile::exists(logFilePath));
    
    // 读取日志文件内容
    QFile logFile(logFilePath);
    QVERIFY(logFile.open(QIODevice::ReadOnly | QIODevice::Text));
    QString logContent = QString::fromUtf8(logFile.readAll());
    logFile.close();
    
    // 验证日志内容
    QVERIFY(logContent.contains("Test info message"));
    QVERIFY(logContent.contains("Test warning message"));
    QVERIFY(logContent.contains("Test error message"));
}

void ApplicationIntegrationTest::testEventSystemIntegration()
{
    // 获取事件总线
    EventBus* eventBus = m_appCore->eventBus();
    QVERIFY(eventBus != nullptr);
    
    // 订阅事件
    int handlerId = eventBus->subscribe<TestEvent>(m_eventReceiver, &TestEventReceiver::handleTestEvent);
    QVERIFY(handlerId > 0);
    
    // 发布事件
    TestEvent event1("Test message 1", 42);
    eventBus->publish(event1);
    
    TestEvent event2("Test message 2", 84);
    eventBus->publish(event2);
    
    // 验证事件接收
    QCOMPARE(m_eventReceiver->receivedMessages.size(), 2);
    QCOMPARE(m_eventReceiver->receivedValues.size(), 2);
    
    QCOMPARE(m_eventReceiver->receivedMessages[0], QString("Test message 1"));
    QCOMPARE(m_eventReceiver->receivedValues[0], 42);
    
    QCOMPARE(m_eventReceiver->receivedMessages[1], QString("Test message 2"));
    QCOMPARE(m_eventReceiver->receivedValues[1], 84);
    
    // 取消订阅
    QVERIFY(eventBus->unsubscribe(handlerId));
    
    // 清空接收记录
    m_eventReceiver->receivedMessages.clear();
    m_eventReceiver->receivedValues.clear();
    
    // 再次发布事件
    TestEvent event3("Test message 3", 126);
    eventBus->publish(event3);
    
    // 验证事件未接收
    QVERIFY(m_eventReceiver->receivedMessages.isEmpty());
    QVERIFY(m_eventReceiver->receivedValues.isEmpty());
}

void ApplicationIntegrationTest::testPluginSystemIntegration()
{
    // 获取插件管理器
    PluginManager* pluginManager = m_appCore->pluginManager();
    QVERIFY(pluginManager != nullptr);
    
    // 验证插件目录扫描
    int loadedCount = pluginManager->scanPluginDirectory(m_tempDir->path());
    QCOMPARE(loadedCount, 0); // 没有插件文件
    
    // 验证插件列表
    QList<IPlugin*> plugins = pluginManager->getPlugins();
    QVERIFY(plugins.isEmpty());
}

void ApplicationIntegrationTest::testErrorHandlingIntegration()
{
    // 设置错误回调
    bool errorCallbackCalled = false;
    QString errorMessage;
    ErrorSeverity errorSeverity;
    
    ErrorHandler::setErrorCallback([&](const ErrorHandler::ErrorInfo& errorInfo) {
        errorCallbackCalled = true;
        errorMessage = errorInfo.message;
        errorSeverity = errorInfo.severity;
    });
    
    // 处理错误
    ErrorHandler::handleError("Test error", ErrorSeverity::Error, "TestSource", 123);
    
    // 验证错误回调
    QVERIFY(errorCallbackCalled);
    QCOMPARE(errorMessage, QString("Test error"));
    QCOMPARE(errorSeverity, ErrorSeverity::Error);
    
    // 处理异常
    try {
        throw std::runtime_error("Test exception");
    } catch (const std::exception& e) {
        ErrorHandler::handleException(e, "TestSource");
    }
    
    // 验证异常处理
    QVERIFY(errorCallbackCalled);
    QVERIFY(errorMessage.contains("Test exception"));
}

void ApplicationIntegrationTest::testDataModelIntegration()
{
    // 创建优化参数模型
    OptimizationParameters params;
    
    // 设置参数
    params.setMethod(OptimizationParameters::Method::GradientDescent);
    params.setMethodName("Gradient Descent");
    params.setObjective("minimize(x^2 + y^2)");
    params.setConstraints(QStringList() << "x + y <= 10" << "x >= 0" << "y >= 0");
    params.setVariableRange("x", 0.0, 10.0);
    params.setVariableRange("y", 0.0, 10.0);
    params.setMaxIterations(1000);
    params.setTolerance(1e-6);
    
    // 验证参数
    QCOMPARE(params.method(), OptimizationParameters::Method::GradientDescent);
    QCOMPARE(params.methodName(), QString("Gradient Descent"));
    QCOMPARE(params.objective(), QString("minimize(x^2 + y^2)"));
    QCOMPARE(params.constraints().size(), 3);
    QCOMPARE(params.getVariableRange("x").first, 0.0);
    QCOMPARE(params.getVariableRange("x").second, 10.0);
    QCOMPARE(params.maxIterations(), 1000);
    QCOMPARE(params.tolerance(), 1e-6);
    
    // 验证模型有效性
    QVERIFY(params.isValid());
    
    // 序列化和反序列化
    QVariant variant = params.toVariant();
    
    OptimizationParameters newParams;
    newParams.fromVariant(variant);
    
    // 验证反序列化结果
    QCOMPARE(newParams.method(), OptimizationParameters::Method::GradientDescent);
    QCOMPARE(newParams.methodName(), QString("Gradient Descent"));
    QCOMPARE(newParams.objective(), QString("minimize(x^2 + y^2)"));
    QCOMPARE(newParams.constraints().size(), 3);
    QCOMPARE(newParams.getVariableRange("x").first, 0.0);
    QCOMPARE(newParams.getVariableRange("x").second, 10.0);
    QCOMPARE(newParams.maxIterations(), 1000);
    QCOMPARE(newParams.tolerance(), 1e-6);
}

void ApplicationIntegrationTest::testRepositoryIntegration()
{
    // 创建文件仓库
    QString repoPath = m_tempDir->path() + "/test_repository.json";
    FileRepository<OptimizationParameters> repository(repoPath);
    
    // 创建测试数据
    OptimizationParameters params1;
    params1.setId("param1");
    params1.setMethodName("Method 1");
    params1.setObjective("Objective 1");
    
    OptimizationParameters params2;
    params2.setId("param2");
    params2.setMethodName("Method 2");
    params2.setObjective("Objective 2");
    
    // 添加数据
    QVERIFY(repository.add(params1));
    QVERIFY(repository.add(params2));
    
    // 验证数据
    QVERIFY(repository.exists("param1"));
    QVERIFY(repository.exists("param2"));
    QCOMPARE(repository.count(), 2);
    
    // 获取数据
    OptimizationParameters retrievedParams1 = repository.get("param1");
    QCOMPARE(retrievedParams1.id(), QString("param1"));
    QCOMPARE(retrievedParams1.methodName(), QString("Method 1"));
    QCOMPARE(retrievedParams1.objective(), QString("Objective 1"));
    
    // 更新数据
    params1.setMethodName("Updated Method 1");
    QVERIFY(repository.update(params1));
    
    // 验证更新
    retrievedParams1 = repository.get("param1");
    QCOMPARE(retrievedParams1.methodName(), QString("Updated Method 1"));
    
    // 保存仓库
    QVERIFY(repository.save());
    
    // 创建新仓库并加载
    FileRepository<OptimizationParameters> newRepository(repoPath);
    QVERIFY(newRepository.load());
    
    // 验证加载的数据
    QCOMPARE(newRepository.count(), 2);
    QVERIFY(newRepository.exists("param1"));
    QVERIFY(newRepository.exists("param2"));
    
    OptimizationParameters loadedParams1 = newRepository.get("param1");
    QCOMPARE(loadedParams1.methodName(), QString("Updated Method 1"));
    
    // 移除数据
    QVERIFY(newRepository.remove("param1"));
    QVERIFY(!newRepository.exists("param1"));
    QCOMPARE(newRepository.count(), 1);
    
    // 清空仓库
    newRepository.clear();
    QCOMPARE(newRepository.count(), 0);
}

void ApplicationIntegrationTest::testCompleteWorkflow()
{
    // 创建完整的工作流程测试
    
    // 1. 配置应用程序
    ConfigManager* configManager = m_appCore->configManager();
    configManager->setValue("theme", "dark");
    configManager->setValue("language", "en");
    configManager->save();
    
    // 2. 设置主题
    ThemeManager* themeManager = m_appCore->themeManager();
    themeManager->setTheme(configManager->getValue("theme").toString());
    
    // 3. 创建优化参数
    OptimizationParameters params;
    params.setMethodName("Gradient Descent");
    params.setObjective("minimize(x^2 + y^2)");
    params.setConstraints(QStringList() << "x + y <= 10");
    params.setMaxIterations(1000);
    
    // 4. 保存到仓库
    QString repoPath = m_tempDir->path() + "/workflow_repository.json";
    FileRepository<OptimizationParameters> repository(repoPath);
    repository.add(params);
    repository.save();
    
    // 5. 发布事件
    EventBus* eventBus = m_appCore->eventBus();
    TestEvent event("Optimization completed", 100);
    eventBus->publish(event);
    
    // 6. 记录日志
    LogManager* logManager = m_appCore->logManager();
    logManager->logInfo("Workflow completed successfully");
    
    // 验证工作流程
    QVERIFY(verifyApplicationState());
}

void ApplicationIntegrationTest::testConcurrentAccess()
{
    // 创建多个线程同时访问应用程序核心
    const int threadCount = 5;
    QList<QFuture<void>> futures;
    
    for (int i = 0; i < threadCount; ++i) {
        futures.append(QtConcurrent::run([this, i]() {
            // 配置操作
            ConfigManager* configManager = m_appCore->configManager();
            configManager->setValue(QString("thread_%1").arg(i), i);
            
            // 主题操作
            ThemeManager* themeManager = m_appCore->themeManager();
            themeManager->setTheme(i % 2 == 0 ? "light" : "dark");
            
            // 日志操作
            LogManager* logManager = m_appCore->logManager();
            logManager->logInfo(QString("Thread %1 log").arg(i));
            
            // 事件操作
            EventBus* eventBus = m_appCore->eventBus();
            TestEvent event(QString("Thread %1 event").arg(i), i);
            eventBus->publish(event);
        }));
    }
    
    // 等待所有线程完成
    for (QFuture<void>& future : futures) {
        future.waitForFinished();
    }
    
    // 验证配置
    ConfigManager* configManager = m_appCore->configManager();
    for (int i = 0; i < threadCount; ++i) {
        QCOMPARE(configManager->getValue(QString("thread_%1").arg(i)).toInt(), i);
    }
}

void ApplicationIntegrationTest::testMemoryManagement()
{
    // 测试内存管理
    
    // 使用智能指针
    std::unique_ptr<OptimizationParameters> paramsPtr = std::make_unique<OptimizationParameters>();
    paramsPtr->setMethodName("Test Method");
    
    // 验证智能指针
    QVERIFY(paramsPtr != nullptr);
    QCOMPARE(paramsPtr->methodName(), QString("Test Method"));
    
    // 使用RAII模式
    {
        QFile file(m_tempDir->path() + "/test_file.txt");
        QVERIFY(file.open(QIODevice::WriteOnly));
        file.write("Test content");
        // 文件会在作用域结束时自动关闭
    }
    
    // 验证文件已关闭并写入内容
    QFile readFile(m_tempDir->path() + "/test_file.txt");
    QVERIFY(readFile.open(QIODevice::ReadOnly));
    QCOMPARE(QString::fromUtf8(readFile.readAll()), QString("Test content"));
    readFile.close();
}

void ApplicationIntegrationTest::testPerformance()
{
    // 启用性能分析
    PerformanceProfiler::instance().setEnabled(true);
    PerformanceProfiler::instance().clearProfiles();
    
    // 测试性能
    {
        ScopedProfiler profiler("testConfigPerformance");
        
        ConfigManager* configManager = m_appCore->configManager();
        for (int i = 0; i < 1000; ++i) {
            configManager->setValue(QString("perf_key_%1").arg(i), i);
            configManager->getValue(QString("perf_key_%1").arg(i));
        }
    }
    
    {
        ScopedProfiler profiler("testEventPerformance");
        
        EventBus* eventBus = m_appCore->eventBus();
        for (int i = 0; i < 1000; ++i) {
            TestEvent event(QString("Performance event %1").arg(i), i);
            eventBus->publish(event);
        }
    }
    
    // 获取性能报告
    QMap<QString, PerformanceProfiler::ProfileInfo> profileInfo = PerformanceProfiler::instance().getAllProfileInfo();
    
    // 验证性能数据
    QVERIFY(profileInfo.contains("testConfigPerformance"));
    QVERIFY(profileInfo.contains("testEventPerformance"));
    
    // 打印性能报告
    PerformanceProfiler::instance().printReport();
}

bool ApplicationIntegrationTest::verifyApplicationState()
{
    // 验证配置
    ConfigManager* configManager = m_appCore->configManager();
    if (configManager->getValue("theme").toString() != "dark" ||
        configManager->getValue("language").toString() != "en") {
        return false;
    }
    
    // 验证主题
    ThemeManager* themeManager = m_appCore->themeManager();
    if (themeManager->currentTheme() != "dark") {
        return false;
    }
    
    // 验证仓库
    QString repoPath = m_tempDir->path() + "/workflow_repository.json";
    if (!QFile::exists(repoPath)) {
        return false;
    }
    
    FileRepository<OptimizationParameters> repository(repoPath);
    repository.load();
    if (repository.count() == 0) {
        return false;
    }
    
    // 验证日志
    QString logFilePath = m_appCore->logManager()->getLogFilePath();
    QFile logFile(logFilePath);
    if (!logFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return false;
    }
    
    QString logContent = QString::fromUtf8(logFile.readAll());
    logFile.close();
    
    if (!logContent.contains("Workflow completed successfully")) {
        return false;
    }
    
    return true;
}

QString ApplicationIntegrationTest::createTestDataFile(const QString& content)
{
    QString filePath = m_tempDir->path() + "/test_data_" + QUuid::createUuid().toString(QUuid::WithoutBraces) + ".txt";
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out << content;
        file.close();
        return filePath;
    }
    return QString();
}

// 使用QTEST_MAIN宏注册测试类
QTEST_MAIN(ApplicationIntegrationTest)
