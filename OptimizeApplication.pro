QT       += core gui charts printsupport

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17

# 禁用不兼容的编译警告
CONFIG += warn_off

# 输出目录
DESTDIR = $$PWD/bin
UI_DIR = $$PWD/build/ui
MOC_DIR = $$PWD/build/moc
OBJECTS_DIR = $$PWD/build/obj
RCC_DIR = $$PWD/build/rcc

# 包含SARibbonBar
include($$PWD/src/SARibbonBar/SARibbonBar.pri)

# 源文件
SOURCES += \
    src/core/ApplicationCore.cpp \
    src/core/ConfigManager.cpp \
    src/core/ThemeManager.cpp \
    src/data/IFileExample.cpp \
    src/data/InputData.cpp \
    src/main.cpp \
    src/ui/MainWindow.cpp \
    src/utils/Common.cpp \
    src/utils/Logger.cpp \
    src/utils/Utils.cpp \
    src/widgets/ChartWidget.cpp \
    src/widgets/CustomTreeWidget.cpp \
    src/widgets/RibbonOptimizeWidget.cpp \
    src/widgets/SensitivityWidget.cpp

# 头文件
HEADERS += \
    src/core/ApplicationCore.h \
    src/core/ConfigManager.h \
    src/core/ThemeManager.h \
    src/data/IFileExample.h \
    src/data/InputData.h \
    src/ui/MainWindow.h \
    src/utils/Common.h \
    src/utils/Logger.h \
    src/utils/Utils.h \
    src/widgets/ChartWidget.h \
    src/widgets/CustomTreeWidget.h \
    src/widgets/RibbonOptimizeWidget.h \
    src/widgets/SensitivityWidget.h

# UI文件
FORMS += \
    src/ui/MainWindow.ui

# 资源文件
RESOURCES += \
    resources/icons.qrc \
    src/SARibbonBar/SARibbonResource.qrc

# 默认规则使得生成的文件带有前缀
# 例如：moc_*.cpp, ui_*.h
CONFIG += no_keywords

# 复制目标
QMAKE_POST_LINK += $$quote(cmd /c xcopy /y /i \"$$PWD\\build.bat\" \"$$DESTDIR\")

# 使用C++17特性
QMAKE_CXXFLAGS += /std:c++17

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target 