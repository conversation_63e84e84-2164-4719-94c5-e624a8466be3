#include "MemoryUtils.h"
#include "ErrorHandler.h"
#include <QDebug>

namespace MemoryUtils {

MemoryLeakDetector& MemoryLeakDetector::instance()
{
    static MemoryLeakDetector detector;
    return detector;
}

MemoryLeakDetector::MemoryLeakDetector()
{
    qDebug() << "Memory leak detector initialized";
}

MemoryLeakDetector::~MemoryLeakDetector()
{
    printLeakReport();
}

void MemoryLeakDetector::registerAllocation(void* ptr, size_t size, const char* file, int line)
{
    QMutexLocker locker(&m_mutex);
    
    AllocationInfo info;
    info.size = size;
    info.file = file;
    info.line = line;
    
    m_allocations[ptr] = info;
}

void MemoryLeakDetector::registerDeallocation(void* ptr)
{
    QMutexLocker locker(&m_mutex);
    
    m_allocations.remove(ptr);
}

void MemoryLeakDetector::printLeakReport()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_allocations.isEmpty()) {
        qDebug() << "No memory leaks detected";
        return;
    }
    
    qDebug() << "Memory leak report:";
    qDebug() << "-------------------";
    
    size_t totalLeaked = 0;
    
    for (auto it = m_allocations.begin(); it != m_allocations.end(); ++it) {
        qDebug() << "Leaked" << it.value().size << "bytes at" << it.key()
                 << "allocated at" << it.value().file << "line" << it.value().line;
        
        totalLeaked += it.value().size;
    }
    
    qDebug() << "-------------------";
    qDebug() << "Total leaked:" << totalLeaked << "bytes in" << m_allocations.size() << "allocations";
    
    // 报告内存泄漏
    ErrorHandler::handleError(
        QString("Memory leak detected: %1 bytes in %2 allocations")
            .arg(totalLeaked)
            .arg(m_allocations.size()),
        ErrorSeverity::Warning,
        "MemoryLeakDetector"
    );
}

} // namespace MemoryUtils

#ifdef DEBUG_MODE
// 重载全局new和delete操作符
void* operator new(size_t size, const char* file, int line)
{
    void* ptr = std::malloc(size);
    if (!ptr) {
        throw std::bad_alloc();
    }
    
    MemoryUtils::MemoryLeakDetector::instance().registerAllocation(ptr, size, file, line);
    return ptr;
}

void* operator new[](size_t size, const char* file, int line)
{
    void* ptr = std::malloc(size);
    if (!ptr) {
        throw std::bad_alloc();
    }
    
    MemoryUtils::MemoryLeakDetector::instance().registerAllocation(ptr, size, file, line);
    return ptr;
}

void operator delete(void* ptr) noexcept
{
    if (ptr) {
        MemoryUtils::MemoryLeakDetector::instance().registerDeallocation(ptr);
        std::free(ptr);
    }
}

void operator delete[](void* ptr) noexcept
{
    if (ptr) {
        MemoryUtils::MemoryLeakDetector::instance().registerDeallocation(ptr);
        std::free(ptr);
    }
}
#endif
