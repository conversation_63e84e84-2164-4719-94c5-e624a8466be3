#ifndef SENSITIVITYWIDGET_H
#define SENSITIVITYWIDGET_H

#include <QWidget>

QT_BEGIN_NAMESPACE
class QComboBox;
class QSpinBox;
class QDoubleSpinBox;
class QCheckBox;
class QPushButton;
class QLabel;
class QGroupBox;
class QFormLayout;
class QVBoxLayout;
QT_END_NAMESPACE

namespace Ui {
class SensitivityWidget;
}

class SensitivityWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SensitivityWidget(QWidget *parent = nullptr);
    ~SensitivityWidget();

    // Getter methods for Dakota sensitivity analysis methods
    QString getMethod() const;
    int getSamples() const;
    QString getSamplingType() const;
    bool isVarianceBasedDecomp() const;
    double getConvergenceTolerance() const;
    int getSeed() const;
    
    // Setter methods
    void setMethod(const QString &method);
    void setSamples(int samples);
    void setSamplingType(const QString &type);
    void setVarianceBasedDecomp(bool enabled);
    void setConvergenceTolerance(double tolerance);
    void setSeed(int seed);

signals:
    void parametersChanged();
    void methodChanged(const QString &method);

private slots:
    void onMethodChanged();
    void onParameterChanged();
    void onSamplingTypeChanged();
    void onVarianceBasedToggled(bool enabled);

private:
    void setupUI();
    void setupConnections();
    void updateMethodDescription();
    void updateParameterVisibility();

    Ui::SensitivityWidget *ui;
};

#endif // SENSITIVITYWIDGET_H 