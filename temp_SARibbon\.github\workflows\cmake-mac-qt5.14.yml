name: CMake-Mac-Qt5.14

on: [pull_request,push]

env:
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release

jobs:
  macos:
    runs-on: macos-${{ matrix.macos_version }}
    name: macos-${{ matrix.macos_version }}-Qt-${{ matrix.qt_version }}-shared-${{ matrix.shared }}
    strategy:
      fail-fast: false
      matrix:
        macos_version: [latest]
        qt_version: [5.14.*]
        shared: [ON]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Qt
        uses: jurplel/install-qt-action@v4
        with:
            version: ${{ matrix.qt_version }}
            cache: 'false'
            cache-key-prefix: ${{ runner.os }}-Qt-Cache-${{ matrix.qt_version }}
            dir: ${{ github.workspace }}/Qt
            host: 'mac'
            arch: 'clang_64'

      - name: Configure CMake
        run: cmake -DCMAKE_BUILD_TYPE="${{env.BUILD_TYPE}}" -DBUILD_SHARED_LIBS=${{ matrix.shared }} -DBUILD_EXAMPLES=OFF -B "${{github.workspace}}/build"

      - name: Build
        run: cmake --build ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}