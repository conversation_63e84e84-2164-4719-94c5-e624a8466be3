#include "EventBus.h"

EventBus::EventBus()
    : QObject(nullptr)
    , m_nextHandlerId(1)
{
}

EventBus::~EventBus()
{
    m_eventHandlers.clear();
    m_handlerIds.clear();
}

bool EventBus::unsubscribe(int handlerId)
{
    if (!m_handlerIds.contains(handlerId)) {
        return false;
    }

    QString eventType = m_handlerIds[handlerId];
    m_handlerIds.remove(handlerId);

    // 注意：这里简化了实现，实际应该根据handlerId精确移除对应的处理函数
    // 在实际实现中，需要更复杂的数据结构来跟踪每个处理函数
    
    return true;
}
