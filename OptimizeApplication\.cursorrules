# Cursor Rules for Markdown Time Management
# 在每次写入markdown文件时自动插入当前时间的规则

## Markdown File Time Stamp Rules

### Rule 1: Auto-timestamp on markdown creation
When creating new markdown files (.md), always include:
- Creation timestamp at the beginning
- Last updated timestamp 
- Use Chinese date format: "yyyy年MM月dd日 HH:mm:ss"

### Rule 2: Auto-timestamp on markdown modification
When modifying existing markdown files (.md):
- Update existing timestamp if found (patterns: **最后更新：**, **更新时间：**, **文档更新时间：**)
- If no existing timestamp found, append new timestamp at the end
- Always use format: **最后更新：** [current_time]

### Rule 3: Timestamp format specification
- Default format: **最后更新：** 2025年06月24日 09:30:00
- Alternative formats available: ISO (2025-06-24 09:30:00), US (06/24/2025 09:30:00)
- Always use Chinese format unless specifically requested otherwise

### Rule 4: Implementation method
When working with markdown files, use one of these approaches:

#### Method 1: PowerShell Command (Recommended)
```powershell
powershell -ExecutionPolicy Bypass -File .\scripts\Update-MarkdownTime.ps1 -FilePath "filename.md" -Action "update"
```

#### Method 2: Manual PowerShell
```powershell
$timestamp = Get-Date -Format "yyyy年MM月dd日 HH:mm:ss"
# For new files: add both creation and update time
# For existing files: update existing timestamp or append new one
```

#### Method 3: Batch Script
```cmd
.\scripts\update-md-time.bat filename.md update
```

### Rule 5: Automatic execution triggers
- Before saving markdown files
- Before committing changes (if git hooks are configured)
- When explicitly requested by user
- When creating new documentation

### Rule 6: File patterns to include
- All .md files
- All .markdown files
- Documentation files in any directory
- README files

### Rule 7: Timestamp placement rules
- **New files**: Add timestamp header after title
- **Existing files**: Update existing timestamp or append at end
- **Documentation files**: Prefer header placement after first heading
- **README files**: Add at the end to avoid disrupting structure

### Rule 8: Content preservation
- Never modify existing content except timestamp
- Preserve all formatting and structure
- Maintain UTF-8 encoding
- Keep original line endings

### Rule 9: Batch operations
When processing multiple markdown files:
- Process all files in current directory and subdirectories
- Skip binary files and non-text files
- Log all timestamp updates
- Provide summary of changes made

### Rule 10: Error handling
- Check file existence before processing
- Handle encoding issues gracefully
- Provide clear error messages
- Skip files that cannot be processed safely

## Implementation Examples

### For new markdown file creation:
```markdown
# Document Title

**创建时间：** 2025年06月24日 09:30:00
**最后更新：** 2025年06月24日 09:30:00

---

[Content here]
```

### For existing file updates:
```markdown
[Existing content...]

**最后更新：** 2025年06月24日 09:30:00
```

## Quick Commands for Cursor Terminal

### Single file update:
```powershell
powershell -ExecutionPolicy Bypass -File .\scripts\Update-MarkdownTime.ps1 -FilePath "program.md" -Action "update"
```

### Batch update all markdown files:
```powershell
Get-ChildItem -Filter "*.md" -Recurse | ForEach-Object {
    powershell -ExecutionPolicy Bypass -File ".\scripts\Update-MarkdownTime.ps1" -FilePath $_.FullName -Action "update"
}
```

### Get current timestamp manually:
```powershell
Get-Date -Format "yyyy年MM月dd日 HH:mm:ss"
```

## Integration Notes

- Scripts are located in `.\scripts\` directory
- Main script: `Update-MarkdownTime.ps1`
- Backup script: `update-md-time.bat`
- Documentation: `scripts\README.md`

## Execution Policy Note

If PowerShell execution is restricted, use:
```powershell
powershell -ExecutionPolicy Bypass -File script.ps1
```

## Character Encoding

- Always use UTF-8 encoding for markdown files
- Handle Chinese characters properly
- Preserve existing file encoding when possible

---

## Qt5.14.2 Development Rules

### Qt Environment
- Use Qt5.14.2_msvc2017_x64
- QMake Path: D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin/qmake
- All code and comments must be in English only
- NO Chinese characters in code, variable names, function names, or comments

### Qt Coding Standards
- Use Qt5-style includes: `#include <QtWidgets/QApplication>`
- Prefer new-style signal-slot syntax: `connect(button, &QPushButton::clicked, this, &MainWindow::onButtonClicked)`
- Use parent-child relationship for Qt objects: `new QWidget(this)`
- Follow naming conventions: Classes (PascalCase), methods (camelCase), members (m_prefix)
- Use QString for all string operations with QStringLiteral for literals
- Always use .ui files for complex layouts

### Quick Qt Commands
```bash
# Generate and build
qmake CONFIG+=debug
make  # or nmake on Windows

# Clean build
make clean && qmake && make
```

---

**规则创建时间：** 2025年06月24日 10:09:39
**最后更新：** 2025年06月24日 10:09:39 