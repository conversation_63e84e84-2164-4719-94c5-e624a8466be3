#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QString>
#include <QStringList>
#include <QSettings>
#include <QByteArray>
#include <QVector>

/**
 * @brief The ConfigManager class manages application configuration settings
 */
class ConfigManager
{
public:
    /**
     * @brief Constructor
     */
    ConfigManager();
    
    /**
     * @brief Destructor
     */
    ~ConfigManager();
    
    /**
     * @brief Initialize the configuration manager
     * @return True if initialization was successful, false otherwise
     */
    bool initialize();
    
    /**
     * @brief Save all settings to storage
     */
    void saveSettings();
    
    /**
     * @brief Get the window geometry
     * @return Window geometry as byte array
     */
    QByteArray getWindowGeometry() const;
    
    /**
     * @brief Set the window geometry
     * @param geometry Window geometry as byte array
     */
    void setWindowGeometry(const QByteArray &geometry);
    
    /**
     * @brief Get the window state
     * @return Window state as byte array
     */
    QByteArray getWindowState() const;
    
    /**
     * @brief Set the window state
     * @param state Window state as byte array
     */
    void setWindowState(const QByteArray &state);
    
    /**
     * @brief Get the recent files list
     * @return List of recent files
     */
    QStringList getRecentFiles() const;
    
    /**
     * @brief Add a file to the recent files list
     * @param filePath Path to the file
     */
    void addRecentFile(const QString &filePath);
    
    /**
     * @brief Get the last used directory
     * @return Path to the last used directory
     */
    QString getLastDirectory() const;
    
    /**
     * @brief Set the last used directory
     * @param directory Path to the directory
     */
    void setLastDirectory(const QString &directory);
    
    /**
     * @brief Get a generic setting value
     * @param key Setting key
     * @param defaultValue Default value if the setting doesn't exist
     * @return Setting value
     */
    QVariant getSetting(const QString &key, const QVariant &defaultValue = QVariant()) const;
    
    /**
     * @brief Set a generic setting value
     * @param key Setting key
     * @param value Setting value
     */
    void setSetting(const QString &key, const QVariant &value);

private:
    QSettings *settings;          ///< Settings storage
    
    // Cached settings for frequently accessed values
    QByteArray windowGeometry;    ///< Cached window geometry
    QByteArray windowState;       ///< Cached window state
    QStringList recentFiles;      ///< Cached recent files list
    QString lastDirectory;        ///< Cached last directory
    
    static const int MAX_RECENT_FILES = 10;  ///< Maximum number of recent files
};

#endif // CONFIGMANAGER_H 