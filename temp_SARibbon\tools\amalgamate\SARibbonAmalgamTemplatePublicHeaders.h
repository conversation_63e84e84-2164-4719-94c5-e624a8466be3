﻿//Global
#include "../../src/SARibbonBar/colorWidgets/SAColorWidgetsGlobal.h"
#include "../../src/SARibbonBar/SARibbonBarVersionInfo.h"
#include "../../src/SARibbonBar/SARibbonGlobal.h"
//color widget
#include "../../src/SARibbonBar/colorWidgets/SAColorMenu.h"
#include "../../src/SARibbonBar/colorWidgets/SAColorGridWidget.h"
#include "../../src/SARibbonBar/colorWidgets/SAColorPaletteGridWidget.h"
#include "../../src/SARibbonBar/colorWidgets/SAColorToolButton.h"
//sa ribbon
#include "../../src/SARibbonBar/SAFramelessHelper.h"
#include "../../src/SARibbonBar/SARibbonApplicationButton.h"
#include "../../src/SARibbonBar/SARibbonSystemButtonBar.h"
#include "../../src/SARibbonBar/SARibbonToolButton.h"
#include "../../src/SARibbonBar/SARibbonColorToolButton.h"
#include "../../src/SARibbonBar/SARibbonLineWidgetContainer.h"
#include "../../src/SARibbonBar/SARibbonActionsManager.h"
#include "../../src/SARibbonBar/SARibbonLineEdit.h"
#include "../../src/SARibbonBar/SARibbonCheckBox.h"
#include "../../src/SARibbonBar/SARibbonComboBox.h"
#include "../../src/SARibbonBar/SARibbonButtonGroupWidget.h"
#include "../../src/SARibbonBar/SARibbonStackedWidget.h"
#include "../../src/SARibbonBar/SARibbonSeparatorWidget.h"
#include "../../src/SARibbonBar/SARibbonCtrlContainer.h"
#include "../../src/SARibbonBar/SARibbonQuickAccessBar.h"
#include "../../src/SARibbonBar/SARibbonTabBar.h"
#include "../../src/SARibbonBar/SARibbonControlButton.h"
#include "../../src/SARibbonBar/SARibbonMenu.h"

#include "../../src/SARibbonBar/SARibbonPannelOptionButton.h"
#include "../../src/SARibbonBar/SARibbonPannelItem.h"
#include "../../src/SARibbonBar/SARibbonPannelLayout.h"
#include "../../src/SARibbonBar/SARibbonPannel.h"
#include "../../src/SARibbonBar/SARibbonCategory.h"
#include "../../src/SARibbonBar/SARibbonCategoryLayout.h"
#include "../../src/SARibbonBar/SARibbonContextCategory.h"
#include "../../src/SARibbonBar/SARibbonGalleryItem.h"
#include "../../src/SARibbonBar/SARibbonGalleryGroup.h"
#include "../../src/SARibbonBar/SARibbonGallery.h"
#include "../../src/SARibbonBar/SARibbonBar.h"
#include "../../src/SARibbonBar/SARibbonElementFactory.h"
#include "../../src/SARibbonBar/SARibbonElementManager.h"
#include "../../src/SARibbonBar/SARibbonCustomizeData.h"
#include "../../src/SARibbonBar/SARibbonCustomizeWidget.h"
#include "../../src/SARibbonBar/SARibbonCustomizeDialog.h"
#include "../../src/SARibbonBar/SARibbonMainWindow.h"
#include "../../src/SARibbonBar/SARibbonWidget.h"
#include "../../src/SARibbonBar/SARibbonApplicationWidget.h"