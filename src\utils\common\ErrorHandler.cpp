#include "ErrorHandler.h"
#include <QDateTime>
#include <QDebug>
#include <QCoreApplication>

#ifdef Q_OS_WIN
#include <windows.h>
#include <dbghelp.h>
#pragma comment(lib, "dbghelp.lib")
#endif

<PERSON>rror<PERSON>andler* ErrorHandler::s_instance = nullptr;

ErrorHandler::ErrorHandler()
    : QObject(nullptr)
    , m_stackTraceEnabled(false)
    , m_minimumSeverity(ErrorSeverity::Info)
{
}

ErrorHandler::~ErrorHandler()
{
}

ErrorHandler* ErrorHandler::instance()
{
    if (!s_instance) {
        s_instance = new ErrorHandler();
    }
    return s_instance;
}

void ErrorHandler::handleError(const QString& message, 
                              ErrorSeverity severity,
                              const QString& source,
                              int errorCode)
{
    ErrorInfo errorInfo;
    errorInfo.message = message;
    errorInfo.severity = severity;
    errorInfo.source = source;
    errorInfo.timestamp = QDateTime::currentDateTime().toString(Qt::ISODate);
    errorInfo.errorCode = errorCode;
    
    if (instance()->m_stackTraceEnabled) {
        errorInfo.stackTrace = getCurrentStackTrace();
    }
    
    instance()->handleErrorInternal(errorInfo);
}

void ErrorHandler::handleException(const std::exception& e, const QString& source)
{
    QString message = QString("Exception: %1").arg(e.what());
    handleError(message, ErrorSeverity::Error, source);
}

void ErrorHandler::setErrorCallback(const ErrorCallback& callback)
{
    instance()->m_errorCallback = callback;
}

void ErrorHandler::setStackTraceEnabled(bool enabled)
{
    instance()->m_stackTraceEnabled = enabled;
}

void ErrorHandler::setMinimumSeverity(ErrorSeverity severity)
{
    instance()->m_minimumSeverity = severity;
}

QString ErrorHandler::severityToString(ErrorSeverity severity)
{
    switch (severity) {
    case ErrorSeverity::Info:     return "INFO";
    case ErrorSeverity::Warning:  return "WARNING";
    case ErrorSeverity::Error:    return "ERROR";
    case ErrorSeverity::Critical: return "CRITICAL";
    case ErrorSeverity::Fatal:    return "FATAL";
    default:                      return "UNKNOWN";
    }
}

QString ErrorHandler::getCurrentStackTrace()
{
    QString stackTrace;
    
#ifdef Q_OS_WIN
    // Windows平台的堆栈跟踪实现
    HANDLE process = GetCurrentProcess();
    HANDLE thread = GetCurrentThread();
    
    CONTEXT context;
    memset(&context, 0, sizeof(CONTEXT));
    context.ContextFlags = CONTEXT_FULL;
    RtlCaptureContext(&context);
    
    SymInitialize(process, NULL, TRUE);
    
    DWORD image;
    STACKFRAME64 stackframe;
    ZeroMemory(&stackframe, sizeof(STACKFRAME64));
    
#ifdef _M_IX86
    image = IMAGE_FILE_MACHINE_I386;
    stackframe.AddrPC.Offset = context.Eip;
    stackframe.AddrPC.Mode = AddrModeFlat;
    stackframe.AddrFrame.Offset = context.Ebp;
    stackframe.AddrFrame.Mode = AddrModeFlat;
    stackframe.AddrStack.Offset = context.Esp;
    stackframe.AddrStack.Mode = AddrModeFlat;
#elif _M_X64
    image = IMAGE_FILE_MACHINE_AMD64;
    stackframe.AddrPC.Offset = context.Rip;
    stackframe.AddrPC.Mode = AddrModeFlat;
    stackframe.AddrFrame.Offset = context.Rsp;
    stackframe.AddrFrame.Mode = AddrModeFlat;
    stackframe.AddrStack.Offset = context.Rsp;
    stackframe.AddrStack.Mode = AddrModeFlat;
#endif
    
    for (int i = 0; i < 25; i++) {
        BOOL result = StackWalk64(
            image, process, thread,
            &stackframe, &context, NULL,
            SymFunctionTableAccess64, SymGetModuleBase64, NULL);
        
        if (!result) break;
        
        char buffer[sizeof(SYMBOL_INFO) + MAX_SYM_NAME * sizeof(TCHAR)];
        PSYMBOL_INFO symbol = (PSYMBOL_INFO)buffer;
        symbol->SizeOfStruct = sizeof(SYMBOL_INFO);
        symbol->MaxNameLen = MAX_SYM_NAME;
        
        DWORD64 displacement = 0;
        if (SymFromAddr(process, stackframe.AddrPC.Offset, &displacement, symbol)) {
            stackTrace += QString("  %1: %2 + 0x%3\n")
                         .arg(i)
                         .arg(symbol->Name)
                         .arg(displacement, 0, 16);
        } else {
            stackTrace += QString("  %1: 0x%2\n")
                         .arg(i)
                         .arg(stackframe.AddrPC.Offset, 0, 16);
        }
    }
    
    SymCleanup(process);
#else
    // 其他平台的简单实现
    stackTrace = "Stack trace not available on this platform";
#endif
    
    return stackTrace;
}

void ErrorHandler::handleErrorInternal(const ErrorInfo& errorInfo)
{
    // 检查最小错误级别
    if (errorInfo.severity < m_minimumSeverity) {
        return;
    }
    
    // 输出到调试控制台
    QString logMessage = QString("[%1] [%2] %3")
                        .arg(errorInfo.timestamp)
                        .arg(severityToString(errorInfo.severity))
                        .arg(errorInfo.message);
    
    if (!errorInfo.source.isEmpty()) {
        logMessage += QString(" (Source: %1)").arg(errorInfo.source);
    }
    
    if (errorInfo.errorCode != 0) {
        logMessage += QString(" (Code: %1)").arg(errorInfo.errorCode);
    }
    
    switch (errorInfo.severity) {
    case ErrorSeverity::Info:
        qInfo().noquote() << logMessage;
        break;
    case ErrorSeverity::Warning:
        qWarning().noquote() << logMessage;
        break;
    case ErrorSeverity::Error:
    case ErrorSeverity::Critical:
    case ErrorSeverity::Fatal:
        qCritical().noquote() << logMessage;
        break;
    }
    
    if (!errorInfo.stackTrace.isEmpty()) {
        qDebug().noquote() << "Stack trace:\n" << errorInfo.stackTrace;
    }
    
    // 调用自定义回调
    if (m_errorCallback) {
        m_errorCallback(errorInfo);
    }
    
    // 发送信号
    emit errorOccurred(errorInfo);
    
    // 处理致命错误
    if (errorInfo.severity == ErrorSeverity::Fatal) {
        qFatal("Fatal error occurred: %s", errorInfo.message.toUtf8().constData());
    }
}

// ExceptionGuard实现
ExceptionGuard::ExceptionGuard(const QString& source)
    : m_source(source)
{
}

ExceptionGuard::~ExceptionGuard()
{
}
