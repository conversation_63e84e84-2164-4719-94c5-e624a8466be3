#include "SensitivityWidget.h"
#include "../utils/Logger.h"
#include "../SARibbonBar/SARibbonPannel.h"
#include <QHeaderView>
#include <QMessageBox>

SensitivityWidget::SensitivityWidget(QWidget *parent)
    : QWidget(parent)
    , mainLayout(new QVBoxLayout(this))
    , controlLayout(new QHBoxLayout())
    , parametersTable(new QTableView(this))
    , tableModel(new QStandardItemModel(this))
    , addButton(new QPushButton(tr("Add"), this))
    , removeButton(new QPushButton(tr("Remove"), this))
    , runButton(new QPushButton(tr("Run Analysis"), this))
    , methodCombo(new QComboBox(this))
    , samplesLabel(new QLabel(tr("Samples:"), this))
    , samplesSpinBox(new QSpinBox(this))
    , toleranceLabel(new QLabel(tr("Tolerance:"), this))
    , toleranceSpinBox(new QDoubleSpinBox(this))
{
    LOG_INFO("Creating SensitivityWidget", "SensitivityWidget");
    
    initUI();
    setupConnections();
    
    LOG_INFO("SensitivityWidget created", "SensitivityWidget");
}

SensitivityWidget::~SensitivityWidget()
{
    LOG_INFO("Destroying SensitivityWidget", "SensitivityWidget");
}

SARibbonCategory* SensitivityWidget::createRibbonCategory(SARibbonBar* ribbon)
{
    LOG_INFO("Creating sensitivity ribbon category", "SensitivityWidget");
    
    // Create ribbon category
    SARibbonCategory* category = ribbon->addCategoryPage(tr("Sensitivity"));
    
    // Method panel
    SARibbonPannel* methodPanel = category->addPannel(tr("Method"));
    
    // Add method actions
    QAction* localAction = new QAction(QIcon(":/icons/local_sensitivity.png"), tr("Local"), this);
    localAction->setToolTip(tr("Local sensitivity analysis"));
    methodPanel->addLargeAction(localAction);
    
    QAction* globalAction = new QAction(QIcon(":/icons/global_sensitivity.png"), tr("Global"), this);
    globalAction->setToolTip(tr("Global sensitivity analysis"));
    methodPanel->addLargeAction(globalAction);
    
    QAction* sobolAction = new QAction(QIcon(":/icons/sobol.png"), tr("Sobol"), this);
    sobolAction->setToolTip(tr("Sobol indices sensitivity analysis"));
    methodPanel->addLargeAction(sobolAction);
    
    QAction* morrisAction = new QAction(QIcon(":/icons/morris.png"), tr("Morris"), this);
    morrisAction->setToolTip(tr("Morris method (elementary effects)"));
    methodPanel->addLargeAction(morrisAction);
    
    // Parameters panel
    SARibbonPannel* parametersPanel = category->addPannel(tr("Parameters"));
    
    // Add parameter actions
    QAction* addAction = new QAction(QIcon(":/icons/add.png"), tr("Add"), this);
    addAction->setToolTip(tr("Add a parameter to analyze"));
    connect(addAction, &QAction::triggered, this, &SensitivityWidget::addParameter);
    parametersPanel->addLargeAction(addAction);
    
    QAction* removeAction = new QAction(QIcon(":/icons/remove.png"), tr("Remove"), this);
    removeAction->setToolTip(tr("Remove selected parameter"));
    connect(removeAction, &QAction::triggered, this, &SensitivityWidget::removeParameter);
    parametersPanel->addLargeAction(removeAction);
    
    // Settings panel
    SARibbonPannel* settingsPanel = category->addPannel(tr("Settings"));
    
    // Add sample count control
    settingsPanel->addSmallWidget(new QLabel(tr("Samples:"), this));
    QSpinBox* ribbonSamplesSpinBox = new QSpinBox(this);
    ribbonSamplesSpinBox->setRange(10, 10000);
    ribbonSamplesSpinBox->setValue(100);
    ribbonSamplesSpinBox->setSingleStep(10);
    settingsPanel->addSmallWidget(ribbonSamplesSpinBox);
    
    // Add tolerance control
    settingsPanel->addSmallWidget(new QLabel(tr("Tolerance:"), this));
    QDoubleSpinBox* ribbonToleranceSpinBox = new QDoubleSpinBox(this);
    ribbonToleranceSpinBox->setRange(0.00001, 0.1);
    ribbonToleranceSpinBox->setValue(0.001);
    ribbonToleranceSpinBox->setSingleStep(0.001);
    ribbonToleranceSpinBox->setDecimals(6);
    settingsPanel->addSmallWidget(ribbonToleranceSpinBox);
    
    // Run panel
    SARibbonPannel* runPanel = category->addPannel(tr("Run"));
    
    // Add run action
    QAction* runAction = new QAction(QIcon(":/icons/run.png"), tr("Run"), this);
    runAction->setToolTip(tr("Run sensitivity analysis"));
    connect(runAction, &QAction::triggered, this, &SensitivityWidget::runAnalysis);
    runPanel->addLargeAction(runAction);
    
    return category;
}

void SensitivityWidget::addParameter()
{
    LOG_INFO("Adding parameter to sensitivity analysis", "SensitivityWidget");
    
    // Add a new row to the table
    QList<QStandardItem*> rowItems;
    rowItems << new QStandardItem(tr("Parameter %1").arg(tableModel->rowCount() + 1));
    rowItems << new QStandardItem("0.0");
    rowItems << new QStandardItem("1.0");
    rowItems << new QStandardItem("0.1");
    
    tableModel->appendRow(rowItems);
}

void SensitivityWidget::removeParameter()
{
    LOG_INFO("Removing parameter from sensitivity analysis", "SensitivityWidget");
    
    QModelIndexList selectedIndexes = parametersTable->selectionModel()->selectedRows();
    if (selectedIndexes.isEmpty()) {
        QMessageBox::information(this, tr("Selection Required"), 
                                tr("Please select a parameter to remove."));
        return;
    }
    
    // Sort in descending order so we can remove rows without affecting other row indices
    std::sort(selectedIndexes.begin(), selectedIndexes.end(), 
              [](const QModelIndex& a, const QModelIndex& b) { return b.row() < a.row(); });
    
    for (const QModelIndex& index : selectedIndexes) {
        tableModel->removeRow(index.row());
    }
}

void SensitivityWidget::runAnalysis()
{
    LOG_INFO("Running sensitivity analysis", "SensitivityWidget");
    
    // Get the selected method
    QString methodName = methodCombo->currentText();
    int sampleCount = samplesSpinBox->value();
    double tolerance = toleranceSpinBox->value();
    
    LOG_INFO(QString("Method: %1, Samples: %2, Tolerance: %3")
             .arg(methodName)
             .arg(sampleCount)
             .arg(tolerance), "SensitivityWidget");
    
    // TODO: Implement sensitivity analysis logic
    
    QMessageBox::information(this, tr("Sensitivity Analysis"),
                            tr("Starting sensitivity analysis using %1 method with %2 samples.")
                            .arg(methodName)
                            .arg(sampleCount));
}

void SensitivityWidget::onMethodChanged(int index)
{
    LOG_INFO(QString("Sensitivity method changed to index %1").arg(index), "SensitivityWidget");
    
    // Update UI based on method selection
    // For example, enable/disable certain options
    
    // TODO: Update UI based on selected method
}

void SensitivityWidget::initUI()
{
    LOG_INFO("Initializing UI for SensitivityWidget", "SensitivityWidget");
    
    setLayout(mainLayout);
    
    // Set up table
    QStringList headers;
    headers << tr("Parameter") << tr("Min") << tr("Max") << tr("Step");
    tableModel->setHorizontalHeaderLabels(headers);
    
    parametersTable->setModel(tableModel);
    parametersTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    parametersTable->setSelectionMode(QAbstractItemView::SingleSelection);
    parametersTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    parametersTable->verticalHeader()->setVisible(false);
    
    mainLayout->addWidget(parametersTable, 1);
    
    // Set up control layout
    mainLayout->addLayout(controlLayout);
    
    // Method selection
    methodCombo->addItem(tr("Local Sensitivity"));
    methodCombo->addItem(tr("Global Sensitivity"));
    methodCombo->addItem(tr("Sobol Indices"));
    methodCombo->addItem(tr("Morris Method"));
    
    controlLayout->addWidget(new QLabel(tr("Method:")));
    controlLayout->addWidget(methodCombo);
    
    // Sample count
    samplesSpinBox->setRange(10, 10000);
    samplesSpinBox->setValue(100);
    samplesSpinBox->setSingleStep(10);
    
    controlLayout->addWidget(samplesLabel);
    controlLayout->addWidget(samplesSpinBox);
    
    // Tolerance
    toleranceSpinBox->setRange(0.00001, 0.1);
    toleranceSpinBox->setValue(0.001);
    toleranceSpinBox->setSingleStep(0.001);
    toleranceSpinBox->setDecimals(6);
    
    controlLayout->addWidget(toleranceLabel);
    controlLayout->addWidget(toleranceSpinBox);
    
    // Add buttons
    controlLayout->addWidget(addButton);
    controlLayout->addWidget(removeButton);
    controlLayout->addWidget(runButton);
    
    // Add spacing for better appearance
    controlLayout->addStretch();
}

void SensitivityWidget::setupConnections()
{
    LOG_INFO("Setting up connections for SensitivityWidget", "SensitivityWidget");
    
    connect(addButton, &QPushButton::clicked, this, &SensitivityWidget::addParameter);
    connect(removeButton, &QPushButton::clicked, this, &SensitivityWidget::removeParameter);
    connect(runButton, &QPushButton::clicked, this, &SensitivityWidget::runAnalysis);
    connect(methodCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), 
            this, &SensitivityWidget::onMethodChanged);
} 