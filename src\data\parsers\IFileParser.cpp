#include "IFileParser.h"
#include <QFileInfo>

bool IFileParser::canParse(const QString& filePath) const
{
    QFileInfo fileInfo(filePath);
    QString extension = fileInfo.suffix().toLower();
    
    QStringList extensions = supportedExtensions();
    for (const QString& ext : extensions) {
        if (extension == ext.toLower()) {
            return true;
        }
    }
    
    return false;
}

IFileParser::ParseResult IFileParser::validateFile(const QString& filePath)
{
    ParseResult result;
    
    if (!canParse(filePath)) {
        result.success = false;
        result.errorMessage = QString("Unsupported file format: %1").arg(filePath);
        return result;
    }
    
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        result.success = false;
        result.errorMessage = QString("File does not exist: %1").arg(filePath);
        return result;
    }
    
    if (!fileInfo.isReadable()) {
        result.success = false;
        result.errorMessage = QString("File is not readable: %1").arg(filePath);
        return result;
    }
    
    result.success = true;
    return result;
}
