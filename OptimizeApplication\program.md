﻿# OptimizeApplication 程序开发记录

## 项目概述

OptimizeApplication是一个基于Qt的优化分析应用程序，集成了Dakota优化库，支持多种分析类型包括优化、敏感性分析和不确定性量化。

---

## 2024年树结构重构 - Dakota Method节点对应

### 修改背景

原有的单一Project节点结构无法很好地支持Dakota的多种分析方法。为了更好地对应Dakota的method节点配置，需要将树结构重新设计为三个独立的分析类型节点。

### 修改目标

1. 将project复制成三个节点：优化、敏感性分析和UQ
2. 每个节点包含独立的inputWidget和outputWidget
3. 根据不同类型对应Dakota内的method节点配置

### 实施方案

#### 1. 节点类型扩展

**文件：** `src/utils/Common.h`

```cpp
// Node type enumeration
enum class NodeType {
    Root,
    Optimize,
    Sensitivity,    // 新增：敏感性分析
    UQ,            // 新增：不确定性量化
    Input,
    Output,
    Solver,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Unknown
};
```

#### 2. 树结构重新设计

**文件：** `src/widgets/CustomTreeWidget.cpp`

**新的树结构：**
```
├── Optimization
│   ├── Input
│   └── Output
├── Sensitivity Analysis  
│   ├── Input
│   └── Output
├── Uncertainty Quantification
│   ├── Input
│   └── Output
└── Solver
    ├── Pipes (n)
    ├── Junctions (n)
    ├── Volumes (n)
    └── Branches (n)
```

**关键修改：**
- 移除了原来的Project根节点
- 创建三个顶级分析节点，每个都有独立的Input/Output子节点
- 更新了节点类型处理、图标显示和翻译支持

#### 3. 新增参数配置Widget

##### SensitivityWidget - 敏感性分析配置

**文件：** 
- `src/widgets/SensitivityWidget.h`
- `src/widgets/SensitivityWidget.cpp` 
- `src/widgets/SensitivityWidget.ui`

**支持的Dakota方法：**
- `sampling`: Monte Carlo和Latin Hypercube采样
- `local_reliability`: 基于梯度的FORM/SORM方法
- `polynomial_chaos`: 多项式混沌展开
- `stoch_collocation`: 随机配置方法

**主要参数配置：**
- 采样大小和类型（random, lhs, incremental_lhs）
- 方差分解选项（Sobol指数计算）
- 收敛容差设置
- 输出选项（相关矩阵、敏感性指数、主效应、交互效应）

##### UQWidget - 不确定性量化配置

**文件：**
- `src/widgets/UQWidget.h`
- `src/widgets/UQWidget.cpp`
- `src/widgets/UQWidget.ui`

**支持的Dakota方法：**
- `sampling`: Monte Carlo采样
- `polynomial_chaos`: 多项式混沌展开
- `stoch_collocation`: 随机配置
- `local_reliability` / `global_reliability`: 可靠性分析
- `importance_sampling`: 重要性采样

**主要参数配置：**
- 采样参数（样本数、类型、随机种子）
- 多项式展开设置（阶数、展开类型）
- 自适应细化选项
- 输出统计选项（矩、概率水平、可靠性水平、Sobol指数）

#### 4. ParamWidget集成更新

**文件：** `src/widgets/ParamWidget.h/.cpp`

**新增功能：**
- 添加SensitivityWidget和UQWidget实例
- 新增显示方法：`showSensitivityParams()`, `showUQParams()`
- 更新信号连接，支持新Widget的参数变化通知
- 添加getter方法获取新Widget引用

#### 5. MainWindow事件处理更新

**文件：** `src/ui/MainWindow.cpp`

**更新内容：**
- `onTreeItemChanged()`: 添加对Sensitivity和UQ节点的处理
- `onTreeItemDoubleClicked()`: 为新节点类型添加详细信息显示
- 更新节点选择逻辑，支持新的分析类型切换

#### 6. 构建系统更新

**文件：** `OptimizeApplication.pro`

**添加内容：**
```pro
SOURCES += \
    # ... 现有文件 ...
    src/widgets/SensitivityWidget.cpp \
    src/widgets/UQWidget.cpp \

HEADERS += \
    # ... 现有文件 ...
    src/widgets/SensitivityWidget.h \
    src/widgets/UQWidget.h \

FORMS += \
    # ... 现有文件 ...
    src/widgets/SensitivityWidget.ui \
    src/widgets/UQWidget.ui \
```

### Dakota Method对应关系

#### 优化分析 (Optimization)
```dakota
method
  genetic_algorithm
    population_size 100
    max_generations 500
    mutation_rate 0.1
    crossover_rate 0.8
```

#### 敏感性分析 (Sensitivity Analysis)
```dakota
method
  sampling
    sample_type lhs
    samples 1000
    variance_based_decomp
    seed 12345
```

或者：
```dakota
method
  local_reliability
    mpp_search x_taylor_mean
    integration second_order
```

#### 不确定性量化 (UQ)
```dakota
method
  polynomial_chaos
    expansion_order 3
    expansion_type total_order
    samples_on_emulator 10000
```

或者：
```dakota
method
  sampling
    sample_type random
    samples 10000
    seed 12345
```

### 技术特点

1. **模块化设计**：每种分析类型都有独立的参数配置界面
2. **Dakota兼容**：参数设置直接对应Dakota的method配置
3. **动态界面**：根据选择的方法动态显示/隐藏相关参数
4. **参数验证**：实时验证参数的有效性和一致性
5. **扩展性强**：易于添加新的分析方法和参数选项

### 使用流程

1. **选择分析类型**：在树结构中选择Optimization、Sensitivity Analysis或UQ节点
2. **配置方法参数**：在参数面板中选择具体的Dakota方法并配置参数
3. **设置输入输出**：在各分析类型下的Input/Output节点中配置变量和响应
4. **运行分析**：系统根据配置生成Dakota输入文件并执行分析

### 后续扩展计划

1. **添加更多Dakota方法**：继续集成其他Dakota支持的分析方法
2. **参数模板系统**：为常用配置提供预设模板
3. **结果可视化**：为不同分析类型提供专门的结果展示界面
4. **配置管理**：支持分析配置的保存、加载和版本管理
5. **批量分析**：支持同时配置和运行多种分析类型

### 修改完成状态

✅ 节点类型扩展 (Common.h)  
✅ 树结构重新设计 (CustomTreeWidget)  
✅ SensitivityWidget开发完成  
✅ UQWidget开发完成  
✅ ParamWidget集成更新  
✅ MainWindow事件处理更新  
✅ 构建系统配置更新  

**修改日期：** 2024年12月  
**状态：** 已完成，待测试验证

---

## OptimizeWidget重新设计 (2024-12-19)

### 修改背景
用户要求在OptimizeWidget内只使用pareto_set、conmin_frcg、conmin_mfd、soga四种算法，重新组织UI界面内容。

### 实施内容

#### 1. UI界面重新设计 (OptimizeWidget.ui)
**算法选择组**
- 修改为"Dakota Optimization Method"
- 只保留四种Dakota算法：pareto_set、conmin_frcg、conmin_mfd、soga
- 添加算法描述显示

**参数组织结构**
- **收敛设置组 (Convergence Settings)**
  - 最大迭代次数 (Max Iterations)
  - 最大函数评估次数 (Max Function Evaluations)  
  - 收敛容差 (Convergence Tolerance)

- **SOGA参数组 (Single-Objective GA)**
  - 种群大小 (Population Size)
  - 变异率 (Mutation Rate)
  - 交叉率 (Crossover Rate)
  - 替换类型 (Replacement Type): elitist, roulette_wheel, unique_roulette_wheel

- **Pareto Set参数组 (Multi-Objective)**
  - 权重集数量 (Weight Sets)
  - 随机权重集生成选项

- **CONMIN参数组 (Gradient-Based)**
  - 梯度容差 (Gradient Tolerance)
  - 最大步长 (Max Step Size)
  - 投机梯度选项 (Speculative Gradient)

- **输出设置组 (Output Settings)**
  - 详细输出 (Verbose Output)
  - 调试输出 (Debug Output)
  - 静默模式 (Quiet Mode)

#### 2. 代码实现更新

**OptimizeWidget.h**
- 更新getter/setter方法名，对应新的Dakota参数
- 移除旧的通用优化算法相关方法
- 添加Dakota特定的参数访问方法
- 简化信号定义，只保留必要的parametersChanged和methodChanged

**OptimizeWidget.cpp**
- 完全重写实现，移除旧的滑块同步逻辑
- 实现Dakota方法描述更新功能
- 添加参数组可见性控制：根据选择的方法动态显示/隐藏相关参数组
- 更新连接设置，简化事件处理

#### 3. Dakota方法对应关系
- **pareto_set**: 多目标优化，使用Pareto前沿分析
- **conmin_frcg**: Fletcher-Reeves共轭梯度方法
- **conmin_mfd**: 可行方向法
- **soga**: 单目标遗传算法

#### 4. 界面特性
- **动态参数显示**: 根据选择的方法自动显示/隐藏相关参数组
- **Dakota兼容**: 所有参数直接对应Dakota的method配置选项
- **简化界面**: 移除了不必要的控制按钮和进度条，专注于参数配置

### 技术改进
1. **模块化设计**: 每种算法类型都有独立的参数组
2. **智能界面**: 动态显示相关参数，避免界面混乱
3. **Dakota集成**: 参数名称和选项直接对应Dakota文档
4. **代码简化**: 移除复杂的滑块同步逻辑，使用直接的数值输入

### Dakota配置示例

#### pareto_set方法
```dakota
method
  pareto_set
    multi_objective_weight_sets 10
    random_weight_sets
    max_iterations 1000
    max_function_evaluations 10000
    convergence_tolerance 1e-6
```

#### conmin_frcg方法
```dakota
method
  conmin_frcg
    max_iterations 1000
    max_function_evaluations 10000
    convergence_tolerance 1e-6
    gradient_tolerance 1e-4
    max_step 1.0
    speculative_gradient
```

#### conmin_mfd方法
```dakota
method
  conmin_mfd
    max_iterations 1000
    max_function_evaluations 10000
    convergence_tolerance 1e-6
    gradient_tolerance 1e-4
    max_step 1.0
```

#### soga方法
```dakota
method
  soga
    population_size 50
    max_iterations 1000
    max_function_evaluations 10000
    mutation_rate 0.08
    crossover_rate 0.8
    replacement_type elitist
```

### 修改完成状态

✅ OptimizeWidget.ui界面重新设计  
✅ OptimizeWidget.h头文件更新  
✅ OptimizeWidget.cpp实现重写  
✅ Dakota方法参数映射完成  
✅ 动态界面控制实现  

**修改日期：** 2024年12月19日  
**状态：** 已完成，OptimizeWidget专门针对Dakota优化方法重新设计

---

## 左侧树Widget右键菜单功能 (2024-12-19)

### 修改背景
用户要求为左侧树widget添加右键菜单，可以添加三种节点（Optimization、Sensitivity Analysis、Uncertainty Quantification），支持多个同类型节点创建、自动编号，以及节点移动功能。

### 实施内容

#### 1. 右键菜单设计
**菜单项目**
- **添加节点**
  - Add Optimization - 添加优化分析节点
  - Add Sensitivity Analysis - 添加敏感性分析节点  
  - Add Uncertainty Quantification - 添加不确定性量化节点
- **节点管理**
  - Delete - 删除选中的分析节点
  - Rename - 重命名选中的分析节点
- **节点移动**
  - Move Up - 向上移动节点
  - Move Down - 向下移动节点

#### 2. 自动编号功能
**命名规则**
- 第一个节点：使用基础名称（如"Optimization"）
- 后续节点：自动添加序号（如"Optimization 2", "Optimization 3"）
- 智能编号：检测现有节点的最大编号，新节点使用下一个编号

**实现特点**
- 支持不同类型节点独立编号
- 删除节点后不会重新编号（保持编号稳定性）
- 重命名不影响自动编号逻辑

#### 3. 节点移动功能
**移动规则**
- 只能移动根级别的分析节点（Optimization、Sensitivity Analysis、UQ）
- 不能移动系统节点（Solver）
- 移动后保持节点的展开状态和子节点结构

**移动限制**
- 第一个节点不能向上移动
- 最后一个节点不能向下移动
- 移动操作实时更新菜单项的可用状态

#### 4. 代码实现更新

**CustomTreeWidget.h**
- 更新右键菜单Action定义
- 添加新的槽函数声明
- 添加辅助方法：generateUniqueNodeName、canMoveNodeUp、canMoveNodeDown

**CustomTreeWidget.cpp**
- **createActions()**: 创建新的菜单动作，使用对应的图标
- **createMenus()**: 重新组织菜单结构，分组显示功能
- **setupConnections()**: 连接新的槽函数
- **contextMenuEvent()**: 智能启用/禁用菜单项
- **新增槽函数**:
  - onAddOptimization() - 添加优化节点
  - onAddSensitivityAnalysis() - 添加敏感性分析节点
  - onAddUncertaintyQuantification() - 添加UQ节点
  - onDeleteNode() - 删除节点（仅限分析节点）
  - onRenameNode() - 重命名节点
  - onMoveNodeUp() / onMoveNodeDown() - 节点移动

#### 5. 智能菜单控制
**动态启用/禁用**
- 添加操作：始终可用
- 删除操作：仅对分析节点可用，系统节点不可删除
- 重命名操作：仅对分析节点可用
- 移动操作：根据节点位置动态判断是否可移动

#### 6. 节点结构保持
**自动子节点创建**
- 新建的分析节点自动包含Input和Output子节点
- 保持与现有节点结构的一致性
- 新节点默认展开显示

### 使用流程

1. **添加节点**：右键空白区域或任意节点 → 选择"Add XXX" → 自动创建带编号的新节点
2. **删除节点**：右键分析节点 → 选择"Delete" → 确认删除
3. **重命名节点**：右键分析节点 → 选择"Rename" → 输入新名称
4. **移动节点**：右键分析节点 → 选择"Move Up/Down" → 节点位置调整

### 技术特点

1. **智能编号**：自动检测现有编号，生成唯一的节点名称
2. **动态菜单**：根据选中节点类型和位置智能启用菜单项
3. **结构保持**：移动和操作不破坏节点的层次结构
4. **用户友好**：清晰的图标和分组菜单，操作直观
5. **扩展性强**：易于添加新的节点类型和操作

### 修改完成状态

✅ CustomTreeWidget.h头文件更新  
✅ 右键菜单Action重新设计  
✅ 槽函数实现完成  
✅ 自动编号功能实现  
✅ 节点移动功能实现  
✅ 智能菜单控制实现  

**修改日期：** 2024年12月19日  
**状态：** 已完成，左侧树widget支持完整的右键菜单功能

---

## Solver节点结构优化 (2024-12-19)

### 修改背景
用户要求在solver节点内增加input和output节点，原有的打开.i文件生成的节点放入input节点下，以便更好地组织solver相关的内容。

### 实施内容

#### 1. Solver节点结构重新设计
**新的层次结构**
```
└── Solver
    ├── Input
    │   ├── Pipes (n)
    │   ├── Junctions (n)
    │   ├── Volumes (n)
    │   └── Branches (n)
    └── Output
```

**原有结构**
```
└── Solver
    ├── Pipes (n)
    ├── Junctions (n)
    ├── Volumes (n)
    └── Branches (n)
```

#### 2. 代码实现更新

**setupTreeStructure()方法**
- 在Solver节点下添加Input和Output子节点
- Input节点设置为默认展开状态
- 保持与其他分析节点的Input/Output结构一致性

**addComponentsToSolverNode()方法**
- 修改组件添加逻辑，将所有.i文件解析的组件添加到Solver/Input节点下
- 更新日志信息，明确指出组件添加到"Solver Input node"
- 确保Input节点自动展开显示组件分类

**setInputFilePath()方法**
- 修改清理逻辑，只清理Input节点下的内容，保留Output节点
- 避免删除Solver节点的基本结构（Input/Output节点）

**retranslate()方法**
- 添加对Solver下Input和Output节点的翻译支持
- 确保界面语言切换时正确更新节点名称

#### 3. 功能改进

**更好的内容组织**
- 输入相关内容（.i文件组件）放在Input节点下
- 为将来的输出内容预留Output节点
- 保持树结构的逻辑一致性

**用户体验优化**
- Input节点默认展开，方便查看组件
- 清理操作更精确，不影响节点结构
- 与分析节点的Input/Output结构保持一致

#### 4. 向后兼容性
- 保持所有现有的组件解析和显示功能
- 组件双击编辑功能不受影响
- 日志记录更加清晰和准确

### 技术特点

1. **结构化组织**：将solver相关内容按输入/输出分类
2. **逻辑一致性**：与分析节点的结构保持一致
3. **扩展性强**：为将来的输出功能预留空间
4. **用户友好**：更清晰的内容组织和导航

### 修改完成状态

✅ setupTreeStructure()方法更新  
✅ addComponentsToSolverNode()方法重构  
✅ setInputFilePath()方法优化  
✅ retranslate()方法扩展  
✅ 节点结构重新设计完成  

**修改日期：** 2024年12月19日  
**状态：** 已完成，Solver节点结构优化，支持Input/Output子节点组织

---

## 项目管理功能实现 (2024-12-19)

### 修改背景
原有系统启动时会自动加载默认的树结构，不符合实际项目管理需求。用户要求软件启动时左侧树为空，实现完整的项目管理功能，包括新建项目、项目目录管理和空项目状态。

### 修改目标

1. 软件启动时左侧树为空
2. 实现New Project功能，弹出工程名输入窗口  
3. 在exe目录下创建工程目录
4. 新建项目时只显示Solver节点
5. 其他节点通过右键菜单添加

### 实施内容

#### 1. 空项目状态实现

**文件：** `src/widgets/CustomTreeWidget.h/.cpp`

**新增方法声明：**
```cpp
// Project management
void createNewProject(const QString& projectName, const QString& projectPath);
void clearProject();
void setupEmptyProject();
```

**关键修改：**
- **构造函数更新**：调用`setupEmptyProject()`而不是`initTree()`
- **setupEmptyProject()方法**：清空树并重置所有节点指针为nullptr
- **clearProject()方法**：清空项目并返回空状态
- **createNewProject()方法**：创建新项目，只包含Solver节点及其Input/Output子节点

#### 2. 新建项目功能

**文件：** `src/ui/MainWindow.h/.cpp`

**新增成员变量：**
```cpp
// Project management
QString m_currentProjectName;
QString m_currentProjectPath;
```

**onNewProject()方法完整实现：**
1. **检查未保存更改**：调用maybeSave()确认保存状态
2. **获取项目名**：使用QInputDialog弹出输入框
3. **创建项目目录**：在exe目录下创建以项目名命名的文件夹
4. **处理目录冲突**：如果目录已存在，询问用户是否覆盖
5. **更新项目状态**：设置项目名称、路径，清空当前文件
6. **更新界面**：调用CustomTreeWidget的createNewProject()方法
7. **界面反馈**：更新窗口标题、清空编辑器、记录日志

#### 3. 项目目录管理

**目录结构设计：**
```
[exe目录]/
├── [项目名1]/
├── [项目名2]/
└── ...
```

**功能特点：**
- 自动在应用程序exe目录下创建项目文件夹
- 智能检查目录是否已存在
- 支持覆盖已存在的项目（用户确认）
- 完整的错误处理和用户提示
- 使用QDir::mkpath()确保目录创建成功

#### 4. 界面状态管理

**窗口标题动态更新：**
- 无项目时：`"Optimize Application - No Project"`
- 有项目时：`"Optimize Application - [项目名]"`
- 构造函数中设置默认标题为"No Project"

**文本编辑器内容管理：**
- **无项目状态**：显示"No project loaded. Use 'New Project' to create..."
- **项目加载后**：显示项目相关内容和操作指南
- **关闭项目后**：返回无项目提示状态

#### 5. 关闭项目功能

**onCloseProject()方法实现：**
1. **检查未保存更改**：确保数据安全
2. **清空项目信息**：重置项目名称、路径、当前文件
3. **清空界面**：调用CustomTreeWidget的clearProject()方法
4. **重置状态**：更新窗口标题为"No Project"
5. **用户反馈**：更新文本编辑器内容，记录操作日志

#### 6. 初始项目结构

**新建项目时的树结构：**
```
[项目名]
└── Solver
    ├── Input
    └── Output
```

**设计特点：**
- 只创建Solver节点，其他分析节点需要用户手动添加
- Solver节点包含Input和Output子节点，保持结构一致性
- 树标题显示为项目名称而不是"Project Explorer"
- Solver节点默认展开，方便用户操作

#### 7. 必要的头文件添加

**MainWindow.cpp中添加：**
```cpp
#include <QInputDialog>
#include <QLineEdit>
#include <QDir>
#include <QCoreApplication>
```

### 工作流程

#### 软件启动流程
1. **初始状态**
   - 左侧树完全为空，显示"Project Explorer"
   - 主工作区显示"No project loaded"提示
   - 窗口标题显示"Optimize Application - No Project"

#### 新建项目流程
1. **用户操作**：点击Ribbon界面的"New Project"按钮
2. **输入项目名**：弹出QInputDialog对话框
3. **验证和创建**：
   - 检查项目名是否为空
   - 在exe目录下创建项目文件夹
   - 处理目录已存在的情况
4. **界面更新**：
   - 树标题更改为项目名
   - 只显示Solver节点（包含Input/Output）
   - 窗口标题更新为"Optimize Application - [项目名]"
   - 记录操作日志

#### 添加分析节点流程
1. **右键操作**：在树的空白区域右键
2. **选择节点类型**：Add Optimization/Sensitivity Analysis/UQ
3. **自动创建**：系统创建相应节点及其Input/Output子节点
4. **智能编号**：如果同类型节点已存在，自动添加序号

#### 关闭项目流程
1. **用户操作**：点击"Close Project"按钮
2. **保存检查**：检查是否有未保存的更改
3. **清空状态**：
   - 清空树结构
   - 重置项目信息
   - 更新界面为无项目状态
4. **用户反馈**：更新标题和提示信息

### 技术特点

1. **状态管理**：完整的项目状态跟踪和管理
2. **用户体验**：直观的界面反馈和操作提示
3. **数据安全**：未保存更改的检查和确认机制
4. **错误处理**：完善的异常情况处理和用户提示
5. **扩展性**：为将来的项目文件保存/加载功能预留接口
6. **一致性**：与现有右键菜单功能完美集成

### 代码修改总结

#### CustomTreeWidget类
- **新增方法**：setupEmptyProject(), clearProject(), createNewProject()
- **构造函数修改**：启动时调用setupEmptyProject()
- **状态管理**：完整的节点指针重置和管理

#### MainWindow类  
- **新增成员变量**：m_currentProjectName, m_currentProjectPath
- **方法实现**：onNewProject(), onCloseProject()完整实现
- **界面管理**：窗口标题、文本内容的动态更新
- **构造函数修改**：初始化项目管理变量，设置默认标题

### 修改完成状态

✅ CustomTreeWidget空项目状态实现  
✅ 新建项目功能完整实现  
✅ 项目目录创建和管理  
✅ 界面状态动态更新  
✅ 关闭项目功能实现  
✅ 错误处理和用户提示  
✅ 与现有功能集成测试  

**修改日期：** 2024年12月19日  
**状态：** 已完成，实现完整的项目管理功能，软件启动时为空项目状态

---

## XML项目保存功能实现 (2025-06-13 16:10:00)

### 修改背景
用户要求实现点击上方保存按钮的功能，能够将当前界面的Project内容全部保存，包括treewidget内的节点层级和对应Parameters内的参数界面里的内容，工程文件格式为XML。

### 修改目标

1. 实现完整的项目保存功能
2. 保存树结构和节点层级关系
3. 保存所有参数界面的配置内容
4. 使用XML格式存储项目文件
5. 实现项目加载功能

### 实施内容

#### 1. XML保存核心功能

**文件：** `src/ui/MainWindow.h/.cpp`

**新增头文件包含：**
```cpp
#include <QXmlStreamWriter>
#include <QXmlStreamReader>
```

**新增方法声明：**
```cpp
// Project file management
bool saveProjectToXML(const QString& filePath);
bool loadProjectFromXML(const QString& filePath);
void saveTreeNodeToXML(QXmlStreamWriter& writer, QTreeWidgetItem* item);
void saveParametersToXML(QXmlStreamWriter& writer);
QTreeWidgetItem* loadTreeNodeFromXML(QXmlStreamReader& reader, QTreeWidgetItem* parent = nullptr);
void loadParametersFromXML(QXmlStreamReader& reader);
```

#### 2. onSaveFile方法完整实现

**功能流程：**
1. **项目检查**：验证是否有当前项目
2. **文件路径生成**：在项目目录下创建 `[项目名].xml` 文件
3. **XML保存**：调用 `saveProjectToXML()` 方法
4. **状态更新**：更新文件状态、窗口标题、日志记录
5. **用户反馈**：状态栏提示和错误处理

**代码实现：**
```cpp
void MainWindow::onSaveFile() 
{
    // Check if we have a current project
    if (m_currentProjectName.isEmpty() || m_currentProjectPath.isEmpty()) {
        QMessageBox::warning(this, tr("No Project"), 
                           tr("Please create or open a project first."));
        return;
    }
    
    // Generate project file path
    QString projectFileName = m_currentProjectName + ".xml";
    QString projectFilePath = QDir(m_currentProjectPath).absoluteFilePath(projectFileName);
    
    // Save project to XML with complete error handling
    if (saveProjectToXML(projectFilePath)) {
        m_currentFile = projectFilePath;
        m_isModified = false;
        // Update UI and log success
    } else {
        // Handle save error
    }
}
```

#### 3. XML文件结构设计

**完整的XML架构：**
```xml
 <OptimizeProject version="1.0" name="项目名" created="2025-06-13T16:10:00">
  <ProjectInfo>
    <Name>项目名称</Name>
    <Path>项目路径</Path>
    <LastModified>最后修改时间</LastModified>
  </ProjectInfo>
  
  <TreeStructure>
    <Node text="节点名" type="节点类型ID" expanded="true/false" moduleType="模块类型">
      <ComponentData>
        <ComponentID>组件ID</ComponentID>
        <ComponentName>组件名称</ComponentName>
        <ComponentType>组件类型</ComponentType>
        <!-- 组件特定数据 -->
        <Volumes>
          <Volume>体积值1</Volume>
          <Volume>体积值2</Volume>
        </Volumes>
        <Lengths>
          <Length>长度值1</Length>
          <Length>长度值2</Length>
        </Lengths>
      </ComponentData>
      <!-- 递归子节点 -->
      <Node text="子节点" type="类型" expanded="true">
        <!-- 子节点内容 -->
      </Node>
    </Node>
  </TreeStructure>
  
  <Parameters>
    <OptimizationParameters>
      <Method>Dakota方法名</Method>
      <MaxIterations>最大迭代次数</MaxIterations>
      <MaxFunctionEvaluations>最大函数评估次数</MaxFunctionEvaluations>
      <ConvergenceTolerance>收敛容差</ConvergenceTolerance>
      <PopulationSize>种群大小</PopulationSize>
      <MutationRate>变异率</MutationRate>
      <CrossoverRate>交叉率</CrossoverRate>
      <ReplacementType>替换类型</ReplacementType>
      <MultiObjectiveWeightSets>多目标权重集</MultiObjectiveWeightSets>
      <RandomWeightSetsEnabled>随机权重集启用</RandomWeightSetsEnabled>
      <GradientTolerance>梯度容差</GradientTolerance>
      <MaxStepSize>最大步长</MaxStepSize>
      <SpeculativeGradientEnabled>推测梯度启用</SpeculativeGradientEnabled>
      <VerboseOutputEnabled>详细输出启用</VerboseOutputEnabled>
      <DebugOutputEnabled>调试输出启用</DebugOutputEnabled>
      <QuietModeEnabled>安静模式启用</QuietModeEnabled>
    </OptimizationParameters>
    
    <SensitivityParameters>
      <Method>敏感性分析方法</Method>
      <Samples>采样数量</Samples>
      <SamplingType>采样类型</SamplingType>
      <VarianceBasedDecomp>方差分解启用</VarianceBasedDecomp>
      <ConvergenceTolerance>收敛容差</ConvergenceTolerance>
      <Seed>随机种子</Seed>
    </SensitivityParameters>
    
    <UQParameters>
      <Method>不确定性量化方法</Method>
      <Samples>采样数量</Samples>
      <SamplingType>采样类型</SamplingType>
      <PolynomialOrder>多项式阶数</PolynomialOrder>
      <ExpansionType>展开类型</ExpansionType>
      <ConvergenceTolerance>收敛容差</ConvergenceTolerance>
      <Seed>随机种子</Seed>
      <AdaptiveRefinement>自适应细化启用</AdaptiveRefinement>
    </UQParameters>
  </Parameters>
</OptimizeProject>
```

#### 4. 树结构保存实现

**saveTreeNodeToXML方法特点：**
- **递归保存**：完整保存树的层次结构
- **节点属性**：保存文本、类型、展开状态、模块类型
- **组件数据**：保存Pipe、Junction等组件的详细数据
- **数据完整性**：保存所有UserRole数据

**支持的组件类型：**
- **Pipe组件**：ComponentID、ComponentName、NumberOfVolumes、Volumes数组、Lengths数组
- **Junction组件**：ComponentID、ComponentName、FromComponent、ToComponent、Area、ForwardLoss、ReverseLoss
- **扩展性**：易于添加新的组件类型支持

#### 5. 参数保存实现

**saveParametersToXML方法功能：**

**优化参数保存：**
- Dakota方法配置（pareto_set、conmin_frcg、conmin_mfd、soga）
- 迭代和评估限制参数
- 收敛控制参数
- SOGA算法特定参数（种群大小、变异率、交叉率、替换类型）
- Pareto Set参数（多目标权重集、随机权重集）
- CONMIN参数（梯度容差、最大步长、推测梯度）
- 输出控制参数（详细输出、调试输出、安静模式）

**敏感性分析参数保存：**
- 分析方法（sampling、local_reliability、polynomial_chaos、stoch_collocation）
- 采样配置（采样数、采样类型、随机种子）
- 方差分解设置
- 收敛控制参数

**不确定性量化参数保存：**
- UQ方法（sampling、polynomial_chaos、stoch_collocation、reliability分析）
- 采样参数配置
- 多项式展开设置（阶数、展开类型）
- 自适应细化选项
- 收敛和种子参数

#### 6. 项目加载功能

**loadProjectFromXML方法实现：**
1. **文件解析**：使用QXmlStreamReader解析XML文件
2. **项目信息恢复**：恢复项目名称、路径、文件状态
3. **树结构重建**：递归重建完整的树结构
4. **参数恢复**：恢复所有Widget的参数设置
5. **界面更新**：更新窗口标题、树标题、状态信息

**loadTreeNodeFromXML方法特点：**
- **节点重建**：根据保存的属性重建QTreeWidgetItem
- **类型恢复**：恢复NodeType和图标设置
- **指针管理**：正确设置CustomTreeWidget的成员指针
- **展开状态**：恢复节点的展开/折叠状态
- **组件数据**：恢复组件的详细数据（当前简化处理）

**loadParametersFromXML方法功能：**
- **参数映射**：将XML数据映射到Widget的setter方法
- **类型转换**：正确处理字符串到数值和布尔值的转换
- **Widget更新**：调用各个Widget的setter方法恢复参数
- **错误处理**：跳过无效或缺失的参数

#### 7. Open Project功能

**onOpenProject方法实现：**
1. **未保存检查**：检查当前项目的未保存更改
2. **文件选择**：打开文件对话框选择XML项目文件
3. **项目加载**：调用loadProjectFromXML方法
4. **界面更新**：更新文本编辑器内容和状态信息
5. **错误处理**：处理加载失败的情况

#### 8. CustomTreeWidget扩展

**文件：** `src/widgets/CustomTreeWidget.h`

**新增公共方法：**
```cpp
// Node pointer management for loading
void setSolverItem(QTreeWidgetItem* item) { m_solverItem = item; }
void setOptimizeItem(QTreeWidgetItem* item) { m_optimizeItem = item; }
void setSensitivityItem(QTreeWidgetItem* item) { m_sensitivityItem = item; }
void setUQItem(QTreeWidgetItem* item) { m_uqItem = item; }
void setInputItem(QTreeWidgetItem* item) { m_inputItem = item; }
void setOutputItem(QTreeWidgetItem* item) { m_outputItem = item; }
```

**功能说明：**
- 为XML加载功能提供设置内部指针的接口
- 确保加载后的树结构指针正确性
- 维护与现有功能的兼容性

### 技术特点

1. **数据完整性**：保存项目的所有状态信息，包括树结构、节点属性、组件数据和参数配置
2. **XML标准化**：使用标准XML格式，具有良好的可读性和扩展性
3. **递归处理**：完整处理树的层次结构，支持任意深度的节点嵌套
4. **类型安全**：正确处理不同数据类型的序列化和反序列化
5. **错误处理**：完善的文件操作和XML解析错误处理机制
6. **用户体验**：提供清晰的保存/加载反馈和错误提示
7. **向后兼容**：保持与现有项目管理功能的完全兼容
8. **扩展性强**：易于添加新的参数类型和组件数据支持

### 使用流程

#### 保存项目流程
1. **创建/打开项目**：确保有当前活动项目
2. **配置参数**：在各个参数界面中设置所需参数
3. **添加组件**：通过右键菜单添加分析节点，加载Solver组件
4. **点击保存**：点击Ribbon界面的"Save"按钮
5. **自动保存**：系统在项目目录下生成 `[项目名].xml` 文件
6. **状态更新**：界面显示保存成功，文件状态更新为已保存

#### 加载项目流程
1. **打开项目**：点击"Open Project"按钮
2. **选择文件**：在文件对话框中选择XML项目文件
3. **自动加载**：系统解析XML文件并重建项目状态
4. **界面恢复**：树结构、参数设置、窗口标题全部恢复
5. **继续工作**：可以继续编辑项目或进行新的配置

#### 项目文件管理
- **文件位置**：项目文件保存在项目目录下，文件名为 `[项目名].xml`
- **文件格式**：标准XML格式，可以用文本编辑器查看
- **版本控制**：XML文件包含版本信息，便于将来的格式升级
- **备份建议**：用户可以复制XML文件进行备份

### 代码修改总结

#### MainWindow类
- **新增方法**：saveProjectToXML(), loadProjectFromXML(), saveTreeNodeToXML(), saveParametersToXML(), loadTreeNodeFromXML(), loadParametersFromXML()
- **方法实现**：onSaveFile()完整实现, onOpenProject()完整实现
- **头文件添加**：QXmlStreamWriter, QXmlStreamReader
- **错误处理**：完善的文件操作和XML解析错误处理

#### CustomTreeWidget类
- **新增方法**：setSolverItem(), setOptimizeItem(), setSensitivityItem(), setUQItem(), setInputItem(), setOutputItem()
- **功能扩展**：为XML加载提供内部指针设置接口
- **兼容性**：保持与现有功能的完全兼容

#### XML文件结构
- **根元素**：OptimizeProject，包含版本和项目基本信息
- **项目信息**：ProjectInfo节，包含名称、路径、修改时间
- **树结构**：TreeStructure节，递归保存完整的节点层次
- **参数配置**：Parameters节，分别保存三种分析类型的参数

### 修改完成状态

✅ XML保存核心功能实现  
✅ onSaveFile方法完整实现  
✅ 树结构递归保存功能  
✅ 参数配置完整保存功能  
✅ XML文件结构设计完成  
✅ 项目加载功能实现  
✅ onOpenProject方法实现  
✅ CustomTreeWidget扩展完成  
✅ 错误处理和用户反馈  
✅ 与现有功能集成测试  

**修改日期：** 2025年6月13日 16:10:00  
**状态：** 已完成，实现完整的XML项目保存和加载功能，支持树结构和参数的完整保存 

---

## SolverOutputWidget创建与集成 (2024-12-19)

### 修改背景

为了更好地区分Solver节点下的Output配置和其他Output节点的配置，需要创建一个专门的SolverOutputWidget类，提供更丰富的Solver输出配置选项。

### 修改目标

1. 创建新的SolverOutputWidget类，风格与OutputWidget一致
2. 在点击Solver下的Output节点时，显示SolverOutputWidget页面
3. 提供专门的Solver输出配置功能，包括文件管理、报告生成等

### 实施方案

#### 1. SolverOutputWidget类创建

**文件创建：**
- `src/widgets/SolverOutputWidget.h` - 头文件，包含完整的类声明
- `src/widgets/SolverOutputWidget.cpp` - 实现文件，包含所有方法的完整实现  
- `src/widgets/SolverOutputWidget.ui` - UI界面文件，包含丰富的用户界面

**主要功能模块：**

##### 输出目录配置
- 输出目录选择（带Browse按钮）
- 文件名模式设置（支持模式字符串）
- 输出格式选择：Dakota (.dat)、CSV (.csv)、JSON (.json)、XML (.xml)

##### 自动保存设置
- 启用/禁用自动保存复选框
- 保存间隔设置（10-3600秒，默认60秒）
- 间隔控件的智能启用/禁用

##### 压缩设置
- 启用/禁用压缩功能
- 压缩级别选择：Low、Medium、High
- 压缩级别控件的智能启用/禁用

##### 报告设置
- 启用/禁用报告生成
- 报告格式选择：HTML、PDF、Text
- 包含图表选项
- 包含统计信息选项
- 报告相关控件的智能启用/禁用

##### 输出变量管理
- 表格形式显示输出变量（变量名、类型、描述）
- 添加变量功能（支持Scalar、Vector、Matrix、Function类型）
- 更新变量功能
- 删除变量功能（带确认对话框）
- 表格选择状态管理

**关键方法：**
```cpp
// 输出目录和文件设置
QString getOutputDirectory() const;
QString getFileNamePattern() const;
QString getOutputFormat() const;
void setOutputDirectory(const QString &directory);
void setFileNamePattern(const QString &pattern);
void setOutputFormat(const QString &format);

// 自动保存设置
bool isAutoSaveEnabled() const;
int getAutoSaveInterval() const;
void setAutoSaveEnabled(bool enabled);
void setAutoSaveInterval(int interval);

// 压缩设置
bool isCompressionEnabled() const;
QString getCompressionLevel() const;
void setCompressionEnabled(bool enabled);
void setCompressionLevel(const QString &level);

// 报告设置
bool isReportEnabled() const;
QString getReportFormat() const;
bool includeCharts() const;
bool includeStatistics() const;

// 输出变量管理
void addOutputVariable(const QString &name, const QString &type, const QString &description);
void updateOutputVariable(int row, const QString &name, const QString &type, const QString &description);
void deleteOutputVariable(int row);
QStringList getOutputVariableNames() const;
```

#### 2. ParamWidget集成

**文件修改：** `src/widgets/ParamWidget.h/.cpp`

**新增内容：**
- 添加SolverOutputWidget前置声明
- 添加`m_solverOutputWidget`成员变量
- 添加`showSolverOutputParams()`方法
- 添加`getSolverOutputWidget()`getter方法
- 在构造函数中创建SolverOutputWidget实例
- 在setupConnections中连接参数变化信号

**关键代码：**
```cpp
// 头文件声明
class SolverOutputWidget;
void showSolverOutputParams();
SolverOutputWidget* getSolverOutputWidget() const { return m_solverOutputWidget; }

// 实现文件
#include "SolverOutputWidget.h"
m_solverOutputWidget = new SolverOutputWidget(this);
m_stackedWidget->addWidget(m_solverOutputWidget);
connect(m_solverOutputWidget, &SolverOutputWidget::parametersChanged,
        this, &ParamWidget::parametersChanged);
```

#### 3. MainWindow智能节点识别

**文件修改：** `src/ui/MainWindow.h/.cpp`

**新增功能：**
- 添加SolverOutputWidget前置声明
- 添加参数保存和加载方法声明
- 在`onTreeItemChanged()`中实现智能节点识别逻辑

**智能识别逻辑：**
```cpp
case NodeType::Output:
    // 检查Output节点是否在Solver节点下
    if (parentItem && m_treeWidget->getNodeType(parentItem) == NodeType::Solver) {
        // 这是Solver/Output，显示SolverOutputWidget
        m_paramWidget->showSolverOutputParams();
        if (m_paramWidget->getSolverOutputWidget()) {
            loadSolverOutputParametersToWidget(currentItem, m_paramWidget->getSolverOutputWidget());
        }
    } else {
        // 这是常规Output节点，显示OutputWidget
        m_paramWidget->showOutputParams();
        if (m_paramWidget->getOutputWidget()) {
            loadOutputParametersToWidget(currentItem, m_paramWidget->getOutputWidget());
        }
    }
    break;
```

**参数管理方法：**
```cpp
// 保存SolverOutputWidget参数到树节点 (使用Qt::UserRole + 80-99)
void saveSolverOutputParametersToNode(QTreeWidgetItem* item, SolverOutputWidget* widget);

// 从树节点加载参数到SolverOutputWidget
void loadSolverOutputParametersToWidget(QTreeWidgetItem* item, SolverOutputWidget* widget);
```

**数据存储策略：**
- 使用Qt::UserRole + 80-99范围存储SolverOutputWidget参数
- 避免与其他Widget的数据存储冲突
- 支持输出变量列表的保存和恢复

#### 4. 构建系统更新

**文件修改：** `OptimizeApplication.pro`

**添加内容：**
```pro
SOURCES += \
    # ... 现有文件 ...
    src/widgets/SolverOutputWidget.cpp \

HEADERS += \
    # ... 现有文件 ...
    src/widgets/SolverOutputWidget.h \

FORMS += \
    # ... 现有文件 ...
    src/widgets/SolverOutputWidget.ui \
```

### UI设计特点

#### 界面风格一致性
- 与OutputWidget保持一致的设计风格
- 使用QGroupBox分组组织功能模块
- 按钮使用颜色编码（绿色添加、蓝色更新、红色删除）
- 响应式布局，支持不同窗口大小

#### 用户体验优化
- **智能控件状态管理**：根据复选框状态自动启用/禁用相关控件
- **文件对话框集成**：Browse按钮支持目录选择，默认路径为Documents/OptimizeApplication/Output
- **输入验证**：输入对话框支持变量类型选择和描述输入
- **操作确认**：删除操作提供确认对话框防止误操作
- **表格交互**：支持行选择，Update和Delete按钮根据选择状态智能启用

#### 默认值设置
- 输出目录：`Documents/OptimizeApplication/Output`
- 文件名模式：`result_%1`
- 输出格式：Dakota (.dat)
- 自动保存：启用，间隔60秒
- 压缩：禁用，级别Medium
- 报告：启用，HTML格式，包含图表和统计信息

### 技术实现特点

#### 模块化设计
- 清晰的方法分组（输出设置、自动保存、压缩、报告、变量管理）
- 独立的信号槽连接管理
- 可扩展的参数配置架构

#### 数据管理
- 完整的参数保存和恢复功能
- 输出变量的动态管理
- 参数变化时的自动通知机制

#### 交互逻辑
- 智能的父节点检测，区分Solver/Output和其他Output节点
- 参数变化时自动保存到树节点数据
- 节点切换时的参数自动加载

### 使用流程

1. **访问Solver输出配置**：在树结构中点击Solver节点下的Output
2. **配置输出目录**：设置输出目录、文件名模式和格式
3. **设置自动保存**：配置自动保存选项和间隔
4. **配置压缩选项**：选择是否启用压缩及压缩级别
5. **设置报告生成**：配置报告格式和包含内容
6. **管理输出变量**：添加、更新或删除需要监控的输出变量
7. **参数自动保存**：所有配置自动保存到项目文件中

### 扩展性设计

#### 预留功能扩展
- 支持更多输出格式的添加
- 可扩展的变量类型定义
- 更多报告选项的支持
- 高级压缩算法的集成

#### 界面扩展
- 可添加更多配置分组
- 支持高级输出过滤选项
- 可集成实时预览功能

### 修改完成状态

✅ SolverOutputWidget类创建完成  
✅ UI界面设计完成  
✅ 完整功能实现完成  
✅ ParamWidget集成完成  
✅ MainWindow智能识别逻辑完成  
✅ 参数保存和加载机制完成  
✅ 构建系统配置更新完成  

**修改日期：** 2024年12月19日  
**状态：** 已完成，功能正常

### 测试验证要点

1. **节点识别测试**：验证点击不同Output节点时显示正确的Widget
2. **参数保存测试**：验证参数在节点切换时正确保存和加载
3. **UI交互测试**：验证所有控件的启用/禁用逻辑
4. **输出变量管理测试**：验证变量的添加、更新、删除功能
5. **文件对话框测试**：验证Browse按钮和目录选择功能
6. **参数持久化测试**：验证项目保存和加载时参数的正确性

--- 

**最后更新：** 2025年07月14日 16:48:25
