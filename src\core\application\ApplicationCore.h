#ifndef APPLICATIONCORE_H
#define APPLICATIONCORE_H

#include <QObject>
#include <QMap>
#include <QString>
#include <memory>

class ConfigManager;
class ThemeManager;
class LogManager;
class EventBus;
class PluginManager;
class ServiceLocator;

/**
 * @brief 应用程序核心类，作为应用程序的中心控制器
 * 
 * ApplicationCore提供了一个单例实例，负责管理应用程序的核心服务，
 * 包括配置管理、主题管理、日志管理、事件系统和插件系统。
 */
class ApplicationCore : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取ApplicationCore的单例实例
     * @return ApplicationCore的单例实例指针
     */
    static ApplicationCore* instance();

    /**
     * @brief 初始化应用程序核心
     * @return 初始化是否成功
     */
    bool initialize();

    /**
     * @brief 清理应用程序资源
     */
    void cleanup();

    /**
     * @brief 获取配置管理器
     * @return 配置管理器指针
     */
    ConfigManager* configManager() const;

    /**
     * @brief 获取主题管理器
     * @return 主题管理器指针
     */
    ThemeManager* themeManager() const;

    /**
     * @brief 获取日志管理器
     * @return 日志管理器指针
     */
    LogManager* logManager() const;

    /**
     * @brief 获取事件总线
     * @return 事件总线指针
     */
    EventBus* eventBus() const;

    /**
     * @brief 获取插件管理器
     * @return 插件管理器指针
     */
    PluginManager* pluginManager() const;

    /**
     * @brief 检查应用程序是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const;

    /**
     * @brief 获取应用程序版本
     * @return 应用程序版本字符串
     */
    QString version() const;

    /**
     * @brief 获取应用程序名称
     * @return 应用程序名称字符串
     */
    QString applicationName() const;

signals:
    /**
     * @brief 应用程序初始化完成信号
     */
    void initialized();

    /**
     * @brief 应用程序即将退出信号
     */
    void aboutToQuit();

private:
    /**
     * @brief 构造函数（私有，确保单例模式）
     */
    ApplicationCore();

    /**
     * @brief 析构函数
     */
    ~ApplicationCore();

    // 禁止拷贝和赋值
    ApplicationCore(const ApplicationCore&) = delete;
    ApplicationCore& operator=(const ApplicationCore&) = delete;

    // 服务实例
    std::unique_ptr<ConfigManager> m_configManager;
    std::unique_ptr<ThemeManager> m_themeManager;
    std::unique_ptr<LogManager> m_logManager;
    std::unique_ptr<EventBus> m_eventBus;
    std::unique_ptr<PluginManager> m_pluginManager;
    std::unique_ptr<ServiceLocator> m_serviceLocator;

    // 应用程序状态
    bool m_initialized;
    QString m_version;
    QString m_applicationName;

    // 单例实例
    static ApplicationCore* s_instance;
};

#endif // APPLICATIONCORE_H
