﻿/* 
start ribbon set
dark style theme 2
参考AutoCAD 2021的界面配色

深色背景颜色:#192430
浅色背景颜色:#344253
更浅一层背景颜色:#4d5973 一般是控件的编辑区域颜色
字体颜色:#dadada
按钮背景颜色：#344253
按钮hover背景:#435c76
选中边框颜色:#2a8db5
*/

/*SARibbonBar*/
SARibbonBar{
  background-color: #192430;
  border: solid #192430;
  color:#dadada;
}

/*SARibbonQuickAccessBar*/
SARibbonQuickAccessBar{
   background-color: transparent;
}


/*SARibbonCtrlContainer*/
SARibbonCtrlContainer {
  background-color: transparent;
}

/*
SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
*/
SARibbonSeparatorWidget {
  color: #5d636a;
}

/*SARibbonCategory*/
SARibbonCategory {
  background-color: #192430;
  /*顶部有一条线*/
  border-top:1px solid #344253;
}
SARibbonCategory:focus {
  outline: none;
}

/*
SARibbonCategory下的SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
*/
SARibbonCategory > SARibbonSeparatorWidget {
  color: transparent;
}

/*SARibbonPannel*/

SARibbonPannel {
  background-color: #344253;
  border: none;
}

/*
SARibbonPannel下的SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
*/
SARibbonPannel > SARibbonSeparatorWidget {
  color: #5d636a;
}

/*SARibbonPannelLabel Pannel下标题栏*/
SARibbonPannelLabel {
    background-color: #4d5973;
    color: #dadada;
}

/*SARibbonPannelOptionButton*/
SARibbonPannelOptionButton {
  border:none;
  background-color: transparent;
  color:#dadada;
}

SARibbonPannelOptionButton:hover {
  background-color:#375d77;
}

/*SARibbonButtonGroupWidget*/
SARibbonButtonGroupWidget{
  background-color: transparent;
  color: #dadada;
}

SARibbonPannel > SARibbonButtonGroupWidget {
  border: 1px solid #2a8db5;
  background-color: transparent;
}

/*SARibbonApplicationButton*/
SARibbonApplicationButton{
  color:#dadada;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  background-color: #344253;
}

SARibbonApplicationButton:hover{
  background-color: #435c76;
}

SARibbonApplicationButton:pressed{
  background-color: #435c76;
}

SARibbonApplicationButton:focus{
  outline: none;
}

SARibbonApplicationButton::menu-indicator {
  /*subcontrol-position: right;*/
  width:0px;
}

/*SARibbonTabBar*/
SARibbonTabBar{
    background-color: transparent;
}

SARibbonTabBar::tab {
  color: #afafa6;
  border:1px solid transparent;/*加上边框，否则选中后加入边框后会有1px的偏移*/
  background: transparent;
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 5px;
  margin-bottom: 0px;
  min-width: 50px;
  padding-left: 5px;
  padding-right: 5px;
}

SARibbonTabBar::tab:selected {
  color: #dadada;
  background-color: #344253;
}

SARibbonTabBar::tab:hover:!selected {
  /*border:1px solid #344253;*/
  border-top:1px solid #344253;
  border-right:1px solid #344253;
  border-left:1px solid #344253;
  border-bottom:1px solid transparent;
  color: white;
}

SARibbonTabBar::tab:!selected {
  color: #afafa6;
}

/*SARibbonCheckBox*/
SARibbonCheckBox{
    color:#dadada;
    background-color:transparent;
}

/*SARibbonToolButton*/

SARibbonToolButton{
    border:none;
    color:#dadada;
    background-color:#344253;
} 

SARibbonToolButton:focus {
  border: 1px solid #2a8db5;
}
SARibbonToolButton:pressed{
    color:#dadada;
    background-color: #435c76;
}
SARibbonToolButton:checked{
    color:#dadada;
    border: 1px solid #2a8db5;
    background-color: #435c76;
}
SARibbonToolButton:hover {
    color:#dadada;
    background-color: #435c76;
}

/*SARibbonControlToolButton*/

SARibbonControlToolButton {
  background-color: transparent;/*{ControlToolButton.BKColor}*/
  color: #dadada;
  border: 1px solid transparent;
}

SARibbonControlToolButton:pressed {
  background-color: #435c76;/*{ControlToolButton.BKColor:pressed}*/
}

SARibbonControlToolButton:checked {
  border: 1px solid #2a8db5;/*{ControlToolButton.BorderColor:checked}*/
  background-color: #435c76;/*{ControlToolButton.BKColor:checked}*/
}

SARibbonControlToolButton:hover {
  color: #dadada;
  border: 1px solid transparent;/*{ControlToolButton.BorderColor:hover}*/
  background-color: #435c76;/*{ControlToolButton.BKColor:hover}*/
}

/*SARibbonControlButton*/
SARibbonControlButton{
  background-color:transparent;
  border: none;
  color:#dadada;
}
SARibbonControlButton:pressed{
  background-color: #435c76;
}
SARibbonControlButton:checked{
  border: 1px solid #2a8db5;
  background-color: #435c76;
}
SARibbonControlButton:hover {
  border: 1px solid transparent;
  background-color: #344253;
 }

SARibbonGalleryButton{
  background-color:#344253;
  border: 1px solid #435c76;
  color: #dadada;
}


/*SARibbonMenu*/
SARibbonMenu {
  color:#dadada;
  background-color: #4d5973;

}
SARibbonMenu::item {
  padding: 5px;
  background-color: #344253;
}
SARibbonMenu::item:selected {
  border: 1px solid #2a8db5;
  background-color: #435c76;
}
SARibbonMenu::item:hover {
    color:#dadada;
    border: 1px solid transparent;
    background-color: #435c76;
}
SARibbonMenu::icon{
  margin-left: 1px;
}

/*SARibbonGallery*/
SARibbonGallery {
  background-color: #4d5973;
  color: #dadada;
  border: 1px solid #888888;
}

/*SARibbonGalleryGroup*/
SARibbonGalleryGroup {
  show-decoration-selected: 1;
  background-color: transparent;
  color: #dadada;
}
SARibbonGalleryGroup::item {
  color: #dadada;
}
SARibbonGalleryGroup::item:selected {
  background-color: #435c76;
  border: 1px solid #2a8db5;
}
SARibbonGalleryGroup::item:hover {
  background-color: #435c76;
  border: 1px solid transparent;
}


/*RibbonGalleryViewport*/

SARibbonGalleryViewport {
  background-color: #4d5973;
}

/*SARibbonLineEdit*/
SARibbonLineEdit {
  border:1px solid #192430;
  background-color: #4d5973;
  color: #dadada;
  selection-background-color: #2a8db5;
  selection-color: #dadada;
}


/*SARibbonComboBox*/
SARibbonComboBox {
  color : #dadada;
  border: 1px solid #192430;
  background-color: #344253;
}

SARibbonComboBox:hover{
  border: 1px solid #2a8db5;
}

SARibbonComboBox:editable {
  color : #dadada;
  background-color: #4d5973;
  selection-background-color: #2a8db5;
  selection-color: #dadada;
}

SARibbonComboBox::drop-down {
  subcontrol-origin: padding;
  subcontrol-position: top right;
  width: 1em;
  border: none;
}

SARibbonComboBox::drop-down:hover {
  border: none;
  background-color: #2a8db5;
}

SARibbonComboBox::down-arrow {
  image: url(:/SARibbon/image/resource/ArrowDown.png);
}




/*SARibbonCategoryScrollButton*/
SARibbonCategoryScrollButton {
  border: 1px solid #2a8db5;
  color: #dadada;
  background-color: #344253;
}

SARibbonCategoryScrollButton[arrowType="3"] {
  border-right-width: 1px;
}

SARibbonCategoryScrollButton[arrowType="4"] {
  border-left-width: 1px;
}
/*SARibbonSystemToolButton*/

SARibbonSystemToolButton {
  background-color: transparent;
  border:none;
}

SARibbonSystemToolButton:focus {
  outline: none;
}

/* 深色模式的系统按钮设置 */
/*Min*/
SARibbonSystemToolButton#SAMinimizeWindowButton {
  background-position:center;
  background-repeat: no-repeat;
  background-image: url(:/SARibbon/image/resource/Titlebar_Min_Hover.svg);
}
SARibbonSystemToolButton#SAMinimizeWindowButton:hover{
  background-color: #b2b2b2;
}
SARibbonSystemToolButton#SAMinimizeWindowButton:pressed{
  background-color: #cacacb;
}
/*Max*/
SARibbonSystemToolButton#SAMaximizeWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/SARibbon/image/resource/Titlebar_Max_Hover.svg);
}
SARibbonSystemToolButton#SAMaximizeWindowButton:hover {
    background-color: #b2b2b2;
}
SARibbonSystemToolButton#SAMaximizeWindowButton:checked {
    background-image: url(:/SARibbon/image/resource/Titlebar_Normal_Hover.svg) center;
}


/*Close*/
SARibbonSystemToolButton#SACloseWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/SARibbon/image/resource/Titlebar_Close_Hover.svg);
}
SARibbonSystemToolButton#SACloseWindowButton:hover {
  background-color: #e81123;
}
SARibbonSystemToolButton#SACloseWindowButton:pressed {
    background-color: #f1707a;
}
