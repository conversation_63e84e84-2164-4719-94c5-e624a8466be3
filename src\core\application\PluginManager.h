#ifndef PLUGINMANAGER_H
#define PLUGINMANAGER_H

#include <QObject>
#include <QMap>
#include <QList>
#include <QString>
#include <QPluginLoader>

class IPlugin;

/**
 * @brief 插件管理器类，负责加载和管理插件
 * 
 * PluginManager提供了插件的加载、卸载和管理功能，
 * 支持动态扩展应用程序功能。
 */
class PluginManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     */
    PluginManager();

    /**
     * @brief 析构函数
     */
    ~PluginManager();

    /**
     * @brief 初始化插件管理器
     * @return 初始化是否成功
     */
    bool initialize();

    /**
     * @brief 清理插件管理器资源
     */
    void cleanup();

    /**
     * @brief 加载插件
     * @param path 插件文件路径
     * @return 加载是否成功
     */
    bool loadPlugin(const QString& path);

    /**
     * @brief 卸载插件
     * @param name 插件名称
     * @return 卸载是否成功
     */
    bool unloadPlugin(const QString& name);

    /**
     * @brief 获取所有已加载的插件
     * @return 插件列表
     */
    QList<IPlugin*> getPlugins() const;

    /**
     * @brief 根据名称获取插件
     * @param name 插件名称
     * @return 插件指针，如果未找到则返回nullptr
     */
    IPlugin* getPlugin(const QString& name) const;

    /**
     * @brief 扫描插件目录
     * @param directory 插件目录路径
     * @return 加载的插件数量
     */
    int scanPluginDirectory(const QString& directory);

signals:
    /**
     * @brief 插件加载信号
     * @param plugin 已加载的插件
     */
    void pluginLoaded(IPlugin* plugin);

    /**
     * @brief 插件卸载信号
     * @param name 已卸载的插件名称
     */
    void pluginUnloaded(const QString& name);

private:
    QMap<QString, IPlugin*> m_plugins;
    QList<QPluginLoader*> m_loaders;
};

/**
 * @brief 插件接口
 * 
 * 所有插件必须实现此接口，以便被插件管理器识别和管理。
 */
class IPlugin
{
public:
    /**
     * @brief 虚析构函数
     */
    virtual ~IPlugin() = default;

    /**
     * @brief 获取插件名称
     * @return 插件名称
     */
    virtual QString name() const = 0;

    /**
     * @brief 获取插件版本
     * @return 插件版本
     */
    virtual QString version() const = 0;

    /**
     * @brief 获取插件描述
     * @return 插件描述
     */
    virtual QString description() const = 0;

    /**
     * @brief 初始化插件
     * @return 初始化是否成功
     */
    virtual bool initialize() = 0;

    /**
     * @brief 关闭插件
     */
    virtual void shutdown() = 0;
};

// 定义插件接口的元对象系统支持
Q_DECLARE_INTERFACE(IPlugin, "com.optimize.IPlugin/1.0")

#endif // PLUGINMANAGER_H
