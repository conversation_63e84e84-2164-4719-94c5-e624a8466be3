/****************************************************************************
** Meta object code from reading C++ file 'CustomTreeWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../src/widgets/CustomTreeWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CustomTreeWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CustomTreeWidget_t {
    QByteArrayData data[18];
    char stringdata0[251];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CustomTreeWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CustomTreeWidget_t qt_meta_stringdata_CustomTreeWidget = {
    {
QT_MOC_LITERAL(0, 0, 16), // "CustomTreeWidget"
QT_MOC_LITERAL(1, 17, 12), // "nodeSelected"
QT_MOC_LITERAL(2, 30, 0), // ""
QT_MOC_LITERAL(3, 31, 16), // "QTreeWidgetItem*"
QT_MOC_LITERAL(4, 48, 4), // "item"
QT_MOC_LITERAL(5, 53, 8), // "NodeType"
QT_MOC_LITERAL(6, 62, 4), // "type"
QT_MOC_LITERAL(7, 67, 17), // "nodeDoubleClicked"
QT_MOC_LITERAL(8, 85, 24), // "nodeContextMenuRequested"
QT_MOC_LITERAL(9, 110, 3), // "pos"
QT_MOC_LITERAL(10, 114, 19), // "onItemDoubleClicked"
QT_MOC_LITERAL(11, 134, 6), // "column"
QT_MOC_LITERAL(12, 141, 28), // "onCustomContextMenuRequested"
QT_MOC_LITERAL(13, 170, 22), // "onItemSelectionChanged"
QT_MOC_LITERAL(14, 193, 11), // "onNewFolder"
QT_MOC_LITERAL(15, 205, 14), // "onDeleteFolder"
QT_MOC_LITERAL(16, 220, 14), // "onRenameFolder"
QT_MOC_LITERAL(17, 235, 15) // "onRefreshFolder"

    },
    "CustomTreeWidget\0nodeSelected\0\0"
    "QTreeWidgetItem*\0item\0NodeType\0type\0"
    "nodeDoubleClicked\0nodeContextMenuRequested\0"
    "pos\0onItemDoubleClicked\0column\0"
    "onCustomContextMenuRequested\0"
    "onItemSelectionChanged\0onNewFolder\0"
    "onDeleteFolder\0onRenameFolder\0"
    "onRefreshFolder"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CustomTreeWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   64,    2, 0x06 /* Public */,
       7,    2,   69,    2, 0x06 /* Public */,
       8,    2,   74,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      10,    2,   79,    2, 0x08 /* Private */,
      12,    1,   84,    2, 0x08 /* Private */,
      13,    0,   87,    2, 0x08 /* Private */,
      14,    0,   88,    2, 0x08 /* Private */,
      15,    0,   89,    2, 0x08 /* Private */,
      16,    0,   90,    2, 0x08 /* Private */,
      17,    0,   91,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, 0x80000000 | 5,    4,    6,
    QMetaType::Void, 0x80000000 | 3, 0x80000000 | 5,    4,    6,
    QMetaType::Void, 0x80000000 | 3, QMetaType::QPoint,    4,    9,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::Int,    4,   11,
    QMetaType::Void, QMetaType::QPoint,    9,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void CustomTreeWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CustomTreeWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->nodeSelected((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< NodeType(*)>(_a[2]))); break;
        case 1: _t->nodeDoubleClicked((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< NodeType(*)>(_a[2]))); break;
        case 2: _t->nodeContextMenuRequested((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< const QPoint(*)>(_a[2]))); break;
        case 3: _t->onItemDoubleClicked((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->onCustomContextMenuRequested((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 5: _t->onItemSelectionChanged(); break;
        case 6: _t->onNewFolder(); break;
        case 7: _t->onDeleteFolder(); break;
        case 8: _t->onRenameFolder(); break;
        case 9: _t->onRefreshFolder(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CustomTreeWidget::*)(QTreeWidgetItem * , NodeType );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CustomTreeWidget::nodeSelected)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (CustomTreeWidget::*)(QTreeWidgetItem * , NodeType );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CustomTreeWidget::nodeDoubleClicked)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (CustomTreeWidget::*)(QTreeWidgetItem * , const QPoint & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CustomTreeWidget::nodeContextMenuRequested)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CustomTreeWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QTreeWidget::staticMetaObject>(),
    qt_meta_stringdata_CustomTreeWidget.data,
    qt_meta_data_CustomTreeWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CustomTreeWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CustomTreeWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CustomTreeWidget.stringdata0))
        return static_cast<void*>(this);
    return QTreeWidget::qt_metacast(_clname);
}

int CustomTreeWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QTreeWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void CustomTreeWidget::nodeSelected(QTreeWidgetItem * _t1, NodeType _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void CustomTreeWidget::nodeDoubleClicked(QTreeWidgetItem * _t1, NodeType _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void CustomTreeWidget::nodeContextMenuRequested(QTreeWidgetItem * _t1, const QPoint & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
