@echo off
setlocal

REM Kill any running instances of the application
taskkill /F /IM OptimizeApplication.exe 2>nul
timeout /t 1 /nobreak >nul

REM Set path to Qt installation
set QTDIR=D:\Qt\Qt5.14.2\5.14.2\msvc2017_64
set PATH=%QTDIR%\bin;%PATH%

REM Set up Visual Studio environment variables
call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvars64.bat"

REM Create build directory if it doesn't exist
if not exist "build" mkdir build

REM Navigate to build directory
cd build

REM Run qmake
%QTDIR%\bin\qmake.exe ..\OptimizeApplication.pro -spec win32-msvc "CONFIG+=debug" "CONFIG+=qml_debug"

REM Build the project
nmake

REM Return to original directory
cd ..

echo Build completed. 