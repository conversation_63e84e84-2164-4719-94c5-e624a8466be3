#include "PipeWidget.h"
#include "ui_PipeWidget.h"
#include <QHeaderView>
#include <QMessageBox>
#include <QFileDialog>
#include <QMenu>
#include <QComboBox>
#include "../utils/Logger.h"

PipeWidget::PipeWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::PipeWidget)
    , m_isUpdating(false)
{
    ui->setupUi(this);
    setupUI();
    setupConnections();
    setupContextMenus();
    LOG_INFO("PipeWidget created", "PipeWidget");
}

PipeWidget::PipeWidget(const PipeComponent& pipe, QWidget *parent)
    : PipeWidget(parent)
{
    setPipeComponent(pipe);
}

PipeWidget::~PipeWidget()
{
    delete ui;
    LOG_INFO("PipeWidget destroyed", "PipeWidget");
}

void PipeWidget::setupUI()
{
    // Additional UI setup that's not in the .ui file
    ui->volumeTable->horizontalHeader()->setStretchLastSection(true);
    ui->initialConditionTable->horizontalHeader()->setStretchLastSection(true);
}

void PipeWidget::setupConnections()
{
    connect(ui->componentNameEdit, &QLineEdit::textChanged, this, &PipeWidget::onBasicDataChanged);
    connect(ui->numberOfVolumesSpin, QOverload<int>::of(&QSpinBox::valueChanged), this, &PipeWidget::onBasicDataChanged);
    connect(ui->componentTypeCombo, QOverload<const QString &>::of(&QComboBox::currentTextChanged), this, &PipeWidget::onBasicDataChanged);
    
    connect(ui->volumeTable, &QTableWidget::cellChanged, this, &PipeWidget::onVolumeDataChanged);
    connect(ui->initialConditionTable, &QTableWidget::cellChanged, this, &PipeWidget::onInitialConditionChanged);
    
    connect(ui->volumeControlEdit, &QLineEdit::textChanged, this, &PipeWidget::onControlFlagsChanged);
    connect(ui->junctionControlEdit, &QLineEdit::textChanged, this, &PipeWidget::onJunctionFlagsChanged);
    connect(ui->initialConditionFlagSpin, QOverload<int>::of(&QSpinBox::valueChanged), this, &PipeWidget::onControlFlagsChanged);
    
    connect(ui->addVolumeBtn, &QPushButton::clicked, this, &PipeWidget::addVolume);
    connect(ui->removeVolumeBtn, &QPushButton::clicked, this, &PipeWidget::removeVolume);
    
    connect(ui->validateBtn, &QPushButton::clicked, this, [this]() {
        if (validateData()) {
            QMessageBox::information(this, "Validation", "All data is valid!");
        }
    });
    connect(ui->generateCardsBtn, &QPushButton::clicked, this, [this]() {
        QString cards = generateRELAP5Cards();
        QMessageBox::information(this, "RELAP5 Cards", cards);
    });
    connect(ui->exportCSVBtn, &QPushButton::clicked, this, &PipeWidget::exportToCSV);
    connect(ui->importCSVBtn, &QPushButton::clicked, this, &PipeWidget::importFromCSV);
}

void PipeWidget::setPipeComponent(const PipeComponent& pipe)
{
    m_pipeComponent = pipe;
    updateDisplay();
}

PipeComponent PipeWidget::getPipeComponent() const
{
    return m_pipeComponent;
}

void PipeWidget::updateDisplay()
{
    if (m_isUpdating) return;
    m_isUpdating = true;
    
    // Update basic information
    ui->componentIdEdit->setText(m_pipeComponent.componentId);
    ui->componentNameEdit->setText(m_pipeComponent.componentName);
    ui->numberOfVolumesSpin->setValue(m_pipeComponent.numberOfVolumes);
    ui->componentTypeCombo->setCurrentText(m_pipeComponent.componentType);
    
    // Update control flags
    updateControlFlags();
    updateJunctionFlags();
    
    // Update volume table
    updateVolumeTable();
    
    // Update initial condition table
    updateInitialConditionTable();
    
    m_isUpdating = false;
}

void PipeWidget::updateVolumeTable()
{
    ui->volumeTable->setRowCount(m_pipeComponent.numberOfVolumes);
    
    for (int i = 0; i < m_pipeComponent.numberOfVolumes; ++i) {
        // Volume
        QTableWidgetItem* volumeItem = new QTableWidgetItem();
        if (i < m_pipeComponent.volumes.size()) {
            volumeItem->setText(QString::number(m_pipeComponent.volumes[i], 'g', 6));
        } else {
            volumeItem->setText("0.0");
        }
        ui->volumeTable->setItem(i, 0, volumeItem);
        
        // Length
        QTableWidgetItem* lengthItem = new QTableWidgetItem();
        if (i < m_pipeComponent.lengths.size()) {
            lengthItem->setText(QString::number(m_pipeComponent.lengths[i], 'g', 6));
        } else {
            lengthItem->setText("0.0");
        }
        ui->volumeTable->setItem(i, 1, lengthItem);
        
        // Elevation
        QTableWidgetItem* elevationItem = new QTableWidgetItem();
        if (i < m_pipeComponent.elevations.size()) {
            elevationItem->setText(QString::number(m_pipeComponent.elevations[i], 'g', 6));
        } else {
            elevationItem->setText("0.0");
        }
        ui->volumeTable->setItem(i, 2, elevationItem);
        
        // Roughness
        QTableWidgetItem* roughnessItem = new QTableWidgetItem();
        if (i < m_pipeComponent.roughness.size()) {
            roughnessItem->setText(QString::number(m_pipeComponent.roughness[i], 'g', 6));
        } else {
            roughnessItem->setText("0.0");
        }
        ui->volumeTable->setItem(i, 3, roughnessItem);
        
        // Hydraulic diameter
        QTableWidgetItem* diameterItem = new QTableWidgetItem();
        if (i < m_pipeComponent.hydraulicDiameters.size()) {
            diameterItem->setText(QString::number(m_pipeComponent.hydraulicDiameters[i], 'g', 6));
        } else {
            diameterItem->setText("0.0");
        }
        ui->volumeTable->setItem(i, 4, diameterItem);
        
        // Angle
        QTableWidgetItem* angleItem = new QTableWidgetItem();
        if (i < m_pipeComponent.angles.size()) {
            angleItem->setText(QString::number(m_pipeComponent.angles[i], 'g', 6));
        } else {
            angleItem->setText("0.0");
        }
        ui->volumeTable->setItem(i, 5, angleItem);
    }
}

void PipeWidget::updateInitialConditionTable()
{
    ui->initialConditionTable->setRowCount(m_pipeComponent.numberOfVolumes);
    
    for (int i = 0; i < m_pipeComponent.numberOfVolumes; ++i) {
        // Thermodynamic state
        QTableWidgetItem* stateItem = new QTableWidgetItem();
        if (i < m_pipeComponent.initialConditions.size()) {
            stateItem->setText(QString::number(m_pipeComponent.initialConditions[i].thermodynamicState));
        } else {
            stateItem->setText("3");
        }
        ui->initialConditionTable->setItem(i, 0, stateItem);
        
        // Pressure
        QTableWidgetItem* pressureItem = new QTableWidgetItem();
        if (i < m_pipeComponent.initialConditions.size()) {
            pressureItem->setText(QString::number(m_pipeComponent.initialConditions[i].pressure, 'g', 6));
        } else {
            pressureItem->setText("1.0e5");
        }
        ui->initialConditionTable->setItem(i, 1, pressureItem);
        
        // Temperature
        QTableWidgetItem* temperatureItem = new QTableWidgetItem();
        if (i < m_pipeComponent.initialConditions.size()) {
            temperatureItem->setText(QString::number(m_pipeComponent.initialConditions[i].temperature, 'g', 6));
        } else {
            temperatureItem->setText("300.0");
        }
        ui->initialConditionTable->setItem(i, 2, temperatureItem);
        
        // Quality
        QTableWidgetItem* qualityItem = new QTableWidgetItem();
        if (i < m_pipeComponent.initialConditions.size()) {
            qualityItem->setText(QString::number(m_pipeComponent.initialConditions[i].quality, 'g', 6));
        } else {
            qualityItem->setText("0.0");
        }
        ui->initialConditionTable->setItem(i, 3, qualityItem);
        
        // Velocity
        QTableWidgetItem* velocityItem = new QTableWidgetItem();
        if (i < m_pipeComponent.initialConditions.size()) {
            velocityItem->setText(QString::number(m_pipeComponent.initialConditions[i].velocity, 'g', 6));
        } else {
            velocityItem->setText("0.0");
        }
        ui->initialConditionTable->setItem(i, 4, velocityItem);
        
        // Boron density
        QTableWidgetItem* boronItem = new QTableWidgetItem();
        if (i < m_pipeComponent.initialConditions.size()) {
            boronItem->setText(QString::number(m_pipeComponent.initialConditions[i].boronDensity, 'g', 6));
        } else {
            boronItem->setText("0.0");
        }
        ui->initialConditionTable->setItem(i, 5, boronItem);
    }
}

void PipeWidget::clear()
{
    m_pipeComponent = PipeComponent();
    updateDisplay();
}

void PipeWidget::onBasicDataChanged()
{
    m_pipeComponent.componentName = ui->componentNameEdit->text();
    m_pipeComponent.numberOfVolumes = ui->numberOfVolumesSpin->value();
    
    // Resize arrays
    m_pipeComponent.volumes.resize(m_pipeComponent.numberOfVolumes);
    m_pipeComponent.lengths.resize(m_pipeComponent.numberOfVolumes);
    m_pipeComponent.elevations.resize(m_pipeComponent.numberOfVolumes);
    m_pipeComponent.roughness.resize(m_pipeComponent.numberOfVolumes);
    m_pipeComponent.hydraulicDiameters.resize(m_pipeComponent.numberOfVolumes);
    m_pipeComponent.angles.resize(m_pipeComponent.numberOfVolumes);
    m_pipeComponent.initialConditions.resize(m_pipeComponent.numberOfVolumes);
    
    updateVolumeTable();
    updateInitialConditionTable();
    
    emit dataChanged();
    emit componentModified(m_pipeComponent.componentId);
}

void PipeWidget::onVolumeDataChanged()
{
    // Update data from table to component
    for (int i = 0; i < ui->volumeTable->rowCount(); ++i) {
        if (i < m_pipeComponent.volumes.size()) {
            QTableWidgetItem* item = ui->volumeTable->item(i, 0);
            if (item) m_pipeComponent.volumes[i] = item->text().toDouble();
            
            item = ui->volumeTable->item(i, 1);
            if (item) m_pipeComponent.lengths[i] = item->text().toDouble();
            
            item = ui->volumeTable->item(i, 2);
            if (item) m_pipeComponent.elevations[i] = item->text().toDouble();
            
            item = ui->volumeTable->item(i, 3);
            if (item) m_pipeComponent.roughness[i] = item->text().toDouble();
            
            item = ui->volumeTable->item(i, 4);
            if (item) m_pipeComponent.hydraulicDiameters[i] = item->text().toDouble();
            
            item = ui->volumeTable->item(i, 5);
            if (item) m_pipeComponent.angles[i] = item->text().toDouble();
        }
    }
    
    emit dataChanged();
    emit componentModified(m_pipeComponent.componentId);
}

void PipeWidget::onInitialConditionChanged()
{
    // Update initial condition data from table
    for (int i = 0; i < ui->initialConditionTable->rowCount(); ++i) {
        if (i < m_pipeComponent.initialConditions.size()) {
            QTableWidgetItem* item = ui->initialConditionTable->item(i, 0);
            if (item) m_pipeComponent.initialConditions[i].thermodynamicState = item->text().toInt();
            
            item = ui->initialConditionTable->item(i, 1);
            if (item) m_pipeComponent.initialConditions[i].pressure = item->text().toDouble();
            
            item = ui->initialConditionTable->item(i, 2);
            if (item) m_pipeComponent.initialConditions[i].temperature = item->text().toDouble();
            
            item = ui->initialConditionTable->item(i, 3);
            if (item) m_pipeComponent.initialConditions[i].quality = item->text().toDouble();
            
            item = ui->initialConditionTable->item(i, 4);
            if (item) m_pipeComponent.initialConditions[i].velocity = item->text().toDouble();
            
            item = ui->initialConditionTable->item(i, 5);
            if (item) m_pipeComponent.initialConditions[i].boronDensity = item->text().toDouble();
        }
    }
    
    emit dataChanged();
    emit componentModified(m_pipeComponent.componentId);
}

void PipeWidget::addVolume()
{
    ui->numberOfVolumesSpin->setValue(ui->numberOfVolumesSpin->value() + 1);
}

void PipeWidget::removeVolume()
{
    if (ui->numberOfVolumesSpin->value() > 1) {
        ui->numberOfVolumesSpin->setValue(ui->numberOfVolumesSpin->value() - 1);
    }
}

void PipeWidget::onControlFlagsChanged()
{
    if (m_isUpdating) return;
    
    // 这里可以添加控制标记变化的处理逻辑
    emit dataChanged();
    emit componentModified(m_pipeComponent.componentId);
}

void PipeWidget::onJunctionFlagsChanged()
{
    if (m_isUpdating) return;
    
    // 这里可以添加接管控制标记变化的处理逻辑
    emit dataChanged();
    emit componentModified(m_pipeComponent.componentId);
}

void PipeWidget::updateControlFlags()
{
    // 设置默认的控制标记
    ui->volumeControlEdit->setText("00");
    ui->initialConditionFlagSpin->setValue(3); // 默认为P,T状态
}

void PipeWidget::updateJunctionFlags()
{
    // 设置默认的接管控制标记
    ui->junctionControlEdit->setText("00000000");
}

void PipeWidget::setupContextMenus()
{
    // 为表格添加右键菜单
    ui->volumeTable->setContextMenuPolicy(Qt::CustomContextMenu);
    ui->initialConditionTable->setContextMenuPolicy(Qt::CustomContextMenu);
    
    connect(ui->volumeTable, &QTableWidget::customContextMenuRequested, this, [this](const QPoint& pos) {
        QMenu menu(this);
        menu.addAction("Copy", this, &PipeWidget::copyVolumeData);
        menu.addAction("Paste", this, &PipeWidget::pasteVolumeData);
        menu.addSeparator();
        menu.addAction("Fill with Defaults", this, &PipeWidget::fillVolumeDataWithDefaults);
        menu.exec(ui->volumeTable->mapToGlobal(pos));
    });
}

bool PipeWidget::validateData()
{
    QString errorMsg;
    
    // 验证基本数据
    if (m_pipeComponent.componentId.isEmpty()) {
        emit validationError("Component ID cannot be empty");
        return false;
    }
    
    if (m_pipeComponent.numberOfVolumes <= 0) {
        emit validationError("Number of volumes must be greater than 0");
        return false;
    }
    
    // 验证体积数据
    for (int i = 0; i < m_pipeComponent.numberOfVolumes; ++i) {
        if (!validateVolumeData(i, errorMsg)) {
            emit validationError(QString("Volume %1: %2").arg(i+1).arg(errorMsg));
            return false;
        }
        
        if (!validateInitialConditions(i, errorMsg)) {
            emit validationError(QString("Initial Condition %1: %2").arg(i+1).arg(errorMsg));
            return false;
        }
    }
    
    return true;
}

bool PipeWidget::validateVolumeData(int index, QString& errorMsg)
{
    if (index >= m_pipeComponent.volumes.size()) {
        errorMsg = "Volume data not available";
        return false;
    }
    
    if (m_pipeComponent.volumes[index] <= 0) {
        errorMsg = "Volume must be greater than 0";
        return false;
    }
    
    if (m_pipeComponent.lengths[index] <= 0) {
        errorMsg = "Length must be greater than 0";
        return false;
    }
    
    if (m_pipeComponent.hydraulicDiameters[index] <= 0) {
        errorMsg = "Hydraulic diameter must be greater than 0";
        return false;
    }
    
    return true;
}

bool PipeWidget::validateInitialConditions(int index, QString& errorMsg)
{
    if (index >= m_pipeComponent.initialConditions.size()) {
        errorMsg = "Initial condition data not available";
        return false;
    }
    
    const auto& ic = m_pipeComponent.initialConditions[index];
    
    if (ic.pressure <= 0) {
        errorMsg = "Pressure must be greater than 0";
        return false;
    }
    
    if (ic.temperature <= 0) {
        errorMsg = "Temperature must be greater than 0";
        return false;
    }
    
    return true;
}

QString PipeWidget::generateRELAP5Cards() const
{
    QString cards;
    QString baseId = m_pipeComponent.componentId.left(6);
    
    // 组件定义卡
    cards += QString("%1000 \"%2\" %3\n")
             .arg(baseId + "0")
             .arg(m_pipeComponent.componentName)
             .arg(m_pipeComponent.componentType);
    
    // 体积数量卡
    cards += QString("%1001 %2\n")
             .arg(baseId + "0")
             .arg(m_pipeComponent.numberOfVolumes);
    
    // 控制体面积卡
    if (!m_pipeComponent.volumes.isEmpty()) {
        cards += QString("%1101 %2 %3\n")
                 .arg(baseId + "0")
                 .arg(m_pipeComponent.volumes[0], 0, 'g', 6)
                 .arg(m_pipeComponent.numberOfVolumes);
    }
    
    // 控制体长度卡
    if (!m_pipeComponent.lengths.isEmpty()) {
        cards += QString("%1301 %2 %3\n")
                 .arg(baseId + "0")
                 .arg(m_pipeComponent.lengths[0], 0, 'g', 6)
                 .arg(m_pipeComponent.numberOfVolumes);
    }
    
    // 垂直倾斜角卡
    if (!m_pipeComponent.angles.isEmpty()) {
        cards += QString("%1601 %2 %3\n")
                 .arg(baseId + "0")
                 .arg(m_pipeComponent.angles[0], 0, 'g', 6)
                 .arg(m_pipeComponent.numberOfVolumes);
    }
    
    // 高度变化卡
    if (!m_pipeComponent.elevations.isEmpty()) {
        cards += QString("%1701 %2 %3\n")
                 .arg(baseId + "0")
                 .arg(m_pipeComponent.elevations[0], 0, 'g', 6)
                 .arg(m_pipeComponent.numberOfVolumes);
    }
    
    // 壁面粗糙度、水力直径卡
    if (!m_pipeComponent.roughness.isEmpty() && !m_pipeComponent.hydraulicDiameters.isEmpty()) {
        cards += QString("%1801 %2 %3 %4\n")
                 .arg(baseId + "0")
                 .arg(m_pipeComponent.roughness[0], 0, 'g', 6)
                 .arg(m_pipeComponent.hydraulicDiameters[0], 0, 'g', 6)
                 .arg(m_pipeComponent.numberOfVolumes);
    }
    
    // 控制体控制标记卡
    cards += QString("%11001 %2 %3\n")
             .arg(baseId)
             .arg(ui->volumeControlEdit->text())
             .arg(m_pipeComponent.numberOfVolumes);
    
    // 初始条件卡
    if (!m_pipeComponent.initialConditions.isEmpty()) {
        const auto& ic = m_pipeComponent.initialConditions[0];
        cards += QString("%11201 %2 %3 %4 0.0 0.0 0.0 %5\n")
                 .arg(baseId)
                 .arg(ui->initialConditionFlagSpin->value())
                 .arg(ic.pressure, 0, 'g', 6)
                 .arg(ic.temperature, 0, 'g', 6)
                 .arg(m_pipeComponent.numberOfVolumes);
    }
    
    return cards;
}

void PipeWidget::copyVolumeData()
{
    // 实现复制功能
    m_clipboardData.clear();
    // 这里可以添加具体的复制逻辑
}

void PipeWidget::pasteVolumeData()
{
    // 实现粘贴功能
    // 这里可以添加具体的粘贴逻辑
}

void PipeWidget::fillVolumeDataWithDefaults()
{
    for (int i = 0; i < m_pipeComponent.numberOfVolumes; ++i) {
        if (i < m_pipeComponent.volumes.size()) {
            m_pipeComponent.volumes[i] = 0.00012469; // 默认面积
            m_pipeComponent.lengths[i] = 0.1385; // 默认长度
            m_pipeComponent.elevations[i] = 0.1385; // 默认高度变化
            m_pipeComponent.roughness[i] = 6.3e-6; // 默认粗糙度
            m_pipeComponent.hydraulicDiameters[i] = 0.0126; // 默认水力直径
            m_pipeComponent.angles[i] = 90.0; // 默认垂直向上
        }
    }
    updateVolumeTable();
    emit dataChanged();
}

void PipeWidget::fillInitialConditionsWithDefaults()
{
    for (int i = 0; i < m_pipeComponent.numberOfVolumes; ++i) {
        if (i < m_pipeComponent.initialConditions.size()) {
            m_pipeComponent.initialConditions[i].thermodynamicState = 3;
            m_pipeComponent.initialConditions[i].pressure = 6.9e6;
            m_pipeComponent.initialConditions[i].temperature = 538.98;
            m_pipeComponent.initialConditions[i].quality = 0.0;
            m_pipeComponent.initialConditions[i].velocity = 0.0;
            m_pipeComponent.initialConditions[i].boronDensity = 0.0;
        }
    }
    updateInitialConditionTable();
    emit dataChanged();
}

void PipeWidget::exportToCSV()
{
    QString fileName = QFileDialog::getSaveFileName(this, "Export to CSV", 
                                                   m_pipeComponent.componentId + "_data.csv",
                                                   "CSV Files (*.csv)");
    if (!fileName.isEmpty()) {
        // 实现CSV导出功能
        QMessageBox::information(this, "Export", "CSV export functionality to be implemented");
    }
}

void PipeWidget::importFromCSV()
{
    QString fileName = QFileDialog::getOpenFileName(this, "Import from CSV", 
                                                   "",
                                                   "CSV Files (*.csv)");
    if (!fileName.isEmpty()) {
        // 实现CSV导入功能
        QMessageBox::information(this, "Import", "CSV import functionality to be implemented");
    }
}

void PipeWidget::loadFromRELAP5Cards(const QStringList& cards)
{
    // 实现从RELAP5卡片加载数据的功能
    // 这里可以添加具体的解析逻辑
}

void PipeWidget::validateAndHighlightErrors()
{
    // 实现数据验证和错误高亮显示
    // 这里可以添加具体的验证和高亮逻辑
} 