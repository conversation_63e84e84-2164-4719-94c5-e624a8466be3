#include "ChartWidget.h"
#include <QVBoxLayout>
#include <QtCharts/QChart>
#include <QtCharts/QChartView>
#include <QtCharts/QLineSeries>
#include <QtCharts/QValueAxis>
#include <QPainter>
#include "../utils/Logger.h"

ChartWidget::ChartWidget(QWidget *parent)
    : QWidget(parent)
    , chartView(nullptr)
    , chart(nullptr)
    , axisX(nullptr)
    , axisY(nullptr)
{
    initUI();
    LOG_INFO("ChartWidget initialized", "ChartWidget");
}

ChartWidget::~ChartWidget()
{
    LOG_INFO("ChartWidget destroyed", "ChartWidget");
}

void ChartWidget::initUI()
{
    // Create layout
    QVBoxLayout* layout = new QVBoxLayout(this);
    layout->setContentsMargins(5, 5, 5, 5);

    // Create chart
    chart = new QChart();
    chart->setAnimationOptions(QChart::SeriesAnimations);
    chart->setTheme(QChart::ChartThemeLight);
    chart->setTitle(tr("Optimization Results"));

    // Create chart view
    chartView = new QChartView(chart);
    chartView->setRenderHint(QPainter::Antialiasing);
    chartView->setMinimumSize(400, 300);

    // Add to layout
    layout->addWidget(chartView);

    // Setup chart
    setupChart();
}

QString ChartWidget::getCurrentPath()
{
    return chartView->chart()->series().first()->name();
}

void ChartWidget::setupChart()
{
    // Create axes
    axisX = new QValueAxis();
    axisX->setTitleText(tr("Iteration"));
    axisX->setRange(0, 100);
    axisX->setTickCount(11);
    axisX->setLabelFormat("%d");

    axisY = new QValueAxis();
    axisY->setTitleText(tr("Fitness Value"));
    axisY->setRange(0, 1);
    axisY->setTickCount(11);
    axisY->setLabelFormat("%.3f");

    // Set axes
    chart->addAxis(axisX, Qt::AlignBottom);
    chart->addAxis(axisY, Qt::AlignLeft);

    // Set legend
    chart->legend()->setVisible(true);
    chart->legend()->setAlignment(Qt::AlignBottom);
    
    // Add sample data
    addSampleData();
}

void ChartWidget::addSampleData()
{
    // Add sample optimization curves
    QLineSeries* bestSeries = new QLineSeries();
    bestSeries->setName(tr("Best Fitness"));
    
    QLineSeries* avgSeries = new QLineSeries();
    avgSeries->setName(tr("Average Fitness"));
    
    // Generate sample data
    for (int i = 0; i <= 100; ++i) {
        double bestValue = 1.0 - (1.0 - 0.1) * (1.0 - exp(-i * 0.05));
        double avgValue = bestValue * 0.8 + 0.1 * sin(i * 0.1);
        
        bestSeries->append(i, bestValue);
        avgSeries->append(i, avgValue);
    }
    
    chart->addSeries(bestSeries);
    chart->addSeries(avgSeries);
    
    bestSeries->attachAxis(axisX);
    bestSeries->attachAxis(axisY);
    avgSeries->attachAxis(axisX);
    avgSeries->attachAxis(axisY);
}

void ChartWidget::addSeries(const QString& name, const QVector<double>& x, const QVector<double>& y)
{
    // Create data series
    QLineSeries* series = new QLineSeries();
    series->setName(name);

    // Add data points
    for (int i = 0; i < x.size() && i < y.size(); ++i) {
        series->append(x[i], y[i]);
    }

    // Add data series to chart
    chart->addSeries(series);
    series->attachAxis(axisX);
    series->attachAxis(axisY);
    
    LOG_INFO(QString("Series added: %1 with %2 points").arg(name).arg(x.size()), "ChartWidget");
}

void ChartWidget::clearSeries()
{
    chart->removeAllSeries();
    LOG_INFO("All series cleared", "ChartWidget");
}

void ChartWidget::setTitle(const QString& title)
{
    chart->setTitle(title);
    LOG_INFO(QString("Chart title set to: %1").arg(title), "ChartWidget");
}

void ChartWidget::setXAxisLabel(const QString& label)
{
    if (axisX) {
        axisX->setTitleText(label);
    }
}

void ChartWidget::setYAxisLabel(const QString& label)
{
    if (axisY) {
        axisY->setTitleText(label);
    }
}

void ChartWidget::setXAxisRange(double min, double max)
{
    if (axisX) {
        axisX->setRange(min, max);
    }
}

void ChartWidget::setYAxisRange(double min, double max)
{
    if (axisY) {
        axisY->setRange(min, max);
    }
}

void ChartWidget::setTheme(QChart::ChartTheme theme)
{
    if (chart) {
        chart->setTheme(theme);
    }
}

void ChartWidget::setAnimationOptions(QChart::AnimationOptions options)
{
    if (chart) {
        chart->setAnimationOptions(options);
    }
}

void ChartWidget::setLegendAlignment(Qt::AlignmentFlag alignment)
{
    if (chart && chart->legend()) {
        chart->legend()->setAlignment(alignment);
    }
}

void ChartWidget::setLegendVisible(bool visible)
{
    if (chart && chart->legend()) {
        chart->legend()->setVisible(visible);
    }
} 