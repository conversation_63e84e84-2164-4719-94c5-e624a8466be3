#include "InputData.h"
#include "../utils/Logger.h"
#include <QFile>
#include <QFileInfo>
#include <QTextStream>
#include <QDebug>
#include <QRegularExpression>
#include <QRegularExpressionMatch>

InputData::InputData()
    : fileType(InputFileType::UNKNOWN), m_isValid(false)
{
    clear();
}

void InputData::clear()
{
    // Clear file info
    filePath.clear();
    fileName.clear();
    lastModified = QDateTime();
    fileType = InputFileType::UNKNOWN;
    problemTitle.clear();

    // Clear control cards
    controlCard = ControlCard();
    unitSystem = UnitSystem();

    // Clear output control
    plotVariables.clear();
    minorEditVars.clear();

    // Clear geometry components
    pipes.clear();
    branches.clear();
    volumes.clear();
    junctions.clear();

    // Clear heat structures
    heatStructures.clear();
    materials.clear();

    // Clear table data
    tables.clear();

    // Clear comments and raw data
    comments.clear();
    rawLines.clear();

    m_isValid = false;
    m_errors.clear();
}

bool InputData::loadFromFile(const QString& filePath)
{
    clear();

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        m_errors << QString("Cannot open file: %1").arg(filePath);
        return false;
    }

    // Set basic information
    QFileInfo fileInfo(filePath);
    this->filePath = filePath;
    this->fileName = fileInfo.fileName();
    this->lastModified = fileInfo.lastModified();
    this->fileType = detectFileType(filePath);

    // Read file content
    QTextStream in(&file);
    in.setCodec("UTF-8");

    QStringList lines;
    while (!in.atEnd()) {
        QString line = in.readLine();
        lines.append(line);
        rawLines.append(line);
    }
    file.close();

    // Parse file content
    bool success = true;

    // Extract problem title
    for (const QString& line : lines) {
        if (line.startsWith("=") && !line.startsWith("=*")) {
            problemTitle = line.mid(1).trimmed();
            break;
        }
    }

    // Parse sections
    success &= parseControlCard(lines);
    success &= parseUnitSystem(lines);
    success &= parsePlotVariables(lines);
    success &= parseMinorEditVariables(lines);
    success &= parseGeometryComponents(lines);
    success &= parseHeatStructures(lines);
    success &= parseMaterials(lines);
    success &= parseTables(lines);

    parseComments(lines);

    m_isValid = success && m_errors.isEmpty();
    return m_isValid;
}

bool InputData::saveToFile(const QString& filePath) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");

    // Write original lines (preserve original format)
    for (const QString& line : rawLines) {
        out << line << "\n";
    }

    file.close();
    return true;
}

bool InputData::isValid() const
{
    return m_isValid;
}

GeometryComponent* InputData::findComponent(const QString& componentId)
{
    // Search in pipes
    for (auto& pipe : pipes) {
        if (pipe.componentId == componentId) {
            return &pipe;
        }
    }

    // Search in branches
    for (auto& branch : branches) {
        if (branch.componentId == componentId) {
            return &branch;
        }
    }

    // Search in volumes
    for (auto& volume : volumes) {
        if (volume.componentId == componentId) {
            return &volume;
        }
    }

    // Search in junctions
    for (auto& junction : junctions) {
        if (junction.componentId == componentId) {
            return &junction;
        }
    }

    return nullptr;
}

const GeometryComponent* InputData::findComponent(const QString& componentId) const
{
    // Search in pipes
    for (const auto& pipe : pipes) {
        if (pipe.componentId == componentId) {
            return &pipe;
        }
    }

    // Search in branches
    for (const auto& branch : branches) {
        if (branch.componentId == componentId) {
            return &branch;
        }
    }

    // Search in volumes
    for (const auto& volume : volumes) {
        if (volume.componentId == componentId) {
            return &volume;
        }
    }

    // Search in junctions
    for (const auto& junction : junctions) {
        if (junction.componentId == componentId) {
            return &junction;
        }
    }

    return nullptr;
}

bool InputData::parseIFileHeader(const QStringList& lines)
{
    for (int i = 0; i < lines.size() && i < 20; ++i) {
        const QString& line = lines[i];

        // 查找问题标题
        if (line.startsWith("=") && !line.startsWith("=*")) {
            problemTitle = line.mid(1).trimmed();
            break;
        }
    }

    return true;
}

InputFileType InputData::detectFileType(const QString& filePath) const
{
    // Simple detection based on file content
    // This is a placeholder - actual implementation would analyze file content
    
    QFileInfo fileInfo(filePath);
    QString ext = fileInfo.suffix().toLower();
    
    if (ext == "i") {
        return InputFileType::NC_WELANDER; // Default for .i files
    }
    
    return InputFileType::UNKNOWN;
}

QStringList InputData::tokenizeLine(const QString& line) const
{
    // Simple tokenization - split by whitespace
    return line.simplified().split(' ', Qt::SkipEmptyParts);
}

void InputData::parseComments(const QStringList& lines)
{
    for (const QString& line : lines) {
        if (line.startsWith("*")) {
            comments.append(line);
        }
    }
}

bool InputData::parseControlCard(const QStringList& lines)
{
    // Placeholder implementation
    return true;
}

bool InputData::parseUnitSystem(const QStringList& lines)
{
    // Placeholder implementation
    return true;
}

bool InputData::parsePlotVariables(const QStringList& lines)
{
    // Placeholder implementation
    return true;
}

bool InputData::parseMinorEditVariables(const QStringList& lines)
{
    // Placeholder implementation
    return true;
}

bool InputData::parseGeometryComponents(const QStringList& lines)
{
    // Placeholder implementation
    return true;
}

bool InputData::parseHeatStructures(const QStringList& lines)
{
    // Placeholder implementation
    return true;
}

bool InputData::parseMaterials(const QStringList& lines)
{
    // Placeholder implementation
    return true;
}

bool InputData::parseTables(const QStringList& lines)
{
    // Placeholder implementation
    return true;
}

// Generation methods
QString InputData::generateIFileHeader() const
{
    QString header;

    header += "*======================================================================\n";
    if (!problemTitle.isEmpty()) {
        header += QString("=%1\n").arg(problemTitle);
    } else {
        header += "=Generated Input File\n";
    }
    header += "*======================================================================\n\n";

    return header;
}

QString InputData::generateIFileControlCards() const
{
    QString content;

    // 问题控制卡
    if (controlCard.problemNumber > 0) {
        content += formatIFileLine(QString::number(controlCard.problemNumber),
                                   {controlCard.problemType, controlCard.analysisType},
                                   "Problem control card");
    }

    // 单位系统
    if (!unitSystem.inputUnits.isEmpty()) {
        content += formatIFileLine("102", {unitSystem.inputUnits, unitSystem.outputUnits}, "Unit system");
    }
    
    if (!unitSystem.workingFluid.isEmpty()) {
        content += formatIFileLine("110", {unitSystem.workingFluid}, "Working fluid");
    }
    
    if (unitSystem.scaleFactor > 0) {
        content += formatIFileLine("115", {QString::number(unitSystem.scaleFactor)}, "Scale factor");
    }

    // 时间控制卡
    if (controlCard.endTime > 0) {
        QStringList timeValues = {
            QString::number(controlCard.endTime, 'e', 2),
            QString::number(controlCard.minTimeStep, 'e', 2),
            QString::number(controlCard.maxTimeStep, 'e', 2),
            QString::number(controlCard.controlOption),
            QString::number(controlCard.minorEdit),
            QString::number(controlCard.majorEdit),
            QString::number(controlCard.restartFreq)
        };
        content += formatIFileLine("201", timeValues, "Time control card");
    }

    content += "\n";
    return content;
}

QString InputData::generateIFileOutputControl() const
{
    QString content;

    if (!plotVariables.isEmpty() || !minorEditVars.isEmpty()) {
        content += "*======================================================================\n";
        content += "*                                绘图变量\n";
        content += "*======================================================================\n";

        for (const auto& var : plotVariables) {
            QStringList values = {
                var.variableType,
                var.componentId,
                QString::number(var.plotFlag)
            };
            content += formatIFileLine(QString::number(var.variableId), values);
        }

        content += "*======================================================================\n";
        content += "*                                小编辑\n";
        content += "*======================================================================\n";

        for (const auto& var : minorEditVars) {
            QStringList values = {
                var.variableType,
                var.componentId
            };
            content += formatIFileLine(QString::number(var.editId), values);
        }

        content += "\n";
    }

    return content;
}

QString InputData::generateIFileComponents() const
{
    QString content;

    if (getTotalComponents() > 0) {
        content += "*======================================================================\n";
        content += "*                               水力组件\n";
        content += "*======================================================================\n";

        // 生成管道组件
        for (const auto& pipe : pipes) {
            content += formatPipeComponent(pipe);
            content += "*----------------------------------------------------------------------\n";
        }
        
        // 生成分支组件
        for (const auto& branch : branches) {
            content += formatBranchComponent(branch);
            content += "*----------------------------------------------------------------------\n";
        }

        // 生成单体积组件
        for (const auto& volume : volumes) {
            content += formatSingleVolumeComponent(volume);
            content += "*----------------------------------------------------------------------\n";
        }

        // 生成连接组件
        for (const auto& junction : junctions) {
            content += formatJunctionComponent(junction);
            content += "*----------------------------------------------------------------------\n";
        }

        content += "\n";
    }

    return content;
}

QString InputData::generateIFileHeatStructures() const
{
    QString content;

    if (!heatStructures.isEmpty()) {
        content += "*======================================================================\n";
        content += "*                              热结构\n";
        content += "*======================================================================\n";

        for (const auto& hs : heatStructures) {
            content += formatHeatStructureCard(hs);
        }

        content += "\n";
    }

    return content;
}

QString InputData::generateIFileMaterials() const
{
    QString content;

    if (!materials.isEmpty()) {
        content += "*======================================================================\n";
        content += "*                              材料\n";
        content += "*======================================================================\n";

        for (const auto& material : materials) {
            content += formatIFileLine(material.materialId, {material.materialName});
        }

        content += "\n";
    }

    return content;
}

QString InputData::generateIFileTables() const
{
    QString content;

    if (!tables.isEmpty()) {
        content += "*======================================================================\n";
        content += "*                              表格\n";
        content += "*======================================================================\n";

        for (const auto& table : tables) {
            content += formatTableCard(table);
        }

        content += "\n";
    }

    return content;
}

QString InputData::generateIFileFooter() const
{
    QString footer;
    footer += "*======================================================================\n";
    footer += ".End of input\n";
    return footer;
}

// Formatting methods
QString InputData::formatIFileLine(const QString& cardId, const QStringList& values, const QString& comment) const
{
    QString line = cardId;

    for (const QString& value : values) {
        line += "   " + value;
    }

    if (!comment.isEmpty()) {
        line += "   *" + comment;
    }

    line += "\n";
    return line;
}

QString InputData::formatComponentCard(const GeometryComponent& component) const
{
    QString content;
    content += formatIFileLine(component.componentId,
                               {QString("\"%1\"").arg(component.componentName), component.componentType});

    return content;
}

QString InputData::formatPipeComponent(const PipeComponent& pipe) const
{
    QString content;

    // 组件定义行
    content += formatComponentCard(pipe);

    // 体积数量
    if (pipe.numberOfVolumes > 0) {
        content += formatIFileLine(pipe.componentId + "001", {QString::number(pipe.numberOfVolumes)});
    }

    return content;
}

QString InputData::formatBranchComponent(const BranchComponent& branch) const
{
    QString content;

    // 组件定义行
    content += formatComponentCard(branch);

    // 分支数据
    if (branch.numberOfJunctions > 0) {
        QStringList values = {
            QString::number(branch.numberOfJunctions),
            QString::number(branch.numberOfVolumes)
        };
        content += formatIFileLine(branch.componentId + "001", values);
    }

    return content;
}

QString InputData::formatSingleVolumeComponent(const SingleVolumeComponent& volume) const
{
    QString content;

    // 组件定义行
    content += formatComponentCard(volume);

    // 体积数据
    QStringList values = {
        QString::number(volume.volume, 'e', 3),
        QString::number(volume.length, 'e', 3),
        QString::number(volume.elevation, 'e', 3)
    };
    content += formatIFileLine(volume.componentId + "101", values);

    return content;
}

QString InputData::formatJunctionComponent(const JunctionComponent& junction) const
{
    QString content;

    // 组件定义行
    content += formatComponentCard(junction);

    // 连接数据
    QStringList values = {
        junction.fromComponent,
        junction.toComponent,
        QString::number(junction.area, 'e', 3),
        QString::number(junction.forwardLoss, 'e', 3),
        QString::number(junction.reverseLoss, 'e', 3),
        "000000"
    };
    content += formatIFileLine(junction.componentId + "101", values);

    return content;
}

QString InputData::formatHeatStructureCard(const HeatStructure& heatStructure) const
{
    QString content;

    QStringList values = {
        QString::number(heatStructure.numberOfAxialNodes),
        QString::number(heatStructure.numberOfRadialNodes),
        QString::number(heatStructure.geometryType),
        QString::number(heatStructure.steadyStateFlag),
        QString::number(heatStructure.leftBoundary, 'e', 3)
    };

    if (heatStructure.rightBoundary > 0) {
        values.append(QString::number(heatStructure.rightBoundary, 'e', 3));
    }

    content += formatIFileLine(heatStructure.heatStructureId + "000", values);

    return content;
}

QString InputData::formatTableCard(const TableData& table) const
{
    QString content;

    // 表格定义行
    content += formatIFileLine(table.tableId, {table.tableType});

    // 表格数据点
    for (int i = 0; i < table.dataPoints.size(); ++i) {
        const auto& point = table.dataPoints[i];
        QString pointId = table.tableId.left(6) + QString("%1").arg(i + 1, 3, 10, QChar('0'));
        QStringList values = {
            QString::number(point.first, 'e', 3),
            QString::number(point.second, 'e', 3)
        };
        content += formatIFileLine(pointId, values);
    }

    return content;
}

// Validation methods
bool InputData::validateIFileStructure() const
{
    // 检查基本结构
    if (controlCard.problemNumber <= 0) {
        const_cast<InputData*>(this)->m_errors << "缺少有效的问题控制卡";
        return false;
    }

    if (controlCard.endTime <= 0) {
        const_cast<InputData*>(this)->m_errors << "缺少有效的时间控制卡";
        return false;
    }

    return true;
}

bool InputData::validateComponentReferences() const
{
    // 验证连接组件的引用
    for (const auto& junction : junctions) {
        if (!junction.fromComponent.isEmpty()) {
            if (findComponent(junction.fromComponent) == nullptr) {
                const_cast<InputData*>(this)->m_errors <<
                    QString("连接组件 %1 引用了不存在的组件 %2")
                    .arg(junction.componentId, junction.fromComponent);
                return false;
            }
        }

        if (!junction.toComponent.isEmpty()) {
            if (findComponent(junction.toComponent) == nullptr) {
                const_cast<InputData*>(this)->m_errors <<
                    QString("连接组件 %1 引用了不存在的组件 %2")
                    .arg(junction.componentId, junction.toComponent);
                return false;
            }
        }
    }

    return true;
}

bool InputData::validateHeatStructureReferences() const
{
    // 验证热结构的边界条件引用
    for (const auto& hs : heatStructures) {
        for (const auto& bc : hs.boundaryConditions) {
            if (!bc.componentId.isEmpty()) {
                if (findComponent(bc.componentId) == nullptr) {
                    const_cast<InputData*>(this)->m_errors <<
                        QString("热结构 %1 引用了不存在的组件 %2")
                        .arg(hs.heatStructureId, bc.componentId);
                    return false;
                }
            }
        }
    }

    return true;
}

QStringList InputData::checkIFileConsistency() const
{
    QStringList issues;

    // 检查组件ID的唯一性
    QStringList allIds;
    for (const auto& pipe : pipes) allIds << pipe.componentId;
    for (const auto& branch : branches) allIds << branch.componentId;
    for (const auto& volume : volumes) allIds << volume.componentId;
    for (const auto& junction : junctions) allIds << junction.componentId;

    QStringList duplicates;
    for (int i = 0; i < allIds.size(); ++i) {
        for (int j = i + 1; j < allIds.size(); ++j) {
            if (allIds[i] == allIds[j] && !duplicates.contains(allIds[i])) {
                duplicates << allIds[i];
            }
        }
    }

    for (const QString& duplicate : duplicates) {
        issues << QString("重复的组件ID: %1").arg(duplicate);
    }

    return issues;
} 