﻿   Qt/MSBuild: 3.4.0.1
   Qt: 5.14.2
  Uic'ing src\ui\MainWindow.ui...
  Moc'ing src\ui\MainWindow.h...
  ChartWidget.cpp
  MainWindow.cpp
  main.cpp
d:\qt\source\optimizeapplication\src\main.cpp : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
d:\qt\source\optimizeapplication\src\widgets\chartwidget.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\widgets\ChartWidget.cpp)
d:\qt\source\optimizeapplication\src\widgets\customtreewidget.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\main.cpp)
d:\qt\source\optimizeapplication\src\utils\common.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\main.cpp)
d:\qt\source\optimizeapplication\src\widgets\customtreewidget.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\ui\MainWindow.cpp)
d:\qt\source\optimizeapplication\src\utils\common.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\ui\MainWindow.cpp)
d:\qt\source\optimizeapplication\src\widgets\paramwidget.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\main.cpp)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\main.cpp)
d:\qt\source\optimizeapplication\src\widgets\paramwidget.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\ui\MainWindow.cpp)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\ui\MainWindow.cpp)
d:\qt\source\optimizeapplication\src\core\configmanager.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\main.cpp)
d:\qt\source\optimizeapplication\src\utils\logger.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\main.cpp)
d:\qt\source\optimizeapplication\src\utils\logger.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\widgets\ChartWidget.cpp)
d:\qt\source\optimizeapplication\src\utils\common.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\widgets\ChartWidget.cpp)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(109): error C2065: “i”: 未声明的标识符
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(113): error C3927: "->": 非函数声明符后不允许尾随返回类型
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(113): error C3613: “->”后缺少返回类型(假定为“int”)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(113): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(113): error C2146: 语法错误: 缺少“;”(在标识符“attachAxis”的前面)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(114): error C3927: "->": 非函数声明符后不允许尾随返回类型
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(114): error C3613: “->”后缺少返回类型(假定为“int”)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(114): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(114): error C2086: “int series”: 重定义
  d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(113): note: 参见“series”的声明
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(114): error C2146: 语法错误: 缺少“;”(在标识符“attachAxis”的前面)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C3613: “->”后缺少返回类型(假定为“int”)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C3646: “info”: 未知重写说明符
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C3551: 如果使用尾随返回类型，则主要返回类型应该是单个类型说明符“自动”(而不是 "int")
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C2556: “int Logger::instance(void)”: 重载函数与“Logger *Logger::instance(void)”只是在返回类型上不同
  d:\qt\source\optimizeapplication\src\utils\logger.h(17): note: 参见“Logger::instance”的声明
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C2040: “Logger::instance”:“int (void)”与“Logger *(void)”的间接寻址级别不同
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C2059: 语法错误:“(”
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C2143: 语法错误: 缺少“)”(在“(”的前面)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C2440: “初始化”: 无法从“const char [32]”转换为“int”
  d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): note: 没有使该转换得以执行的上下文
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C2059: 语法错误:“.”
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(116): error C2059: 语法错误:“)”
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(117): error C2059: 语法错误:“}”
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(117): error C2143: 语法错误: 缺少“;”(在“}”的前面)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(120): error C2143: 语法错误: 缺少“;”(在“{”的前面)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(120): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(125): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(125): error C2143: 语法错误: 缺少“,”(在“&”的前面)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(126): error C2511: “void ChartWidget::setTitle(const int)”:“ChartWidget”中没有找到重载的成员函数
  d:\qt\source\optimizeapplication\src\widgets\chartwidget.h(13): note: 参见“ChartWidget”的声明
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(127): error C2065: “title”: 未声明的标识符
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(128): error C2065: “instance”: 未声明的标识符
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(128): error C2064: 项不会计算为接受 1 个参数的函数
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(128): error C2065: “title”: 未声明的标识符
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(131): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(131): error C2143: 语法错误: 缺少“,”(在“&”的前面)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(132): error C2511: “void ChartWidget::setXAxisLabel(const int)”:“ChartWidget”中没有找到重载的成员函数
  d:\qt\source\optimizeapplication\src\widgets\chartwidget.h(13): note: 参见“ChartWidget”的声明
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(133): error C2597: 对非静态成员“ChartWidget::axisX”的非法引用
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(134): error C2065: “label”: 未声明的标识符
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(138): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(138): error C2143: 语法错误: 缺少“,”(在“&”的前面)
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(139): error C2511: “void ChartWidget::setYAxisLabel(const int)”:“ChartWidget”中没有找到重载的成员函数
  d:\qt\source\optimizeapplication\src\widgets\chartwidget.h(13): note: 参见“ChartWidget”的声明
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(140): error C2597: 对非静态成员“ChartWidget::axisY”的非法引用
d:\qt\source\optimizeapplication\src\widgets\chartwidget.cpp(141): error C2065: “label”: 未声明的标识符
d:\qt\source\optimizeapplication\src\utils\logger.h : warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\ui\MainWindow.cpp)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(273): error C2065: “versionLabel”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(318): error C2039: “onTreeItemChanged”: 不是“MainWindow”的成员
  d:\qt\source\optimizeapplication\src\ui\mainwindow.h(32): note: 参见“MainWindow”的声明
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(318): error C2065: “onTreeItemChanged”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(324): error C2065: “m_styleComboBox”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(325): error C2355: “this”: 只能在非静态成员函数或非静态数据成员初始值设定项的内部引用
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(325): error C4430: 缺少类型说明符 - 假定为 int。注意: C++ 不支持默认 int
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(325): error C2365: “connect”: 重定义；以前的定义是“函数”
  c:\program files (x86)\windows kits\10\include\10.0.26100.0\um\winsock.h(751): note: 参见“connect”的声明
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(325): error C2248: “MainWindow::onStyleChanged”: 无法访问 private 成员(在“MainWindow”类中声明)
  d:\qt\source\optimizeapplication\src\ui\mainwindow.h(63): note: 参见“MainWindow::onStyleChanged”的声明
  d:\qt\source\optimizeapplication\src\ui\mainwindow.h(32): note: 参见“MainWindow”的声明
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(326): error C2059: 语法错误:“}”
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(326): error C2143: 语法错误: 缺少“;”(在“}”的前面)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(329): error C2143: 语法错误: 缺少“;”(在“{”的前面)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(329): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(335): error C2059: 语法错误:“}”
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(335): error C2143: 语法错误: 缺少“;”(在“}”的前面)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(338): error C2143: 语法错误: 缺少“;”(在“{”的前面)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(338): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(349): error C2065: “m_currentFile”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(351): error C2027: 使用了未定义类型“ConfigManager”
  d:\qt\source\optimizeapplication\src\core\applicationcore.h(9): note: 参见“ConfigManager”的声明
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(360): error C2065: “m_currentFile”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(365): error C2065: “m_currentFile”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(375): error C2065: “m_currentFile”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(386): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(422): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(440): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(456): error C2039: “onTreeItemChanged”: 不是“MainWindow”的成员
  d:\qt\source\optimizeapplication\src\ui\mainwindow.h(32): note: 参见“MainWindow”的声明
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(460): error C2065: “m_paramWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(466): error C3861: “tr”: 找不到标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(467): error C2065: “m_paramWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(468): error C2065: “m_stackedWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(469): error C2065: “m_stackedWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(469): error C2065: “m_paramWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(471): error C3861: “tr”: 找不到标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(472): error C2065: “m_paramWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(473): error C2065: “m_stackedWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(474): error C2065: “m_stackedWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(474): error C2065: “m_paramWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(476): error C3861: “tr”: 找不到标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(477): error C2065: “m_paramWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(478): error C2065: “m_stackedWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(479): error C2065: “m_stackedWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(479): error C2065: “m_paramWidget”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(492): error C2447: “{”: 缺少函数标题(是否是老式的形式表?)
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(512): error C2027: 使用了未定义类型“ConfigManager”
  d:\qt\source\optimizeapplication\src\core\applicationcore.h(9): note: 参见“ConfigManager”的声明
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(516): error C2065: “m_currentFile”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(605): error C2027: 使用了未定义类型“ConfigManager”
  d:\qt\source\optimizeapplication\src\core\applicationcore.h(9): note: 参见“ConfigManager”的声明
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(611): error C2065: “state”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(612): error C2065: “state”: 未声明的标识符
d:\qt\source\optimizeapplication\src\ui\mainwindow.cpp(625): error C2027: 使用了未定义类型“ConfigManager”
  d:\qt\source\optimizeapplication\src\core\applicationcore.h(9): note: 参见“ConfigManager”的声明
