#include "Common.h"
#include <QStandardPaths>
#include <QDir>
#include <QMessageBox>
#include <QApplication>

// Define application constants
namespace AppConstants {
    const QString APP_NAME = "OptimizeApplication";
    const QString APP_VERSION = "1.0.0";
    const QString ORGANIZATION_NAME = "Optimize Solutions";
    const QString ORGANIZATION_DOMAIN = "optimize-solutions.com";
    
    // Configuration file paths
    const QString CONFIG_DIR = "config";
    const QString LOG_DIR = "logs";
    const QString DATA_DIR = "data";
}

namespace Utils {

QString getAppDataPath()
{
    QString dataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    if (dataPath.isEmpty()) {
        dataPath = QDir::homePath() + "/" + AppConstants::APP_NAME;
    }
    return dataPath;
}

QString getConfigPath()
{
    QString appDataPath = getAppDataPath();
    return QDir(appDataPath).absoluteFilePath(AppConstants::CONFIG_DIR);
}

QString getLogPath()
{
    QString appDataPath = getAppDataPath();
    return QDir(appDataPath).absoluteFilePath(AppConstants::LOG_DIR);
}

bool ensureDirectoryExists(const QString& path)
{
    QDir dir;
    if (!dir.exists(path)) {
        return dir.mkpath(path);
    }
    return true;
}

void showErrorMessage(const QString& title, const QString& message)
{
    QMessageBox::critical(nullptr, title, message);
}

void showInfoMessage(const QString& title, const QString& message)
{
    QMessageBox::information(nullptr, title, message);
}

} // namespace Utils 