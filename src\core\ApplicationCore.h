#ifndef APPLICATIONCORE_H
#define APPLICATIONCORE_H

#include <QString>
#include "ConfigManager.h"

/**
 * @brief The ApplicationCore class is the central application control class
 * 
 * This singleton class manages application-wide functionality and resources.
 */
class ApplicationCore
{
public:
    /**
     * @brief Get the singleton instance
     * @return ApplicationCore instance
     */
    static ApplicationCore* instance();

    /**
     * @brief Destructor
     */
    ~ApplicationCore();

    /**
     * @brief Initialize the application core
     * @return True if initialization successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Shutdown the application core
     */
    void shutdown();

    /**
     * @brief Get the configuration manager
     * @return ConfigManager instance
     */
    ConfigManager* getConfigManager() const;

    /**
     * @brief Check if debug mode is enabled
     * @return True if debug mode is enabled, false otherwise
     */
    bool isDebugMode() const;

    /**
     * @brief Get the application data directory
     * @return Path to application data directory
     */
    QString getAppDataDir() const;

private:
    /**
     * @brief Private constructor (singleton pattern)
     */
    ApplicationCore();

    /**
     * @brief Initialize the logger
     * @return True if initialization successful, false otherwise
     */
    bool initializeLogger();

    static ApplicationCore* _instance;   ///< Singleton instance
    ConfigManager* configManager;        ///< Configuration manager
    bool debugMode;                      ///< Debug mode flag
    QString appDataDir;                  ///< Application data directory
    bool initialized = false;            ///< Flag indicating if the application core is initialized
};

#endif // APPLICATIONCORE_H 