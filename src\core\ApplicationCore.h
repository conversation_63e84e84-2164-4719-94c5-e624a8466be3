#ifndef APPLICATIONCORE_H
#define APPLICATIONCORE_H

#include <QObject>
#include <QApplication>
#include <QTranslator>
#include "Common.h"
#include "ConfigManager.h"

class Logger;

class ApplicationCore : public QObject
{
    Q_OBJECT

public:
    static ApplicationCore* instance();
    
    bool initialize();
    void shutdown();
    
    // 语言支持
    bool loadLanguage(const QString& language);
    QStringList getAvailableLanguages() const;
    QString getCurrentLanguage() const;
    
    // 主题支持
    bool loadTheme(const QString& theme);
    QStringList getAvailableThemes() const;
    QString getCurrentTheme() const;
    
    // 应用程序信息
    QString getAppName() const;
    QString getAppVersion() const;
    QString getOrganizationName() const;
    
    // 配置和日志管理器访问
    ConfigManager* getConfigManager() const;
    Logger* getLogger() const;

signals:
    void languageChanged(const QString& language);
    void themeChanged(const QString& theme);
    void applicationInitialized();
    void applicationShutdown();

private:
    explicit ApplicationCore(QObject *parent = nullptr);
    ~ApplicationCore();
    
    void initializeLogging();
    void initializeConfiguration();
    void loadApplicationSettings();
    void saveApplicationSettings();
    
    static ApplicationCore* m_instance;
    
    ConfigManager* m_configManager;
    Logger* m_logger;
    QTranslator* m_translator;
    QString m_currentLanguage;
    QString m_currentTheme;
    bool m_initialized;
};

#endif // APPLICATIONCORE_H 