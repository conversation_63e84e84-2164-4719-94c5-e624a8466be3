cmake_minimum_required(VERSION 3.14)

project(SARibbonBar VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt package
find_package(Qt5 5.14 COMPONENTS 
    Core 
    Gui 
    Widgets 
    REQUIRED
)

# Set Qt automatic MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# SARibbonBar source files
set(SARIBBONBAR_SOURCES
    SARibbonBar.cpp
    SARibbonMainWindow.cpp
    SARibbonCategory.cpp
    SARibbonPannel.cpp
    SARibbonToolButton.cpp
    SARibbonActionsManager.cpp
    SARibbonApplicationButton.cpp
    SARibbonButtonGroupWidget.cpp
    SARibbonCategoryLayout.cpp
    SARibbonCheckBox.cpp
    SARibbonComboBox.cpp
    SARibbonContextCategory.cpp
    SARibbonControlButton.cpp
    SARibbonCtrlContainer.cpp
    SARibbonCustomizeData.cpp
    SARibbonCustomizeDialog.cpp
    SARibbonCustomizeWidget.cpp
    SARibbonDrawHelper.cpp
    SARibbonElementCreateDelegate.cpp
    SARibbonElementManager.cpp
    SARibbonGallery.cpp
    SARibbonGalleryGroup.cpp
    SARibbonGalleryItem.cpp
    SARibbonLineEdit.cpp
    SARibbonLineWidgetContainer.cpp
    SARibbonMenu.cpp
    SARibbonPannelItem.cpp
    SARibbonPannelLayout.cpp
    SARibbonPannelOptionButton.cpp
    SARibbonQuickAccessBar.cpp
    SARibbonSeparatorWidget.cpp
    SARibbonStackedWidget.cpp
    SARibbonTabBar.cpp
    SAFramelessHelper.cpp
    SAWindowButtonGroup.cpp
    saribbonuser.cpp
)

# SARibbonBar header files
set(SARIBBONBAR_HEADERS
    SARibbonBar.h
    SARibbonMainWindow.h
    SARibbonCategory.h
    SARibbonPannel.h
    SARibbonToolButton.h
    SARibbonActionsManager.h
    SARibbonApplicationButton.h
    SARibbonButtonGroupWidget.h
    SARibbonCategoryLayout.h
    SARibbonCheckBox.h
    SARibbonComboBox.h
    SARibbonContextCategory.h
    SARibbonControlButton.h
    SARibbonCtrlContainer.h
    SARibbonCustomizeData.h
    SARibbonCustomizeDialog.h
    SARibbonCustomizeWidget.h
    SARibbonDrawHelper.h
    SARibbonElementCreateDelegate.h
    SARibbonElementManager.h
    SARibbonGallery.h
    SARibbonGalleryGroup.h
    SARibbonGalleryItem.h
    SARibbonGlobal.h
    SARibbonLineEdit.h
    SARibbonLineWidgetContainer.h
    SARibbonMenu.h
    SARibbonPannelItem.h
    SARibbonPannelLayout.h
    SARibbonPannelOptionButton.h
    SARibbonQuickAccessBar.h
    SARibbonSeparatorWidget.h
    SARibbonStackedWidget.h
    SARibbonTabBar.h
    SAFramelessHelper.h
    SAWindowButtonGroup.h
    saribbonuser.h
)

# Resource files
set(RESOURCE_FILES
    resource.qrc
)

# Create library
add_library(${PROJECT_NAME} STATIC 
    ${SARIBBONBAR_SOURCES}
    ${SARIBBONBAR_HEADERS}
    ${RESOURCE_FILES}
)

# SARibbonBar configuration
target_compile_definitions(${PROJECT_NAME} PRIVATE SA_RIBBON_BAR_NO_EXPORT)

# Include directories
target_include_directories(${PROJECT_NAME} PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include/SARibbonBar>
)

# Link Qt libraries
target_link_libraries(${PROJECT_NAME} 
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
)

# Compiler-specific settings
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE 
        /W4 /wd4996 /std:c++14
    )
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _CRT_SECURE_NO_WARNINGS NOMINMAX
    )
endif()
