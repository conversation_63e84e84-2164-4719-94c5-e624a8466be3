#include "PluginManager.h"
#include <QDir>
#include <QDebug>
#include <QCoreApplication>
#include <QPluginLoader>
#include <QObject>

PluginManager::PluginManager()
    : QObject(nullptr)
{
}

PluginManager::~PluginManager()
{
    cleanup();
}

bool PluginManager::initialize()
{
    // 扫描默认插件目录
    QString pluginDir = QCoreApplication::applicationDirPath() + "/plugins";
    if (QDir(pluginDir).exists()) {
        scanPluginDirectory(pluginDir);
    }

    return true;
}

void PluginManager::cleanup()
{
    // 关闭所有插件
    QMap<QString, IPlugin*>::iterator it;
    for (it = m_plugins.begin(); it != m_plugins.end(); ++it) {
        if (it.value()) {
            it.value()->shutdown();
        }
    }
    m_plugins.clear();

    // 卸载所有插件加载器
    for (int i = 0; i < m_loaders.size(); ++i) {
        QPluginLoader* loader = m_loaders[i];
        if (loader->isLoaded()) {
            loader->unload();
        }
        delete loader;
    }
    m_loaders.clear();
}

bool PluginManager::loadPlugin(const QString& path)
{
    QPluginLoader* loader = new QPluginLoader(path);
    
    if (!loader->load()) {
        qWarning() << "Failed to load plugin:" << path << loader->errorString();
        delete loader;
        return false;
    }

    QObject* pluginObject = loader->instance();
    if (!pluginObject) {
        qWarning() << "Failed to get plugin instance:" << path;
        loader->unload();
        delete loader;
        return false;
    }

    IPlugin* plugin = qobject_cast<IPlugin*>(pluginObject);
    if (!plugin) {
        qWarning() << "Plugin does not implement IPlugin interface:" << path;
        loader->unload();
        delete loader;
        return false;
    }

    QString pluginName = plugin->name();
    if (m_plugins.contains(pluginName)) {
        qWarning() << "Plugin already loaded:" << pluginName;
        loader->unload();
        delete loader;
        return false;
    }

    if (!plugin->initialize()) {
        qWarning() << "Failed to initialize plugin:" << pluginName;
        loader->unload();
        delete loader;
        return false;
    }

    m_plugins[pluginName] = plugin;
    m_loaders.append(loader);

    emit pluginLoaded(plugin);
    qDebug() << "Plugin loaded successfully:" << pluginName;

    return true;
}

bool PluginManager::unloadPlugin(const QString& name)
{
    if (!m_plugins.contains(name)) {
        return false;
    }

    IPlugin* plugin = m_plugins[name];
    plugin->shutdown();
    m_plugins.remove(name);

    // 查找并卸载对应的加载器
    for (int i = 0; i < m_loaders.size(); ++i) {
        QPluginLoader* loader = m_loaders[i];
        QObject* instance = loader->instance();
        IPlugin* pluginInstance = qobject_cast<IPlugin*>(instance);
        if (pluginInstance == plugin) {
            loader->unload();
            m_loaders.removeAt(i);
            delete loader;
            break;
        }
    }

    emit pluginUnloaded(name);
    qDebug() << "Plugin unloaded:" << name;

    return true;
}

QList<IPlugin*> PluginManager::getPlugins() const
{
    return m_plugins.values();
}

IPlugin* PluginManager::getPlugin(const QString& name) const
{
    return m_plugins.value(name, nullptr);
}

int PluginManager::scanPluginDirectory(const QString& directory)
{
    QDir pluginDir(directory);
    if (!pluginDir.exists()) {
        return 0;
    }

    int loadedCount = 0;
    QStringList filters;
    
#ifdef Q_OS_WIN
    filters << "*.dll";
#elif defined(Q_OS_MAC)
    filters << "*.dylib";
#else
    filters << "*.so";
#endif

    QStringList pluginFiles = pluginDir.entryList(filters, QDir::Files);
    for (int i = 0; i < pluginFiles.size(); ++i) {
        const QString& fileName = pluginFiles[i];
        QString fullPath = pluginDir.absoluteFilePath(fileName);
        if (loadPlugin(fullPath)) {
            loadedCount++;
        }
    }

    qDebug() << "Scanned plugin directory:" << directory << "Loaded:" << loadedCount << "plugins";
    return loadedCount;
}
