#ifndef IREPOSITORY_H
#define IREPOSITORY_H

#include <QString>
#include <QList>
#include <QMap>
#include <QVariant>
#include <functional>

/**
 * @brief 数据仓库接口
 * @tparam T 数据模型类型
 * 
 * 所有数据仓库都应该实现此接口，以提供统一的数据访问功能。
 */
template<typename T>
class IRepository
{
public:
    /**
     * @brief 虚析构函数
     */
    virtual ~IRepository() = default;

    /**
     * @brief 添加数据项
     * @param item 数据项
     * @return 是否成功添加
     */
    virtual bool add(const T& item) = 0;

    /**
     * @brief 更新数据项
     * @param item 数据项
     * @return 是否成功更新
     */
    virtual bool update(const T& item) = 0;

    /**
     * @brief 移除数据项
     * @param id 数据项ID
     * @return 是否成功移除
     */
    virtual bool remove(const QString& id) = 0;

    /**
     * @brief 获取数据项
     * @param id 数据项ID
     * @return 数据项
     */
    virtual T get(const QString& id) const = 0;

    /**
     * @brief 获取所有数据项
     * @return 数据项列表
     */
    virtual QList<T> getAll() const = 0;

    /**
     * @brief 查找数据项
     * @param predicate 查找条件
     * @return 符合条件的数据项列表
     */
    virtual QList<T> find(const std::function<bool(const T&)>& predicate) const = 0;

    /**
     * @brief 检查数据项是否存在
     * @param id 数据项ID
     * @return 是否存在
     */
    virtual bool exists(const QString& id) const = 0;

    /**
     * @brief 获取数据项数量
     * @return 数据项数量
     */
    virtual int count() const = 0;

    /**
     * @brief 清空所有数据项
     */
    virtual void clear() = 0;

    /**
     * @brief 批量添加数据项
     * @param items 数据项列表
     * @return 成功添加的数量
     */
    virtual int addBatch(const QList<T>& items) = 0;

    /**
     * @brief 批量更新数据项
     * @param items 数据项列表
     * @return 成功更新的数量
     */
    virtual int updateBatch(const QList<T>& items) = 0;

    /**
     * @brief 批量移除数据项
     * @param ids 数据项ID列表
     * @return 成功移除的数量
     */
    virtual int removeBatch(const QList<QString>& ids) = 0;

    /**
     * @brief 加载数据
     * @return 是否成功加载
     */
    virtual bool load() = 0;

    /**
     * @brief 保存数据
     * @return 是否成功保存
     */
    virtual bool save() = 0;
};

#endif // IREPOSITORY_H
