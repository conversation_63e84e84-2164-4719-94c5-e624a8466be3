// Ensure Qt include paths are correctly set
// Add to project's qmake or CMake: INCLUDEPATH += path/to/qt/include

#include "RibbonWidget.h"
#include "../core/ThemeManager.h"
#include <QWidget>
#include <QTabWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QToolButton>
#include <QGroupBox>
#include <QDebug>

RibbonWidget::RibbonWidget(QWidget *parent)
    : QWidget(parent)
    , m_ribbonTabs(new QTabWidget(this))
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(m_ribbonTabs);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    setLayout(mainLayout);

    // Set some styling to make it look more like a Ribbon
    m_ribbonTabs->setStyleSheet(R"(
        QTabWidget::pane {
            border: 1px solid #C0C0C0;
            background: #F0F0F0;
        }
        QTabBar::tab {
            background: #E0E0E0;
            border: 1px solid #C0C0C0;
            padding: 5px 10px;
            min-width: 80px;
        }
        QTabBar::tab:selected {
            background: #FFFFFF;
            border-bottom: none;
        }
    )");
}

void RibbonWidget::addTab(const QString& tabName)
{
    // Create a new widget for the tab
    QWidget* tabContent = new QWidget(m_ribbonTabs);
    QHBoxLayout* tabLayout = new QHBoxLayout(tabContent);
    tabLayout->setContentsMargins(5, 5, 5, 5);
    tabContent->setLayout(tabLayout);

    // Add the tab to the tab widget
    m_ribbonTabs->addTab(tabContent, tabName);

    // Store the tab content for later use
    m_tabContents[tabName] = tabContent;
}

void RibbonWidget::addGroup(const QString& tabName, const QString& groupName)
{
    // Find the tab content
    if (!m_tabContents.contains(tabName)) {
        qWarning() << "Tab" << tabName << "does not exist";
        return;
    }

    QWidget* tabContent = m_tabContents[tabName];
    QHBoxLayout* tabLayout = qobject_cast<QHBoxLayout*>(tabContent->layout());

    if (!tabLayout) {
        qWarning() << "Invalid tab layout";
        return;
    }

    // Create a group box
    QGroupBox* groupBox = new QGroupBox(groupName, tabContent);
    QVBoxLayout* groupLayout = new QVBoxLayout(groupBox);
    groupBox->setLayout(groupLayout);

    // Add some styling to the group box
    groupBox->setStyleSheet(R"(
        QGroupBox {
            border: 1px solid #C0C0C0;
            margin-top: 10px;
            font-weight: bold;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 3px;
        }
    )");

    // Add the group box to the tab layout
    tabLayout->addWidget(groupBox);

    // Store the group layout for later use
    m_groupLayouts[tabName + "/" + groupName] = groupLayout;
}

void RibbonWidget::applyButtonStyle(QToolButton* button, RibbonButtonStyle style)
{
    if (!button) return;

    // Get theme colors
    ThemeManager* themeManager = ThemeManager::instance();

    // Reset default styling
    button->setStyleSheet("");

    switch (style) {
        case StandardStyle:
            button->setMinimumSize(80, 60);
            button->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
            button->setStyleSheet(QString(R"(
                QToolButton {
                    background-color: %1;
                    color: %2;
                    border: 1px solid %3;
                    border-radius: 3px;
                }
                QToolButton:hover {
                    background-color: %4;
                }
            )")
            .arg(themeManager->getColor(ColorRole::ButtonBackground).name())
            .arg(themeManager->getColor(ColorRole::ButtonText).name())
            .arg(themeManager->getColor(ColorRole::Secondary).name())
            .arg(themeManager->getColor(ColorRole::Foreground).name())
            );
            break;
        
        case LargeStyle:
            button->setMinimumSize(100, 80);
            button->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
            button->setStyleSheet(QString(R"(
                QToolButton {
                    background-color: %1;
                    color: %2;
                    border: 2px solid %3;
                    border-radius: 5px;
                    padding: 5px;
                    font-weight: bold;
                }
                QToolButton:hover {
                    background-color: %4;
                }
            )")
            .arg(themeManager->getColor(ColorRole::Primary).name())
            .arg(themeManager->getColor(ColorRole::ButtonText).name())
            .arg(themeManager->getColor(ColorRole::Accent).name())
            .arg(themeManager->getColor(ColorRole::Secondary).name())
            );
            break;
        
        case CompactStyle:
            button->setMinimumSize(60, 40);
            button->setToolButtonStyle(Qt::ToolButtonIconOnly);
            button->setStyleSheet(QString(R"(
                QToolButton {
                    background-color: transparent;
                    color: %1;
                    border: none;
                }
                QToolButton:hover {
                    background-color: %2;
                }
            )")
            .arg(themeManager->getColor(ColorRole::Text).name())
            .arg(themeManager->getColor(ColorRole::Foreground).name())
            );
            break;
        
        case AccentStyle:
            button->setMinimumSize(80, 60);
            button->setToolButtonStyle(Qt::ToolButtonTextUnderIcon);
            button->setStyleSheet(QString(R"(
                QToolButton {
                    background-color: %1;
                    color: %2;
                    border: none;
                    border-radius: 5px;
                    padding: 5px;
                }
                QToolButton:hover {
                    background-color: %3;
                }
            )")
            .arg(themeManager->getColor(ColorRole::Accent).name())
            .arg(themeManager->getColor(ColorRole::ButtonText).name())
            .arg(themeManager->getColor(ColorRole::Secondary).name())
            );
            break;
    }
}

QToolButton* RibbonWidget::addButton(
    const QString& tabName, 
    const QString& groupName, 
    const QString& buttonText, 
    const QIcon& icon, 
    const QObject* receiver, 
    const char* slot,
    RibbonButtonStyle style)
{
    // Find the group layout
    QString key = tabName + "/" + groupName;
    if (!m_groupLayouts.contains(key)) {
        qWarning() << "Group" << groupName << "in tab" << tabName << "does not exist";
        return nullptr;
    }

    QVBoxLayout* groupLayout = m_groupLayouts[key];

    // Create the button
    QToolButton* button = new QToolButton(this);
    button->setText(buttonText);
    button->setIcon(icon);

    // Apply button style
    applyButtonStyle(button, style);

    // Connect signal and slot if provided
    if (receiver && slot) {
        connect(button, SIGNAL(clicked()), receiver, slot);
    }

    // Add button to the group layout
    groupLayout->addWidget(button);

    return button;
}

QToolButton* RibbonWidget::addDropdownButton(
    const QString& tabName, 
    const QString& groupName, 
    const QString& buttonText, 
    const QIcon& icon, 
    const QStringList& dropdownItems,
    const QObject* receiver, 
    const char* slot,
    RibbonButtonStyle style)
{
    // Find the group layout
    QString key = tabName + "/" + groupName;
    if (!m_groupLayouts.contains(key)) {
        qWarning() << "Group" << groupName << "in tab" << tabName << "does not exist";
        return nullptr;
    }

    QVBoxLayout* groupLayout = m_groupLayouts[key];

    // Create a tool button with dropdown
    QToolButton* button = new QToolButton();
    button->setText(buttonText);
    button->setIcon(icon);
    button->setPopupMode(QToolButton::MenuButtonPopup);

    // Apply the specified style
    applyButtonStyle(button, style);

    // Create dropdown menu
    QMenu* dropdownMenu = new QMenu(button);
    for (const QString& itemText : dropdownItems) {
        QAction* action = dropdownMenu->addAction(itemText);
        
        // If a receiver and slot are provided, connect the action
        if (receiver && slot) {
            connect(action, SIGNAL(triggered()), receiver, slot);
        }
    }

    // Set the dropdown menu
    button->setMenu(dropdownMenu);

    // Connect the main button if a receiver and slot are provided
    if (receiver && slot) {
        connect(button, SIGNAL(clicked()), receiver, slot);
    }

    // Add the button to the group layout
    groupLayout->addWidget(button);

    return button;
}

void RibbonWidget::setButtonColor(QToolButton* button, const QColor& color)
{
    if (!button) return;

    button->setStyleSheet(QString(R"(
        QToolButton {
            background-color: %1;
        }
    )").arg(color.name()));
}

void RibbonWidget::setButtonFont(QToolButton* button, const QFont& font)
{
    if (!button) return;

    button->setFont(font);
}

void RibbonWidget::setButtonTooltip(QToolButton* button, const QString& tooltip)
{
    if (!button) return;

    button->setToolTip(tooltip);
} 