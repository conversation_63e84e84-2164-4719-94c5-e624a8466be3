# Dakota Response参数完整列表

本文档详细介绍Dakota中所有response参数的完整列表和说明。

## 概述

Dakota的`responses`规范指定了在Dakota执行期间接口调用时可以返回的数据类型。规范包括三个主要组和两个可选关键字。

## 主要Response类型（必选其一）

### 1. objective_functions
**用途**: 适用于优化问题的响应类型
**别名**: `num_objective_functions`
**参数**: INTEGER（目标函数数量）

**子关键字**:
- `sense` - 指定每个目标函数是最小化还是最大化
- `primary_scale_types` - 如何缩放每个目标函数
- `primary_scales` - 缩放每个目标函数的特征值
- `weights` - 为每个目标函数指定权重
- `nonlinear_inequality_constraints` - 指定非线性不等式约束
- `nonlinear_equality_constraints` - 指定非线性等式约束
- `scalar_objectives` - 标量目标函数数量
- `field_objectives` - 场目标函数数量

**应用场景**: 用于优化算法，包括单目标和多目标优化

### 2. calibration_terms
**用途**: 适用于校准或最小二乘的响应类型
**别名**: `least_squares_terms`, `num_least_squares_terms`
**参数**: INTEGER（校准项数量）

**子关键字**:
- `scalar_calibration_terms` - 标量校准项数量
- `field_calibration_terms` - 场校准项数量
- `primary_scales` - 缩放每个校准项的特征值
- `weights` - 为每个目标函数指定权重
- `calibration_data` - 提供场或混合场/标量校准数据
- `calibration_data_file` - 仅提供标量校准数据
- `simulation_variance` - 应用于仿真响应的方差
- `nonlinear_inequality_constraints` - 指定非线性不等式约束
- `nonlinear_equality_constraints` - 指定非线性等式约束

**应用场景**: 用于参数校准和最小二乘优化

### 3. response_functions
**用途**: 通用响应类型
**别名**: `num_response_functions`
**参数**: INTEGER（响应函数数量）

**子关键字**:
- `scalar_responses` - 标量响应函数数量
- `field_responses` - 场响应函数数量

**应用场景**: 用于不确定性量化、参数研究和实验设计

## 梯度类型（必选其一）

### 1. no_gradients
**说明**: 不使用梯度信息

### 2. analytic_gradients
**说明**: 分析驱动器将返回梯度

### 3. numerical_gradients
**说明**: 需要梯度并将通过有限差分近似
**子关键字**:
- `method_source` - 指定使用哪个有限差分例程
- `dakota` - 使用Dakota的有限差分例程
  - `ignore_bounds` - 计算梯度时不考虑边界
  - `relative` - 按参数值缩放步长（默认）
  - `absolute` - 不缩放步长
  - `bounds` - 按参数域缩放步长
- `fd_step_size` - 有限差分步长

### 4. mixed_gradients
**说明**: 需要梯度并将从数值和解析源的混合中获得
**子关键字**:
- `id_numerical_gradients` - 标识哪个数值梯度对应哪个响应
- `id_analytic_gradients` - 标识哪个解析梯度对应哪个响应
- `dakota` - Dakota有限差分设置
- `fd_step_size` - 有限差分步长

## Hessian类型（必选其一）

### 1. no_hessians
**说明**: 不使用Hessian信息

### 2. numerical_hessians
**说明**: 需要Hessian并将通过有限差分近似
**子关键字**:
- `dakota` - 使用Dakota的有限差分例程
- `fd_step_size` - 有限差分步长
- `central` - 使用中心差分
- `forward` - 使用前向差分（默认）

### 3. quasi_hessians
**说明**: 需要Hessian并将通过割线更新近似
**子关键字**:
- `bfgs` - 使用BFGS更新（默认）
- `sr1` - 使用SR1更新
- `damped` - 阻尼BFGS更新

### 4. analytic_hessians
**说明**: 需要Hessian并可直接从分析驱动器获得

### 5. mixed_hessians
**说明**: 需要Hessian并将从数值、解析和"准"源的混合中获得
**子关键字**:
- `id_numerical_hessians` - 标识数值Hessian
- `id_analytic_hessians` - 标识解析Hessian
- `id_quasi_hessians` - 标识准Hessian

## 可选参数

### 1. id_responses
**说明**: 命名响应块；当有多个时很有用

### 2. descriptors
**说明**: 响应的标签
**别名**: `response_descriptors`
**参数**: STRINGLIST

**默认标签**:
- 目标函数: `obj_fn_i`
- 校准项: `least_sq_term_i`
- 非线性不等式约束: `nln_ineq_con_i`
- 非线性等式约束: `nln_eq_con_i`
- 响应函数: `response_fn_i`

### 3. metadata
**说明**: 浮点响应元数据的标签（实验性）
**参数**: STRINGLIST

## 场响应相关参数

### field_responses / field_calibration_terms / field_objectives
**子关键字**:
- `lengths` - 场响应的长度
- `num_coordinates_per_field` - 每个场响应的独立坐标数
- `read_field_coordinates` - 指示应读取场坐标的标志

## 约束相关参数

### nonlinear_inequality_constraints
**子关键字**:
- `lower_bounds` - 指定最小值
- `upper_bounds` - 指定最大值
- `scale_types` - 如何缩放约束
- `scales` - 缩放约束的特征值

### nonlinear_equality_constraints
**子关键字**:
- `targets` - 等式约束的目标值
- `scale_types` - 如何缩放约束
- `scales` - 缩放约束的特征值

## 校准数据相关参数

### calibration_data
**子关键字**:
- `data_directory` - 包含校准场数据文件的目录
- `num_experiments` - 不同实验的数量
- `num_config_variables` - 配置变量的数量
- `experiment_variance_type` - 实验误差类型
- `scalar_data_file` - 标量数据文件
- `interpolate` - 仿真值插值标志

### calibration_data_file
**子关键字**:
- `annotated` - 带注释的表格文件格式
- `custom_annotated` - 自定义注释表格文件格式
- `freeform` - 自由格式表格文件格式
- `num_experiments` - 实验数量
- `num_config_variables` - 配置变量数量
- `experiment_variance_type` - 实验误差类型

## 缩放和权重参数

### 缩放类型
- `value` - 按特征值缩放
- `log` - 对数缩放
- `auto` - 自动缩放（仅适用于约束）

### 权重和缩放
- `weights` - 指定权重（用于多目标优化和校准）
- `primary_scales` - 主要响应的缩放值
- `simulation_variance` - 仿真方差

## 使用说明

1. **响应类型选择**: 根据分析方法选择合适的响应类型
   - 优化问题使用`objective_functions`
   - 参数校准使用`calibration_terms`
   - 不确定性量化使用`response_functions`

2. **梯度和Hessian**: 根据方法需求选择
   - 基于梯度的优化需要梯度信息
   - 二阶方法需要Hessian信息
   - 不确定性量化通常不需要导数信息

3. **数据格式**: 响应数据必须按特定顺序返回
   - 主要响应（目标函数/校准项/响应函数）
   - 非线性不等式约束
   - 非线性等式约束

4. **缩放建议**: 当响应值量级差异很大时使用缩放
   - 有助于数值稳定性
   - 改善收敛性能

## 示例配置

### 优化问题
```
responses
  objective_functions = 1
    descriptors = 'mass'
  nonlinear_inequality_constraints = 2
    descriptors = 'stress' 'displacement'
  no_gradients
  no_hessians
```

### 校准问题
```
responses
  calibration_terms = 5
    descriptors = 'temp1' 'temp2' 'temp3' 'temp4' 'temp5'
    calibration_data_file = 'experiment.dat'
      annotated
  numerical_gradients
    dakota
  no_hessians
```

### 不确定性量化
```
responses
  response_functions = 3
    descriptors = 'stress' 'displacement' 'frequency'
  no_gradients
  no_hessians
``` 