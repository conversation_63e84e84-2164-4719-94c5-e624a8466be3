﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Form Files">
      <UniqueIdentifier>{99349809-55BA-4b9d-BF79-8FDBB0286EB3}</UniqueIdentifier>
      <Extensions>ui</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Form Files">
      <UniqueIdentifier>{99349809-55BA-4b9d-BF79-8FDBB0286EB3}</UniqueIdentifier>
      <Extensions>ui</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Generated Files">
      <UniqueIdentifier>{71ED8ED8-ACB9-4CE9-BBE1-E00B30144E11}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;moc;h;def;odl;idl;res;</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{D9D6E242-F8AF-46E4-B9FD-80ECBC20BA3E}</UniqueIdentifier>
      <Extensions>qrc;*</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{D9D6E242-F8AF-46E4-B9FD-80ECBC20BA3E}</UniqueIdentifier>
      <Extensions>qrc;*</Extensions>
      <ParseFiles>false</ParseFiles>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\core\ApplicationCore.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\widgets\ChartWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\core\ConfigManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\widgets\CustomTreeWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\utils\Logger.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\ui\MainWindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\widgets\ParamWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\utils\Utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="src\core\ApplicationCore.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="src\widgets\ChartWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <ClInclude Include="src\utils\Common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <QtMoc Include="src\core\ConfigManager.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="src\widgets\CustomTreeWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="src\utils\Logger.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="src\ui\MainWindow.h">
      <Filter>Header Files</Filter>
    </QtMoc>
    <QtMoc Include="src\widgets\ParamWidget.h">
      <Filter>Header Files</Filter>
    </QtMoc>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="debug\moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
    <CustomBuild Include="release\moc_predefs.h.cbt">
      <Filter>Generated Files</Filter>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <QtUic Include="src\ui\MainWindow.ui">
      <Filter>Form Files</Filter>
    </QtUic>
  </ItemGroup>
  <ItemGroup>
    <None Include="resources\icons\copy.png">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="resources\icons\cut.png">
      <Filter>Resource Files</Filter>
    </None>
    <QtRcc Include="resources\icons.qrc">
      <Filter>Resource Files</Filter>
    </QtRcc>
    <None Include="resources\icons\new.png">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="resources\icons\open.png">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="resources\icons\paste.png">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="resources\icons\save.png">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include=".\OptimizeApplication_debug_resource.rc" />
  </ItemGroup>
</Project>