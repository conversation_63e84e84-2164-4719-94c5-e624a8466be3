#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include "../core/ConfigManager.h"
#include "../SARibbonBar/SARibbonMainWindow.h"
#include "../SARibbonBar/SARibbonBar.h"
#include "../SARibbonBar/SARibbonCategory.h"
#include "../SARibbonBar/SARibbonPannel.h"
#include "../SARibbonBar/SARibbonContextCategory.h"
#include "../SARibbonBar/SARibbonGallery.h"
#include "../SARibbonBar/SARibbonButtonGroupWidget.h"
#include "../SARibbonBar/SARibbonQuickAccessBar.h"
#include "../SARibbonBar/SARibbonMenu.h"
#include "../widgets/RibbonOptimizeWidget.h"
#include "../widgets/SensitivityWidget.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

/**
 * @brief The MainWindow class is the main application window
 */
class MainWindow : public SARibbonMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent widget
     */
    MainWindow(QWidget *parent = nullptr);
    
    /**
     * @brief Destructor
     */
    ~MainWindow();

protected:
    /**
     * @brief Event handler for close events
     * @param event The close event
     */
    void closeEvent(QCloseEvent *event) override;

private Q_SLOTS:
    /**
     * @brief Handle the "New" action
     */
    void on_actionNew_triggered();
    
    /**
     * @brief Handle the "Open" action
     */
    void on_actionOpen_triggered();
    
    /**
     * @brief Handle the "Save" action
     */
    void on_actionSave_triggered();
    
    /**
     * @brief Handle the "Save As" action
     */
    void on_actionSaveAs_triggered();
    
    /**
     * @brief Handle the "Exit" action
     */
    void on_actionExit_triggered();
    
    /**
     * @brief Handle the "About" action
     */
    void on_actionAbout_triggered();
    
    /**
     * @brief Handle recent file selection
     */
    void openRecentFile();
    
    /**
     * @brief Update recent files menu
     */
    void updateRecentFilesMenu();
    
    /**
     * @brief Handle tab change
     * @param index The new tab index
     */
    void onTabChanged(int index);

private:
    Ui::MainWindow *ui;                 ///< UI object
    QString currentFilePath;            ///< Path to the currently open file
    bool fileModified;                  ///< Flag indicating if the file has been modified
    
    // Ribbon bar
    SARibbonBar* ribbonBar;             ///< Ribbon bar
    SARibbonCategory* homeCategory;     ///< Home category
    SARibbonCategory* viewCategory;     ///< View category
    SARibbonCategory* toolsCategory;    ///< Tools category
    SARibbonCategory* optimizeCategory; ///< Optimize category
    SARibbonCategory* sensitivityCategory; ///< Sensitivity category
    
    // Widgets
    RibbonOptimizeWidget* optimizeWidget; ///< Optimize widget
    SensitivityWidget* sensitivityWidget; ///< Sensitivity widget
    QTabWidget* tabWidget;              ///< Main tab widget
    
    /**
     * @brief Create menu and toolbar actions
     */
    void createActions();
    
    /**
     * @brief Setup the ribbon interface
     */
    void setupRibbon();
    
    /**
     * @brief Setup home category
     */
    void setupHomeCategory();
    
    /**
     * @brief Setup view category
     */
    void setupViewCategory();
    
    /**
     * @brief Setup tools category
     */
    void setupToolsCategory();
    
    /**
     * @brief Create status bar
     */
    void createStatusBar();
    
    /**
     * @brief Setup the central widget
     */
    void setupCentralWidget();
    
    /**
     * @brief Setup connections
     */
    void setupConnections();
    
    /**
     * @brief Save file
     * @param filePath Path to save to
     * @return True if save was successful, false otherwise
     */
    bool saveFile(const QString &filePath);
    
    /**
     * @brief Load file
     * @param filePath Path to load from
     * @return True if load was successful, false otherwise
     */
    bool loadFile(const QString &filePath);
    
    /**
     * @brief Check if it's safe to proceed with an action that would discard changes
     * @return True if it's safe to proceed, false otherwise
     */
    bool maybeSave();
    
    /**
     * @brief Set the current file path
     * @param filePath The file path
     */
    void setCurrentFile(const QString &filePath);
    
    /**
     * @brief Set the window title based on the current file
     * @param filePath The file path
     */
    void updateWindowTitle(const QString &filePath = QString());
    
    /**
     * @brief Set the modified state
     * @param modified True if the file has been modified, false otherwise
     */
    void setModified(bool modified);
    
    // Menus
    QMenu *fileMenu;                    ///< File menu
    QMenu *recentFilesMenu;             ///< Recent files menu
    
    // Actions
    QAction *newAction;                 ///< New action
    QAction *openAction;                ///< Open action
    QAction *saveAction;                ///< Save action
    QAction *saveAsAction;              ///< Save As action
    QAction *exitAction;                ///< Exit action
    QAction *aboutAction;               ///< About action
    QAction *aboutQtAction;             ///< About Qt action
    QList<QAction *> recentFileActions; ///< Recent file actions
};

#endif // MAINWINDOW_H 