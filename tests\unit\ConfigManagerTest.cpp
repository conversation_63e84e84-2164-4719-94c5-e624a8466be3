#include "ConfigManagerTest.h"
#include <QSignalSpy>
#include <QThread>
#include <QFile>
#include <QSettings>
#include <QElapsedTimer>
#include <QCoreApplication>
#include <QJsonDocument>
#include <QJsonObject>
#include <QtConcurrent>

void ConfigManagerTest::initTestCase()
{
    // 创建临时目录
    m_tempDir = new QTemporaryDir();
    QVERIFY(m_tempDir->isValid());
    
    // 设置测试配置文件路径
    m_testConfigPath = m_tempDir->path() + "/test_config.ini";
    
    // 创建ConfigManager实例
    m_configManager = new ConfigManager(m_testConfigPath);
    QVERIFY(m_configManager != nullptr);
}

void ConfigManagerTest::cleanupTestCase()
{
    delete m_configManager;
    delete m_tempDir;
}

void ConfigManagerTest::init()
{
    // 清空配置
    m_configManager->clear();
    
    // 设置一些初始配置
    m_configManager->setValue("test_string", "test_value");
    m_configManager->setValue("test_int", 123);
    m_configManager->setValue("test_double", 3.14);
    m_configManager->setValue("test_bool", true);
}

void ConfigManagerTest::cleanup()
{
    // 清空配置
    m_configManager->clear();
}

void ConfigManagerTest::testGetValue()
{
    QFETCH(QString, key);
    QFETCH(QVariant, expectedValue);
    QFETCH(QVariant, defaultValue);
    
    QVariant value = m_configManager->getValue(key, defaultValue);
    QCOMPARE(value, expectedValue);
}

void ConfigManagerTest::testGetValue_data()
{
    QTest::addColumn<QString>("key");
    QTest::addColumn<QVariant>("expectedValue");
    QTest::addColumn<QVariant>("defaultValue");
    
    QTest::newRow("existing string") << "test_string" << QVariant("test_value") << QVariant();
    QTest::newRow("existing int") << "test_int" << QVariant(123) << QVariant();
    QTest::newRow("existing double") << "test_double" << QVariant(3.14) << QVariant();
    QTest::newRow("existing bool") << "test_bool" << QVariant(true) << QVariant();
    QTest::newRow("non-existent with default") << "non_existent" << QVariant("default") << QVariant("default");
    QTest::newRow("non-existent without default") << "non_existent" << QVariant() << QVariant();
}

void ConfigManagerTest::testSetValue()
{
    // 测试设置新值
    m_configManager->setValue("new_key", "new_value");
    QCOMPARE(m_configManager->getValue("new_key").toString(), QString("new_value"));
    
    // 测试更新现有值
    m_configManager->setValue("test_string", "updated_value");
    QCOMPARE(m_configManager->getValue("test_string").toString(), QString("updated_value"));
    
    // 测试设置不同类型的值
    m_configManager->setValue("test_list", QStringList() << "item1" << "item2");
    QStringList list = m_configManager->getValue("test_list").toStringList();
    QCOMPARE(list.size(), 2);
    QCOMPARE(list[0], QString("item1"));
    QCOMPARE(list[1], QString("item2"));
    
    // 测试设置复杂类型
    QVariantMap map;
    map["key1"] = "value1";
    map["key2"] = 123;
    m_configManager->setValue("test_map", map);
    QVariantMap retrievedMap = m_configManager->getValue("test_map").toMap();
    QCOMPARE(retrievedMap.size(), 2);
    QCOMPARE(retrievedMap["key1"].toString(), QString("value1"));
    QCOMPARE(retrievedMap["key2"].toInt(), 123);
}

void ConfigManagerTest::testLoadConfig()
{
    // 保存当前配置
    QVERIFY(m_configManager->save());
    
    // 创建新的ConfigManager实例
    ConfigManager newManager(m_testConfigPath);
    
    // 加载配置
    QVERIFY(newManager.load());
    
    // 验证加载的配置
    QCOMPARE(newManager.getValue("test_string").toString(), QString("test_value"));
    QCOMPARE(newManager.getValue("test_int").toInt(), 123);
    QCOMPARE(newManager.getValue("test_double").toDouble(), 3.14);
    QCOMPARE(newManager.getValue("test_bool").toBool(), true);
    
    // 测试加载无效配置文件
    ConfigManager invalidManager(m_tempDir->path() + "/non_existent.ini");
    QVERIFY(!invalidManager.load());
}

void ConfigManagerTest::testSaveConfig()
{
    // 保存配置
    QVERIFY(m_configManager->save());
    
    // 验证配置文件内容
    QVariantMap expectedContent;
    expectedContent["test_string"] = "test_value";
    expectedContent["test_int"] = 123;
    expectedContent["test_double"] = 3.14;
    expectedContent["test_bool"] = true;
    QVERIFY(verifyConfigFileContent(m_testConfigPath, expectedContent));
    
    // 修改配置并再次保存
    m_configManager->setValue("new_key", "new_value");
    QVERIFY(m_configManager->save());
    
    // 验证更新后的配置文件内容
    expectedContent["new_key"] = "new_value";
    QVERIFY(verifyConfigFileContent(m_testConfigPath, expectedContent));
}

void ConfigManagerTest::testContainsKey()
{
    // 测试存在的键
    QVERIFY(m_configManager->containsKey("test_string"));
    QVERIFY(m_configManager->containsKey("test_int"));
    QVERIFY(m_configManager->containsKey("test_double"));
    QVERIFY(m_configManager->containsKey("test_bool"));
    
    // 测试不存在的键
    QVERIFY(!m_configManager->containsKey("non_existent"));
    
    // 添加新键并测试
    m_configManager->setValue("new_key", "new_value");
    QVERIFY(m_configManager->containsKey("new_key"));
    
    // 移除键并测试
    m_configManager->removeKey("test_string");
    QVERIFY(!m_configManager->containsKey("test_string"));
}

void ConfigManagerTest::testRemoveKey()
{
    // 移除现有键
    QVERIFY(m_configManager->containsKey("test_string"));
    m_configManager->removeKey("test_string");
    QVERIFY(!m_configManager->containsKey("test_string"));
    
    // 移除不存在的键
    m_configManager->removeKey("non_existent");
    QVERIFY(!m_configManager->containsKey("non_existent"));
    
    // 移除键后保存并验证
    QVERIFY(m_configManager->save());
    QVariantMap expectedContent;
    expectedContent["test_int"] = 123;
    expectedContent["test_double"] = 3.14;
    expectedContent["test_bool"] = true;
    QVERIFY(verifyConfigFileContent(m_testConfigPath, expectedContent));
}

void ConfigManagerTest::testClear()
{
    // 清空配置
    m_configManager->clear();
    
    // 验证所有键都已移除
    QVERIFY(!m_configManager->containsKey("test_string"));
    QVERIFY(!m_configManager->containsKey("test_int"));
    QVERIFY(!m_configManager->containsKey("test_double"));
    QVERIFY(!m_configManager->containsKey("test_bool"));
    
    // 验证键列表为空
    QVERIFY(m_configManager->keys().isEmpty());
    
    // 清空后保存并验证
    QVERIFY(m_configManager->save());
    QVariantMap expectedContent;
    QVERIFY(verifyConfigFileContent(m_testConfigPath, expectedContent));
}

void ConfigManagerTest::testKeys()
{
    // 获取所有键
    QStringList keys = m_configManager->keys();
    
    // 验证键列表
    QCOMPARE(keys.size(), 4);
    QVERIFY(keys.contains("test_string"));
    QVERIFY(keys.contains("test_int"));
    QVERIFY(keys.contains("test_double"));
    QVERIFY(keys.contains("test_bool"));
    
    // 添加新键并验证
    m_configManager->setValue("new_key", "new_value");
    keys = m_configManager->keys();
    QCOMPARE(keys.size(), 5);
    QVERIFY(keys.contains("new_key"));
    
    // 移除键并验证
    m_configManager->removeKey("test_string");
    keys = m_configManager->keys();
    QCOMPARE(keys.size(), 4);
    QVERIFY(!keys.contains("test_string"));
    
    // 清空配置并验证
    m_configManager->clear();
    keys = m_configManager->keys();
    QVERIFY(keys.isEmpty());
}

void ConfigManagerTest::testValueChangedSignal()
{
    // 监听valueChanged信号
    QSignalSpy spy(m_configManager, SIGNAL(valueChanged(const QString&, const QVariant&)));
    
    // 设置新值
    m_configManager->setValue("new_key", "new_value");
    
    // 验证信号
    QCOMPARE(spy.count(), 1);
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), QString("new_key"));
    QCOMPARE(arguments.at(1).toString(), QString("new_value"));
    
    // 更新现有值
    m_configManager->setValue("test_string", "updated_value");
    
    // 验证信号
    QCOMPARE(spy.count(), 1);
    arguments = spy.takeFirst();
    QCOMPARE(arguments.at(0).toString(), QString("test_string"));
    QCOMPARE(arguments.at(1).toString(), QString("updated_value"));
    
    // 设置相同的值（不应触发信号）
    m_configManager->setValue("test_string", "updated_value");
    QCOMPARE(spy.count(), 0);
}

void ConfigManagerTest::testThreadSafety()
{
    // 创建多个线程同时访问ConfigManager
    const int threadCount = 10;
    const int operationsPerThread = 100;
    
    QList<QFuture<void>> futures;
    
    for (int i = 0; i < threadCount; ++i) {
        futures.append(QtConcurrent::run([this, i, operationsPerThread]() {
            for (int j = 0; j < operationsPerThread; ++j) {
                QString key = QString("thread_%1_key_%2").arg(i).arg(j);
                QString value = QString("value_%1_%2").arg(i).arg(j);
                
                // 设置值
                m_configManager->setValue(key, value);
                
                // 获取值
                QVariant retrievedValue = m_configManager->getValue(key);
                
                // 验证值
                if (retrievedValue.toString() != value) {
                    qWarning() << "Thread safety test failed: expected" << value << "but got" << retrievedValue.toString();
                }
            }
        }));
    }
    
    // 等待所有线程完成
    for (QFuture<void>& future : futures) {
        future.waitForFinished();
    }
    
    // 验证所有值
    for (int i = 0; i < threadCount; ++i) {
        for (int j = 0; j < operationsPerThread; ++j) {
            QString key = QString("thread_%1_key_%2").arg(i).arg(j);
            QString expectedValue = QString("value_%1_%2").arg(i).arg(j);
            
            QVariant value = m_configManager->getValue(key);
            QCOMPARE(value.toString(), expectedValue);
        }
    }
}

void ConfigManagerTest::testErrorHandling()
{
    // 测试无效路径
    ConfigManager invalidManager("/invalid/path/config.ini");
    QVERIFY(!invalidManager.save());
    
    // 测试只读文件
    QString readOnlyPath = m_tempDir->path() + "/readonly.ini";
    QFile file(readOnlyPath);
    QVERIFY(file.open(QIODevice::WriteOnly));
    file.close();
    
    // 设置为只读
    QVERIFY(file.setPermissions(QFile::ReadOwner));
    
    ConfigManager readOnlyManager(readOnlyPath);
    QVERIFY(!readOnlyManager.save());
}

void ConfigManagerTest::testPerformance()
{
    // 测试大量配置项的性能
    const int itemCount = 1000;
    
    // 测试设置性能
    QElapsedTimer timer;
    timer.start();
    
    for (int i = 0; i < itemCount; ++i) {
        QString key = QString("perf_key_%1").arg(i);
        QString value = QString("perf_value_%1").arg(i);
        m_configManager->setValue(key, value);
    }
    
    qint64 setTime = timer.elapsed();
    qDebug() << "Time to set" << itemCount << "items:" << setTime << "ms";
    
    // 测试获取性能
    timer.restart();
    
    for (int i = 0; i < itemCount; ++i) {
        QString key = QString("perf_key_%1").arg(i);
        m_configManager->getValue(key);
    }
    
    qint64 getTime = timer.elapsed();
    qDebug() << "Time to get" << itemCount << "items:" << getTime << "ms";
    
    // 测试保存性能
    timer.restart();
    m_configManager->save();
    qint64 saveTime = timer.elapsed();
    qDebug() << "Time to save" << itemCount << "items:" << saveTime << "ms";
    
    // 测试加载性能
    ConfigManager newManager(m_testConfigPath);
    timer.restart();
    newManager.load();
    qint64 loadTime = timer.elapsed();
    qDebug() << "Time to load" << itemCount << "items:" << loadTime << "ms";
    
    // 性能应该在合理范围内
    QVERIFY(setTime < 1000);  // 设置1000项应该不超过1秒
    QVERIFY(getTime < 1000);  // 获取1000项应该不超过1秒
    QVERIFY(saveTime < 1000); // 保存1000项应该不超过1秒
    QVERIFY(loadTime < 1000); // 加载1000项应该不超过1秒
}

QString ConfigManagerTest::createTestConfigFile(const QString& content)
{
    QString filePath = m_tempDir->path() + "/test_config_" + QUuid::createUuid().toString(QUuid::WithoutBraces) + ".ini";
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out << content;
        file.close();
        return filePath;
    }
    return QString();
}

bool ConfigManagerTest::verifyConfigFileContent(const QString& filePath, const QVariantMap& expectedContent)
{
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 检查所有期望的键值对
    for (auto it = expectedContent.begin(); it != expectedContent.end(); ++it) {
        if (!settings.contains(it.key()) || settings.value(it.key()) != it.value()) {
            return false;
        }
    }
    
    // 检查是否有多余的键
    QStringList keys = settings.allKeys();
    if (keys.size() != expectedContent.size()) {
        return false;
    }
    
    return true;
}

// 使用QTEST_MAIN宏注册测试类
QTEST_MAIN(ConfigManagerTest)
