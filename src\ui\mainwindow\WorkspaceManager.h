#ifndef WORKSPACEMANAGER_H
#define WORKSPACEMANAGER_H

#include <QObject>
#include <QWidget>
#include <QMap>
#include <QString>

class QSplitter;
class QStackedWidget;
class QTabWidget;

/**
 * @brief 工作区管理器类，负责管理中央工作区域
 * 
 * WorkspaceManager提供了工作区的创建、管理和切换功能，
 * 支持多种布局模式和控件管理。
 */
class WorkspaceManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 布局模式枚举
     */
    enum class LayoutMode {
        Splitter,   // 分割器布局
        Tabbed,     // 标签页布局
        Stacked     // 堆叠布局
    };

    /**
     * @brief 构造函数
     * @param parent 父控件
     */
    explicit WorkspaceManager(QWidget* parent);

    /**
     * @brief 析构函数
     */
    ~WorkspaceManager();

    /**
     * @brief 获取中央控件
     * @return 中央控件指针
     */
    QWidget* centralWidget() const;

    /**
     * @brief 设置布局模式
     * @param mode 布局模式
     */
    void setLayoutMode(LayoutMode mode);

    /**
     * @brief 获取当前布局模式
     * @return 布局模式
     */
    LayoutMode layoutMode() const;

    /**
     * @brief 添加控件
     * @param widget 控件指针
     * @param name 控件名称
     * @param title 控件标题
     */
    void addWidget(QWidget* widget, const QString& name, const QString& title = QString());

    /**
     * @brief 移除控件
     * @param name 控件名称
     * @return 是否成功移除
     */
    bool removeWidget(const QString& name);

    /**
     * @brief 设置当前控件
     * @param name 控件名称
     * @return 是否成功设置
     */
    bool setCurrentWidget(const QString& name);

    /**
     * @brief 获取当前控件名称
     * @return 当前控件名称
     */
    QString currentWidgetName() const;

    /**
     * @brief 获取控件
     * @param name 控件名称
     * @return 控件指针
     */
    QWidget* getWidget(const QString& name) const;

    /**
     * @brief 获取所有控件名称
     * @return 控件名称列表
     */
    QStringList widgetNames() const;

    /**
     * @brief 设置控件可见性
     * @param name 控件名称
     * @param visible 是否可见
     */
    void setWidgetVisible(const QString& name, bool visible);

    /**
     * @brief 检查控件是否可见
     * @param name 控件名称
     * @return 是否可见
     */
    bool isWidgetVisible(const QString& name) const;

    /**
     * @brief 设置分割器比例
     * @param sizes 分割器大小列表
     */
    void setSplitterSizes(const QList<int>& sizes);

    /**
     * @brief 获取分割器比例
     * @return 分割器大小列表
     */
    QList<int> splitterSizes() const;

signals:
    /**
     * @brief 当前控件变更信号
     * @param name 新的当前控件名称
     */
    void currentWidgetChanged(const QString& name);

    /**
     * @brief 控件添加信号
     * @param name 控件名称
     */
    void widgetAdded(const QString& name);

    /**
     * @brief 控件移除信号
     * @param name 控件名称
     */
    void widgetRemoved(const QString& name);

private:
    /**
     * @brief 创建分割器布局
     */
    void createSplitterLayout();

    /**
     * @brief 创建标签页布局
     */
    void createTabbedLayout();

    /**
     * @brief 创建堆叠布局
     */
    void createStackedLayout();

    /**
     * @brief 清理当前布局
     */
    void clearLayout();

    QWidget* m_parent;
    QWidget* m_centralWidget;
    LayoutMode m_layoutMode;
    
    // 布局控件
    QSplitter* m_splitter;
    QTabWidget* m_tabWidget;
    QStackedWidget* m_stackedWidget;
    
    // 控件管理
    QMap<QString, QWidget*> m_widgets;
    QMap<QString, QString> m_widgetTitles;
    QString m_currentWidgetName;
};

#endif // WORKSPACEMANAGER_H
