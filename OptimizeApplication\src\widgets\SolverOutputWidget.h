#ifndef SOLVEROUTPUTWIDGET_H
#define SOLVEROUTPUTWIDGET_H

#include <QWidget>
#include <QGroupBox>
#include <QFormLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QTextEdit>
#include <QProgressBar>
#include <QListWidget>
#include <QTableWidget>
#include <QFileDialog>
#include <QHeaderView>

QT_BEGIN_NAMESPACE
class QLineEdit;
class QComboBox;
class QSpinBox;
class QCheckBox;
class QPushButton;
class QListWidget;
class QTextEdit;
class QProgressBar;
class QLabel;
class QTableWidget;
QT_END_NAMESPACE

namespace Ui {
class SolverOutputWidget;
}

class SolverOutputWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SolverOutputWidget(QWidget *parent = nullptr);
    ~SolverOutputWidget();

    // Output directory and file settings
    QString getOutputDirectory() const;
    QString getFileNamePattern() const;
    QString getOutputFormat() const;
    void setOutputDirectory(const QString &directory);
    void setFileNamePattern(const QString &pattern);
    void setOutputFormat(const QString &format);
    
    // Auto-save settings
    bool isAutoSaveEnabled() const;
    int getAutoSaveInterval() const;
    void setAutoSaveEnabled(bool enabled);
    void setAutoSaveInterval(int interval);
    
    // Compression settings
    bool isCompressionEnabled() const;
    QString getCompressionLevel() const;
    void setCompressionEnabled(bool enabled);
    void setCompressionLevel(const QString &level);
    
    // Report settings
    bool isReportEnabled() const;
    QString getReportFormat() const;
    bool includeCharts() const;
    bool includeStatistics() const;
    void setReportEnabled(bool enabled);
    void setReportFormat(const QString &format);
    void setIncludeCharts(bool include);
    void setIncludeStatistics(bool include);
    
    // Output variables management
    void addOutputVariable(const QString &name, const QString &type, const QString &description);
    void updateOutputVariable(int row, const QString &name, const QString &type, const QString &description);
    void deleteOutputVariable(int row);
    void clearOutputVariables();
    
    // Data access methods
    QStringList getOutputVariableNames() const;
    int getOutputVariableCount() const;

signals:
    void parametersChanged();
    void outputVariableAdded(const QString &name);
    void outputVariableRemoved(const QString &name);

public slots:
    // Output directory slots
    void onBrowseOutputDirectory();
    void onOutputDirectoryChanged();
    void onFileNamePatternChanged();
    void onOutputFormatChanged();
    
    // Auto-save slots
    void onAutoSaveToggled(bool enabled);
    void onAutoSaveIntervalChanged(int interval);
    
    // Compression slots
    void onCompressionToggled(bool enabled);
    void onCompressionLevelChanged();
    
    // Report slots
    void onReportToggled(bool enabled);
    void onReportFormatChanged();
    void onIncludeChartsToggled(bool include);
    void onIncludeStatisticsToggled(bool include);
    
    // Output variable slots
    void onAddOutputVariable();
    void onUpdateOutputVariable();
    void onDeleteOutputVariable();
    void onOutputVariableTableSelectionChanged();

private:
    void setupConnections();
    void setupOutputVariableTable();
    void updateButtonStates();
    void populateFormatComboBoxes();

    Ui::SolverOutputWidget *ui;
    
    // Current selection tracking
    int m_selectedVariableRow;
};

#endif // SOLVEROUTPUTWIDGET_H 