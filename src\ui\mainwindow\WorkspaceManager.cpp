#include "WorkspaceManager.h"
#include <QSplitter>
#include <QStackedWidget>
#include <QTabWidget>
#include <QVBoxLayout>

WorkspaceManager::WorkspaceManager(QWidget* parent)
    : QObject(parent)
    , m_parent(parent)
    , m_centralWidget(nullptr)
    , m_layoutMode(LayoutMode::Stacked)
    , m_splitter(nullptr)
    , m_tabWidget(nullptr)
    , m_stackedWidget(nullptr)
{
    // 创建中央控件
    m_centralWidget = new QWidget(m_parent);
    m_centralWidget->setObjectName("centralWidget");
    
    // 默认使用堆叠布局
    createStackedLayout();
}

WorkspaceManager::~WorkspaceManager()
{
    // 清理控件映射
    m_widgets.clear();
    m_widgetTitles.clear();
}

QWidget* WorkspaceManager::centralWidget() const
{
    return m_centralWidget;
}

void WorkspaceManager::setLayoutMode(LayoutMode mode)
{
    if (m_layoutMode == mode) {
        return;
    }

    // 保存当前控件名称
    QString currentName = m_currentWidgetName;
    
    // 清理当前布局
    clearLayout();
    
    // 设置新布局模式
    m_layoutMode = mode;
    
    // 创建新布局
    switch (m_layoutMode) {
    case LayoutMode::Splitter:
        createSplitterLayout();
        break;
    case LayoutMode::Tabbed:
        createTabbedLayout();
        break;
    case LayoutMode::Stacked:
        createStackedLayout();
        break;
    }
    
    // 重新添加所有控件
    QMap<QString, QWidget*> widgets = m_widgets;
    QMap<QString, QString> titles = m_widgetTitles;
    
    m_widgets.clear();
    m_widgetTitles.clear();
    
    for (auto it = widgets.begin(); it != widgets.end(); ++it) {
        addWidget(it.value(), it.key(), titles.value(it.key()));
    }
    
    // 恢复当前控件
    if (!currentName.isEmpty()) {
        setCurrentWidget(currentName);
    }
}

WorkspaceManager::LayoutMode WorkspaceManager::layoutMode() const
{
    return m_layoutMode;
}

void WorkspaceManager::addWidget(QWidget* widget, const QString& name, const QString& title)
{
    if (!widget || name.isEmpty() || m_widgets.contains(name)) {
        return;
    }

    m_widgets[name] = widget;
    m_widgetTitles[name] = title.isEmpty() ? name : title;
    
    switch (m_layoutMode) {
    case LayoutMode::Splitter:
        if (m_splitter) {
            m_splitter->addWidget(widget);
        }
        break;
    case LayoutMode::Tabbed:
        if (m_tabWidget) {
            m_tabWidget->addTab(widget, m_widgetTitles[name]);
        }
        break;
    case LayoutMode::Stacked:
        if (m_stackedWidget) {
            m_stackedWidget->addWidget(widget);
        }
        break;
    }
    
    emit widgetAdded(name);
    
    // 如果是第一个控件，设置为当前控件
    if (m_widgets.size() == 1) {
        setCurrentWidget(name);
    }
}

bool WorkspaceManager::removeWidget(const QString& name)
{
    if (!m_widgets.contains(name)) {
        return false;
    }

    QWidget* widget = m_widgets[name];
    
    switch (m_layoutMode) {
    case LayoutMode::Splitter:
        // 从分割器中移除控件
        if (m_splitter) {
            widget->setParent(nullptr);
        }
        break;
    case LayoutMode::Tabbed:
        // 从标签页中移除控件
        if (m_tabWidget) {
            int index = m_tabWidget->indexOf(widget);
            if (index >= 0) {
                m_tabWidget->removeTab(index);
            }
        }
        break;
    case LayoutMode::Stacked:
        // 从堆叠控件中移除控件
        if (m_stackedWidget) {
            int index = m_stackedWidget->indexOf(widget);
            if (index >= 0) {
                m_stackedWidget->removeWidget(widget);
            }
        }
        break;
    }
    
    m_widgets.remove(name);
    m_widgetTitles.remove(name);
    
    // 如果移除的是当前控件，切换到其他控件
    if (m_currentWidgetName == name) {
        if (!m_widgets.isEmpty()) {
            setCurrentWidget(m_widgets.keys().first());
        } else {
            m_currentWidgetName.clear();
        }
    }
    
    emit widgetRemoved(name);
    
    return true;
}

bool WorkspaceManager::setCurrentWidget(const QString& name)
{
    if (!m_widgets.contains(name)) {
        return false;
    }

    QWidget* widget = m_widgets[name];
    
    switch (m_layoutMode) {
    case LayoutMode::Splitter:
        // 分割器模式下，所有控件都是可见的，只需要激活当前控件
        widget->setFocus();
        break;
    case LayoutMode::Tabbed:
        // 标签页模式下，切换到对应标签
        if (m_tabWidget) {
            int index = m_tabWidget->indexOf(widget);
            if (index >= 0) {
                m_tabWidget->setCurrentIndex(index);
            }
        }
        break;
    case LayoutMode::Stacked:
        // 堆叠模式下，切换到对应控件
        if (m_stackedWidget) {
            m_stackedWidget->setCurrentWidget(widget);
        }
        break;
    }
    
    QString oldName = m_currentWidgetName;
    m_currentWidgetName = name;
    
    if (oldName != name) {
        emit currentWidgetChanged(name);
    }
    
    return true;
}

QString WorkspaceManager::currentWidgetName() const
{
    return m_currentWidgetName;
}

QWidget* WorkspaceManager::getWidget(const QString& name) const
{
    return m_widgets.value(name, nullptr);
}

QStringList WorkspaceManager::widgetNames() const
{
    return m_widgets.keys();
}

void WorkspaceManager::setWidgetVisible(const QString& name, bool visible)
{
    QWidget* widget = getWidget(name);
    if (widget) {
        widget->setVisible(visible);
    }
}

bool WorkspaceManager::isWidgetVisible(const QString& name) const
{
    QWidget* widget = getWidget(name);
    return widget ? widget->isVisible() : false;
}

void WorkspaceManager::setSplitterSizes(const QList<int>& sizes)
{
    if (m_splitter) {
        m_splitter->setSizes(sizes);
    }
}

QList<int> WorkspaceManager::splitterSizes() const
{
    return m_splitter ? m_splitter->sizes() : QList<int>();
}

void WorkspaceManager::createSplitterLayout()
{
    // 创建分割器布局
    m_splitter = new QSplitter(m_centralWidget);
    m_splitter->setOrientation(Qt::Horizontal);
    
    QVBoxLayout* layout = new QVBoxLayout(m_centralWidget);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(m_splitter);
    
    m_centralWidget->setLayout(layout);
}

void WorkspaceManager::createTabbedLayout()
{
    // 创建标签页布局
    m_tabWidget = new QTabWidget(m_centralWidget);
    m_tabWidget->setTabPosition(QTabWidget::North);
    m_tabWidget->setTabsClosable(false);
    m_tabWidget->setMovable(true);
    
    QVBoxLayout* layout = new QVBoxLayout(m_centralWidget);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(m_tabWidget);
    
    m_centralWidget->setLayout(layout);
    
    // 连接标签页切换信号
    connect(m_tabWidget, &QTabWidget::currentChanged, this, [this](int index) {
        if (index >= 0) {
            QWidget* widget = m_tabWidget->widget(index);
            QString name = m_widgets.key(widget);
            if (!name.isEmpty()) {
                m_currentWidgetName = name;
                emit currentWidgetChanged(name);
            }
        }
    });
}

void WorkspaceManager::createStackedLayout()
{
    // 创建堆叠布局
    m_stackedWidget = new QStackedWidget(m_centralWidget);
    
    QVBoxLayout* layout = new QVBoxLayout(m_centralWidget);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addWidget(m_stackedWidget);
    
    m_centralWidget->setLayout(layout);
    
    // 连接堆叠控件切换信号
    connect(m_stackedWidget, &QStackedWidget::currentChanged, this, [this](int index) {
        if (index >= 0) {
            QWidget* widget = m_stackedWidget->widget(index);
            QString name = m_widgets.key(widget);
            if (!name.isEmpty()) {
                m_currentWidgetName = name;
                emit currentWidgetChanged(name);
            }
        }
    });
}

void WorkspaceManager::clearLayout()
{
    // 断开所有连接
    if (m_tabWidget) {
        disconnect(m_tabWidget, nullptr, this, nullptr);
    }
    
    if (m_stackedWidget) {
        disconnect(m_stackedWidget, nullptr, this, nullptr);
    }
    
    // 清理布局
    if (m_centralWidget->layout()) {
        delete m_centralWidget->layout();
    }
    
    // 清理布局控件
    m_splitter = nullptr;
    m_tabWidget = nullptr;
    m_stackedWidget = nullptr;
}
