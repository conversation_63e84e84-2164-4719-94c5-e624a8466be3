#ifndef CUSTOMTREEWIDGET_H
#define CUSTOMTREEWIDGET_H

#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QMenu>
#include <QAction>
#include <QContextMenuEvent>
#include <QWidget>
#include "../utils/Common.h"
#include "../data/InputData.h"
#include "../data/IFileExample.h"

class CustomTreeWidget : public QTreeWidget
{
    Q_OBJECT

public:
    explicit CustomTreeWidget(QWidget *parent = nullptr);
    ~CustomTreeWidget();

    void initTree();
    void retranslate();
    
    // 文件操作
    void setInputFilePath(const QString& filePath);
    
    // IFileExample功能演示
    void runIFileExampleDemo();
    
    // 节点操作
    QTreeWidgetItem* addNode(const QString& name, NodeType type, QTreeWidgetItem* parent = nullptr);
    void removeNode(QTreeWidgetItem* item);
    void renameNode(QTreeWidgetItem* item, const QString& newName);
    
    // 节点查找
    QTreeWidgetItem* findNode(const QString& name, NodeType type = NodeType::Unknown);
    QList<QTreeWidgetItem*> getNodesByType(NodeType type);
    
    // 节点信息
    NodeType getNodeType(QTreeWidgetItem* item) const;
    void setNodeType(QTreeWidgetItem* item, NodeType type);
    QIcon getNodeIcon(NodeType type) const;
    
    // Project management
    void createNewProject(const QString& projectName, const QString& projectPath);
    void clearProject();
    void setupEmptyProject();
    
    // Node pointer management for loading
    void setSolverItem(QTreeWidgetItem* item) { m_solverItem = item; }
    void setOptimizeItem(QTreeWidgetItem* item) { m_optimizeItem = item; }
    void setSensitivityItem(QTreeWidgetItem* item) { m_sensitivityItem = item; }
    void setUQItem(QTreeWidgetItem* item) { m_uqItem = item; }
    void setInputItem(QTreeWidgetItem* item) { m_inputItem = item; }
    void setOutputItem(QTreeWidgetItem* item) { m_outputItem = item; }

signals:
    void nodeSelected(QTreeWidgetItem* item, NodeType type);
    void nodeDoubleClicked(QTreeWidgetItem* item, NodeType type);
    void nodeContextMenuRequested(QTreeWidgetItem* item, const QPoint& pos);
    void moduleWidgetRequested(QWidget* widget, const QString& title);

protected:
    void contextMenuEvent(QContextMenuEvent *event) override;

private slots:
    void onItemDoubleClicked(QTreeWidgetItem *item, int column);
    void onItemSelectionChanged();
    void onCustomContextMenuRequested(const QPoint &pos);
    
    // Context menu actions
    void onAddOptimization();
    void onAddSensitivityAnalysis();
    void onAddUncertaintyQuantification();
    void onDeleteNode();
    void onRenameNode();
    void onMoveNodeUp();
    void onMoveNodeDown();

private:
    void createActions();
    void createMenus();
    void setupConnections();
    void setupTreeStructure();
    void loadSolverModules();
    
    // IFileExample-based parsing methods
    bool parseIFileWithExample(const QString& filePath);
    void addComponentsToSolverNode();
    void displayParsingResults();
    void generateFilePreview();
    void validateFileFormat();
    void demonstrateIFileFeatures();
    
    QString nodeTypeToString(NodeType type) const;
    NodeType stringToNodeType(const QString& str) const;
    QString generateUniqueNodeName(NodeType type) const;
    bool canMoveNodeUp(QTreeWidgetItem* item) const;
    bool canMoveNodeDown(QTreeWidgetItem* item) const;

    InputData m_inputData;

    // 节点指针
    QMenu* m_contextMenu;
    QTreeWidgetItem* m_optimizeItem;
    QTreeWidgetItem* m_sensitivityItem;
    QTreeWidgetItem* m_uqItem;
    QTreeWidgetItem* m_inputItem;
    QTreeWidgetItem* m_outputItem;
    QTreeWidgetItem* m_solverItem;
    QTreeWidgetItem* mCurrentItems;
    // 上下文菜单

    QAction* m_addOptimizationAction;
    QAction* m_addSensitivityAction;
    QAction* m_addUQAction;
    QAction* m_deleteNodeAction;
    QAction* m_renameNodeAction;
    QAction* m_moveUpAction;
    QAction* m_moveDownAction;


    
    // 输入文件路径
    QString m_inputFilePath;
    

    
    // IFileExample 实例
    IFileExample* m_iFileExample;
};

#endif // CUSTOMTREEWIDGET_H 
