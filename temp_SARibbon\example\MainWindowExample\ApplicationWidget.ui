<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ApplicationWidget</class>
 <widget class="QWidget" name="ApplicationWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>567</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>1</number>
   </property>
   <property name="leftMargin">
    <number>1</number>
   </property>
   <property name="topMargin">
    <number>1</number>
   </property>
   <property name="rightMargin">
    <number>1</number>
   </property>
   <property name="bottomMargin">
    <number>1</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QPushButton" name="pushButtonCancel2">
       <property name="text">
        <string>Cancel</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QListView" name="listView">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QLabel" name="label">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>Press the Esc key to exit the window.</string>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <widget class="QPushButton" name="pushButtonCancel">
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="saribbonresouce.qrc">
         <normaloff>:/icon/icon/delete.svg</normaloff>:/icon/icon/delete.svg</iconset>
       </property>
       <property name="flat">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="saribbonresouce.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>pushButtonCancel</sender>
   <signal>clicked()</signal>
   <receiver>ApplicationWidget</receiver>
   <slot>hide()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>552</x>
     <y>16</y>
    </hint>
    <hint type="destinationlabel">
     <x>729</x>
     <y>53</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>pushButtonCancel2</sender>
   <signal>clicked()</signal>
   <receiver>ApplicationWidget</receiver>
   <slot>hide()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>177</x>
     <y>10</y>
    </hint>
    <hint type="destinationlabel">
     <x>573</x>
     <y>67</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
