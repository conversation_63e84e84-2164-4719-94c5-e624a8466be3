#include "UQWidget.h"
#include "ui_UQWidget.h"

UQWidget::UQWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::UQWidget)
{
    ui->setupUi(this);
    setupConnections();
    updateMethodDescription();
    updateParameterVisibility();
}

UQWidget::~UQWidget()
{
    delete ui;
}

void UQWidget::setupConnections()
{
    // Method selection
    connect(ui->methodCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &UQWidget::onMethodChanged);
    
    // Parameter changes
    connect(ui->samplesSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &UQWidget::onParameterChanged);
    connect(ui->samplingTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &UQWidget::onParameterChanged);
    connect(ui->seedSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &UQWidget::onParameterChanged);
    connect(ui->orderSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &UQWidget::onParameterChanged);
    connect(ui->expansionTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &UQWidget::onExpansionTypeChanged);
    connect(ui->toleranceSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &UQWidget::onParameterChanged);
    
    // Checkboxes
    connect(ui->adaptiveRefinementCheck, &QCheckBox::toggled,
            this, &UQWidget::onAdaptiveRefinementToggled);
    connect(ui->momentsCheck, &QCheckBox::toggled,
            this, &UQWidget::onParameterChanged);
    connect(ui->probabilityLevelsCheck, &QCheckBox::toggled,
            this, &UQWidget::onParameterChanged);
    connect(ui->reliabilityLevelsCheck, &QCheckBox::toggled,
            this, &UQWidget::onParameterChanged);
    connect(ui->responseLevelsCheck, &QCheckBox::toggled,
            this, &UQWidget::onParameterChanged);
    connect(ui->sobolIndicesCheck, &QCheckBox::toggled,
            this, &UQWidget::onParameterChanged);
}

// Getter methods
QString UQWidget::getMethod() const
{
    return ui->methodCombo->currentText();
}

int UQWidget::getSamples() const
{
    return ui->samplesSpin->value();
}

QString UQWidget::getSamplingType() const
{
    return ui->samplingTypeCombo->currentText();
}

int UQWidget::getPolynomialOrder() const
{
    return ui->orderSpin->value();
}

QString UQWidget::getExpansionType() const
{
    return ui->expansionTypeCombo->currentText();
}

double UQWidget::getConvergenceTolerance() const
{
    return ui->toleranceSpin->value();
}

int UQWidget::getSeed() const
{
    return ui->seedSpin->value();
}

bool UQWidget::isAdaptiveRefinement() const
{
    return ui->adaptiveRefinementCheck->isChecked();
}

// Setter methods
void UQWidget::setMethod(const QString &method)
{
    int index = ui->methodCombo->findText(method);
    if (index >= 0) {
        ui->methodCombo->setCurrentIndex(index);
    }
}

void UQWidget::setSamples(int samples)
{
    ui->samplesSpin->setValue(samples);
}

void UQWidget::setSamplingType(const QString &type)
{
    int index = ui->samplingTypeCombo->findText(type);
    if (index >= 0) {
        ui->samplingTypeCombo->setCurrentIndex(index);
    }
}

void UQWidget::setPolynomialOrder(int order)
{
    ui->orderSpin->setValue(order);
}

void UQWidget::setExpansionType(const QString &type)
{
    int index = ui->expansionTypeCombo->findText(type);
    if (index >= 0) {
        ui->expansionTypeCombo->setCurrentIndex(index);
    }
}

void UQWidget::setConvergenceTolerance(double tolerance)
{
    ui->toleranceSpin->setValue(tolerance);
}

void UQWidget::setSeed(int seed)
{
    ui->seedSpin->setValue(seed);
}

void UQWidget::setAdaptiveRefinement(bool enabled)
{
    ui->adaptiveRefinementCheck->setChecked(enabled);
}

// Slots
void UQWidget::onMethodChanged()
{
    updateMethodDescription();
    updateParameterVisibility();
    emit methodChanged(getMethod());
    emit parametersChanged();
}

void UQWidget::onParameterChanged()
{
    emit parametersChanged();
}

void UQWidget::onExpansionTypeChanged()
{
    emit parametersChanged();
}

void UQWidget::onAdaptiveRefinementToggled(bool enabled)
{
    // Enable/disable tolerance setting based on adaptive refinement
    ui->toleranceLabel->setEnabled(enabled);
    ui->toleranceSpin->setEnabled(enabled);
    emit parametersChanged();
}

void UQWidget::updateMethodDescription()
{
    QString method = getMethod();
    QString description;
    
    if (method == "sampling") {
        description = "Sampling: Monte Carlo sampling for uncertainty propagation";
    } else if (method == "polynomial_chaos") {
        description = "Polynomial Chaos: Spectral methods using orthogonal polynomials";
    } else if (method == "stoch_collocation") {
        description = "Stochastic Collocation: Interpolation-based uncertainty quantification";
    } else if (method == "local_reliability") {
        description = "Local Reliability: FORM/SORM methods for reliability analysis";
    } else if (method == "global_reliability") {
        description = "Global Reliability: Efficient Global Optimization for reliability";
    } else if (method == "importance_sampling") {
        description = "Importance Sampling: Variance reduction technique for rare events";
    } else {
        description = "Select a method for uncertainty quantification";
    }
    
    ui->methodDescription->setText(description);
}

void UQWidget::updateParameterVisibility()
{
    QString method = getMethod();
    
    // Show/hide parameters based on selected method
    bool showSampling = (method == "sampling" || method == "importance_sampling");
    bool showExpansion = (method == "polynomial_chaos" || method == "stoch_collocation");
    bool showConvergence = showExpansion;
    
    ui->samplingGroup->setVisible(showSampling || showExpansion);
    ui->expansionGroup->setVisible(showExpansion);
    ui->convergenceGroup->setVisible(showConvergence);
    
    // Update adaptive refinement visibility
    onAdaptiveRefinementToggled(ui->adaptiveRefinementCheck->isChecked());
} 