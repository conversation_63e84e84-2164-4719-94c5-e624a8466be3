CMAKE_MINIMUM_REQUIRED(VERSION 3.15.0 FATAL_ERROR)
SET(PROJECTNAME SARibbonBar)
PROJECT(${PROJECTNAME})
ADD_DEFINITIONS("-DSA_RIBBON_BAR_MAKE_LIB")
SET(CMAKE_CXX_STANDARD 11)
SET(CMAKE_INCLUDE_CURRENT_DIR ON)

IF(NOT DEFINED Qt5_DIR)
	SET(Qt5_DIR "Qt5_DIR-NOTFOUND" CACHE PATH "Qt5_DIR")
ENDIF()
IF(FASTCAE_VTK_INCLUDE STREQUAL "FASTCAE_VTK_INCLUDE-NOTFOUND")		
	MESSAGE(SEND_ERROR "FASTCAE_VTK_INCLUDE is null, please specified VTK include directory!~")
ENDIF()

IF(CMAKE_SYSTEM_NAME MATCHES "Linux")
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_DEBUG ${CMAKE_CURRENT_SOURCE_DIR}/../../output/bin)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_RELEASE ${CMAKE_CURRENT_SOURCE_DIR}/../../output/bin)		
ELSEIF(CMAKE_SYSTEM_NAME MATCHES "Windows")
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_DEBUG ${CMAKE_CURRENT_SOURCE_DIR}/../output/bin_d)    
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_RELEASE ${CMAKE_CURRENT_SOURCE_DIR}/../../output/bin) 
	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_CURRENT_SOURCE_DIR}/../output/bin_d)   
	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_CURRENT_SOURCE_DIR}/../../output/bin)	
ENDIF()

FILE(GLOB SRC_QRC_FILES "*.qrc")
FILE(GLOB SRC_H_FILES "*.h")
FILE(GLOB SRC_CPP_FILES "*.cpp")

FIND_PACKAGE(Qt5 COMPONENTS Widgets REQUIRED)
QT5_ADD_RESOURCES(SRC_Generated_QRC_FILES ${SRC_QRC_FILES})
SET(CMAKE_AUTOMOC ON)	
SOURCE_GROUP("rc" FILES ${SRC_Generated_QRC_FILES} ${SRC_QRC_FILES})
SOURCE_GROUP("Generated Files" FILES "${PROJECT_BINARY_DIR}/${PROJECTNAME}_autogen/mocs_compilation.cpp")
ADD_LIBRARY(${PROJECTNAME} SHARED ${SRC_Generated_QRC_FILES}
								  ${SRC_H_FILES}
								  ${SRC_CPP_FILES})	
TARGET_LINK_LIBRARIES(${PROJECTNAME} ${Qt5Widgets_LIBRARIES})