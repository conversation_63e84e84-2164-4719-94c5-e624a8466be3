#include "MainWindow.h"
#include "ui_MainWindow.h"
#include "../utils/Logger.h"
#include "../utils/Common.h"
#include "../core/ApplicationCore.h"
#include "../core/ThemeManager.h"
#include "../SARibbonBar/SARibbonApplicationButton.h"
#include "../SARibbonBar/SARibbonQuickAccessBar.h"
#include "../SARibbonBar/SARibbonMenu.h"
#include <QMessageBox>
#include <QFileDialog>
#include <QCloseEvent>
#include <QSettings>
#include <QTabWidget>
#include <QStandardPaths>
#include <QFile>
#include <QTextStream>
#include <QMenu>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QAction>
#include <QIcon>
#include <QLabel>
#include <QTimer>

// Maximum number of recent files to show in menu
const int MAX_RECENT_FILES = 5;

MainWindow::MainWindow(QWidget *parent)
    : SARibbonMainWindow(parent)
    , ui(new Ui::MainWindow)
    , fileModified(false)
    , ribbonBar(nullptr)
    , homeCategory(nullptr)
    , viewCategory(nullptr)
    , toolsCategory(nullptr)
    , optimizeCategory(nullptr)
    , sensitivityCategory(nullptr)
    , optimizeWidget(new RibbonOptimizeWidget(this))
    , sensitivityWidget(new SensitivityWidget(this))
    , tabWidget(nullptr)
    , fileMenu(nullptr)
    , recentFilesMenu(nullptr)
    , newAction(nullptr)
    , openAction(nullptr)
    , saveAction(nullptr)
    , saveAsAction(nullptr)
    , exitAction(nullptr)
    , aboutAction(nullptr)
    , aboutQtAction(nullptr)
{
    LOG_INFO("Creating MainWindow", "MainWindow");
    
    // Ensure ApplicationCore is initialized first
    ApplicationCore* appCore = ApplicationCore::instance();
    if (!appCore) {
        LOG_ERROR("ApplicationCore is not initialized", "MainWindow");
    }
    
    ui->setupUi(this);
    
    // Set window properties
    setMinimumSize(800, 600);
    
    // Set up UI components
    createActions();
    
    // Use QTimer to delay the ribbon setup to the next event loop iteration
    QTimer::singleShot(0, this, [this]() {
        setupRibbon();
        createStatusBar();
        setupCentralWidget();
        setupConnections();
        
        // Set up recent files menu
        updateRecentFilesMenu();
        
        // Initialize with no file open
        setCurrentFile("");
        
        LOG_INFO("MainWindow created", "MainWindow");
    });
}

MainWindow::~MainWindow()
{
    LOG_INFO("Destroying MainWindow", "MainWindow");
    delete ui;
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (maybeSave()) {
        event->accept();
    } else {
        event->ignore();
    }
}

void MainWindow::on_actionNew_triggered()
{
    LOG_INFO("New action triggered", "MainWindow");
    
    if (maybeSave()) {
        // Clear current file and reset UI
        setCurrentFile("");
        // TODO: Reset application state for new file
    }
}

void MainWindow::on_actionOpen_triggered()
{
    LOG_INFO("Open action triggered", "MainWindow");
    
    if (maybeSave()) {
        ApplicationCore* appCore = ApplicationCore::instance();
        if (!appCore) {
            LOG_ERROR("ApplicationCore is not initialized", "MainWindow");
            return;
        }
        
        ConfigManager *configManager = appCore->getConfigManager();
        QString lastDir = configManager ? configManager->getLastDirectory() : QDir::homePath();
        
        QString filePath = QFileDialog::getOpenFileName(
            this,
            tr("Open File"),
            lastDir,
            AppConstants::FILE_FILTER
        );
        
        if (!filePath.isEmpty()) {
            loadFile(filePath);
        }
    }
}

void MainWindow::on_actionSave_triggered()
{
    LOG_INFO("Save action triggered", "MainWindow");
    
    if (currentFilePath.isEmpty()) {
        on_actionSaveAs_triggered();
    } else {
        saveFile(currentFilePath);
    }
}

void MainWindow::on_actionSaveAs_triggered()
{
    LOG_INFO("Save As action triggered", "MainWindow");
    
    ApplicationCore* appCore = ApplicationCore::instance();
    if (!appCore) {
        LOG_ERROR("ApplicationCore is not initialized", "MainWindow");
        return;
    }
    
    ConfigManager *configManager = appCore->getConfigManager();
    QString lastDir = configManager ? configManager->getLastDirectory() : QDir::homePath();
    
    if (currentFilePath.isEmpty()) {
        lastDir += "/" + tr("untitled") + AppConstants::FILE_EXTENSION;
    } else {
        lastDir = currentFilePath;
    }
    
    QString filePath = QFileDialog::getSaveFileName(
        this,
        tr("Save File"),
        lastDir,
        AppConstants::FILE_FILTER
    );
    
    if (!filePath.isEmpty()) {
        saveFile(filePath);
    }
}

void MainWindow::on_actionExit_triggered()
{
    LOG_INFO("Exit action triggered", "MainWindow");
    close();
}

void MainWindow::on_actionAbout_triggered()
{
    LOG_INFO("About action triggered", "MainWindow");
    
    QMessageBox::about(this, tr("About Optimize Application"),
        tr("<h2>Optimize Application %1</h2>"
           "<p>A comprehensive application for optimization, sensitivity analysis, "
           "and uncertainty quantification.</p>"
           "<p>Copyright &copy; 2024 Optimize Solutions. All rights reserved.</p>")
        .arg(AppConstants::APP_VERSION));
}

void MainWindow::openRecentFile()
{
    if (maybeSave()) {
        QAction *action = qobject_cast<QAction *>(sender());
        if (action) {
            QString filePath = action->data().toString();
            if (!filePath.isEmpty()) {
                loadFile(filePath);
            }
        }
    }
}

void MainWindow::updateRecentFilesMenu()
{
    LOG_INFO("Updating recent files menu", "MainWindow");
    
    // If menu is not created yet, skip this
    if (!recentFilesMenu) {
        LOG_WARNING("Recent files menu not initialized yet", "MainWindow");
        return;
    }
    
    // Clear existing recent file actions
    recentFilesMenu->clear();
    
    // Get list of recent files from config
    ApplicationCore* appCore = ApplicationCore::instance();
    if (!appCore) {
        LOG_ERROR("ApplicationCore is not initialized", "MainWindow");
        return;
    }
    
    ConfigManager *configManager = appCore->getConfigManager();
    QStringList recentFiles = configManager ? configManager->getRecentFiles() : QStringList();
    
    // Add recent files to menu (up to MAX_RECENT_FILES)
    int count = qMin(recentFiles.size(), MAX_RECENT_FILES);
    for (int i = 0; i < count; ++i) {
        QString filePath = recentFiles.at(i);
        QString fileName = QFileInfo(filePath).fileName();
        
        QAction *action = recentFilesMenu->addAction(fileName);
        action->setData(filePath);
        action->setStatusTip(filePath);
        connect(action, &QAction::triggered, this, &MainWindow::openRecentFile);
    }
    
    // Add separator and clear action if there are recent files
    if (count > 0) {
        recentFilesMenu->addSeparator();
        QAction *clearAction = recentFilesMenu->addAction(tr("Clear Recent Files"));
        connect(clearAction, &QAction::triggered, [this]() {
            ApplicationCore* appCore = ApplicationCore::instance();
            if (!appCore) {
                LOG_ERROR("ApplicationCore is not initialized", "MainWindow");
                return;
            }
            
            ConfigManager *configManager = appCore->getConfigManager();
            if (configManager) {
                configManager->setSetting(AppConstants::Settings::RECENT_FILES, QStringList());
                updateRecentFilesMenu();
            }
        });
    }
    
    // Enable/disable the recent files menu
    recentFilesMenu->setEnabled(count > 0);
}

void MainWindow::onTabChanged(int index)
{
    LOG_INFO(QString("Tab changed to index %1").arg(index), "MainWindow");
    
    // Check if ribbon bar exists
    if (!ribbonBar) {
        LOG_ERROR("Ribbon bar is null", "MainWindow");
        return;
    }
    
    // Show the corresponding ribbon category based on the selected tab
    switch (index) {
    case 0: // Model tab
        ribbonBar->setCurrentIndex(0); // Home category
        break;
    case 1: // Optimization tab
        ribbonBar->setCurrentIndex(ribbonBar->categoryIndex(optimizeCategory));
        break;
    case 2: // Sensitivity tab
        ribbonBar->setCurrentIndex(ribbonBar->categoryIndex(sensitivityCategory));
        break;
    case 3: // Uncertainty tab
        ribbonBar->setCurrentIndex(2); // Tools category (placeholder)
        break;
    case 4: // Results tab
        ribbonBar->setCurrentIndex(1); // View category
        break;
    default:
        ribbonBar->setCurrentIndex(0); // Default to home
        break;
    }
}

void MainWindow::createActions()
{
    LOG_INFO("Creating actions", "MainWindow");
    
    // File actions
    newAction = new QAction(QIcon(":/icons/new.png"), tr("&New"), this);
    newAction->setShortcuts(QKeySequence::New);
    newAction->setStatusTip(tr("Create a new file"));
    connect(newAction, &QAction::triggered, this, &MainWindow::on_actionNew_triggered);
    
    openAction = new QAction(QIcon(":/icons/open.png"), tr("&Open..."), this);
    openAction->setShortcuts(QKeySequence::Open);
    openAction->setStatusTip(tr("Open an existing file"));
    connect(openAction, &QAction::triggered, this, &MainWindow::on_actionOpen_triggered);
    
    saveAction = new QAction(QIcon(":/icons/save.png"), tr("&Save"), this);
    saveAction->setShortcuts(QKeySequence::Save);
    saveAction->setStatusTip(tr("Save the document to disk"));
    connect(saveAction, &QAction::triggered, this, &MainWindow::on_actionSave_triggered);
    
    saveAsAction = new QAction(tr("Save &As..."), this);
    saveAsAction->setShortcuts(QKeySequence::SaveAs);
    saveAsAction->setStatusTip(tr("Save the document under a new name"));
    connect(saveAsAction, &QAction::triggered, this, &MainWindow::on_actionSaveAs_triggered);
    
    exitAction = new QAction(QIcon(":/icons/exit.png"), tr("E&xit"), this);
    exitAction->setShortcuts(QKeySequence::Quit);
    exitAction->setStatusTip(tr("Exit the application"));
    connect(exitAction, &QAction::triggered, this, &MainWindow::on_actionExit_triggered);
    
    // Help actions
    aboutAction = new QAction(QIcon(":/icons/about.png"), tr("&About"), this);
    aboutAction->setStatusTip(tr("Show the application's About box"));
    connect(aboutAction, &QAction::triggered, this, &MainWindow::on_actionAbout_triggered);
    
    aboutQtAction = new QAction(tr("About &Qt"), this);
    aboutQtAction->setStatusTip(tr("Show the Qt library's About box"));
    connect(aboutQtAction, &QAction::triggered, qApp, &QApplication::aboutQt);
}

void MainWindow::setupRibbon()
{
    LOG_INFO("Setting up ribbon interface", "MainWindow");
    
    // Get the ribbon bar
    ribbonBar = static_cast<SARibbonBar*>(SARibbonMainWindow::ribbonBar());
    if (!ribbonBar) {
        LOG_ERROR("Failed to get ribbon bar", "MainWindow");
        return;
    }
    
    // Create application button
    SARibbonApplicationButton* appBtn = new SARibbonApplicationButton(this);
    appBtn->setText(tr("File"));
    ribbonBar->setApplicationButton(appBtn);
    
    // Create application button menu
    QMenu* applicationMenu = new QMenu(this);
    applicationMenu->addAction(newAction);
    applicationMenu->addAction(openAction);
    applicationMenu->addSeparator();
    applicationMenu->addAction(saveAction);
    applicationMenu->addAction(saveAsAction);
    applicationMenu->addSeparator();
    
    // Create recent files menu
    recentFilesMenu = new QMenu(tr("Recent Files"), this);
    applicationMenu->addMenu(recentFilesMenu);
    applicationMenu->addSeparator();
    applicationMenu->addAction(exitAction);
    
    // Set the menu for application button
    appBtn->setMenu(applicationMenu);
    
    // Set up quick access bar
    SARibbonQuickAccessBar* quickAccessBar = ribbonBar->quickAccessBar();
    quickAccessBar->addAction(newAction);
    quickAccessBar->addAction(openAction);
    quickAccessBar->addAction(saveAction);
    
    // Create ribbon categories
    homeCategory = ribbonBar->addCategoryPage(tr("Home"));
    viewCategory = ribbonBar->addCategoryPage(tr("View"));
    toolsCategory = ribbonBar->addCategoryPage(tr("Tools"));
    
    // Add optimization category using RibbonOptimizeWidget
    optimizeCategory = optimizeWidget->createRibbonCategory(ribbonBar);
    
    // Add sensitivity category using SensitivityWidget
    sensitivityCategory = sensitivityWidget->createRibbonCategory(ribbonBar);
    
    // Set up categories
    setupHomeCategory();
    setupViewCategory();
    setupToolsCategory();
}

void MainWindow::setupHomeCategory()
{
    LOG_INFO("Setting up home category", "MainWindow");
    
    if (!homeCategory) {
        LOG_ERROR("Home category is null", "MainWindow");
        return;
    }
    
    // File panel
    SARibbonPannel* filePanel = homeCategory->addPannel(tr("File"));
    if (!filePanel) {
        LOG_ERROR("Failed to create file panel", "MainWindow");
        return;
    }
    
    filePanel->addLargeAction(newAction);
    filePanel->addLargeAction(openAction);
    filePanel->addLargeAction(saveAction);
    filePanel->addSmallAction(saveAsAction);
    
    // Add other panels as needed for the home category
    SARibbonPannel* clipboardPanel = homeCategory->addPannel(tr("Clipboard"));
    // Add clipboard actions...
    
    SARibbonPannel* editPanel = homeCategory->addPannel(tr("Edit"));
    // Add edit actions...
}

void MainWindow::setupViewCategory()
{
    LOG_INFO("Setting up view category", "MainWindow");
    
    if (!viewCategory) {
        LOG_ERROR("View category is null", "MainWindow");
        return;
    }
    
    // View panel
    SARibbonPannel* viewPanel = viewCategory->addPannel(tr("View"));
    if (!viewPanel) {
        LOG_ERROR("Failed to create view panel", "MainWindow");
        return;
    }
    
    // Add view actions...
    
    // Appearance panel
    SARibbonPannel* appearancePanel = viewCategory->addPannel(tr("Appearance"));
    // Add appearance actions...
}

void MainWindow::setupToolsCategory()
{
    LOG_INFO("Setting up tools category", "MainWindow");
    
    if (!toolsCategory) {
        LOG_ERROR("Tools category is null", "MainWindow");
        return;
    }
    
    // Tools panel
    SARibbonPannel* toolsPanel = toolsCategory->addPannel(tr("Tools"));
    if (!toolsPanel) {
        LOG_ERROR("Failed to create tools panel", "MainWindow");
        return;
    }
    
    // Add tools actions...
    
    // Help panel
    SARibbonPannel* helpPanel = toolsCategory->addPannel(tr("Help"));
    if (!helpPanel) {
        LOG_ERROR("Failed to create help panel", "MainWindow");
        return;
    }
    
    helpPanel->addLargeAction(aboutAction);
    helpPanel->addSmallAction(aboutQtAction);
}

void MainWindow::createStatusBar()
{
    LOG_INFO("Creating status bar", "MainWindow");
    
    statusBar()->showMessage(tr("Ready"));
}

void MainWindow::setupCentralWidget()
{
    LOG_INFO("Setting up central widget", "MainWindow");
    
    // Create a tab widget for the central widget
    tabWidget = new QTabWidget(this);
    tabWidget->setTabsClosable(false);
    tabWidget->setMovable(true);
    
    setCentralWidget(tabWidget);
    
    // Add tabs for different functionality
    tabWidget->addTab(new QWidget(), tr("Model"));
    tabWidget->addTab(new QWidget(), tr("Optimization"));
    tabWidget->addTab(sensitivityWidget, tr("Sensitivity"));
    tabWidget->addTab(new QWidget(), tr("Uncertainty"));
    tabWidget->addTab(new QWidget(), tr("Results"));
    
    // Connect tab changed signal
    connect(tabWidget, &QTabWidget::currentChanged, this, &MainWindow::onTabChanged);
}

void MainWindow::setupConnections()
{
    LOG_INFO("Setting up connections", "MainWindow");
    
    // Connect theme manager signals
    ApplicationCore* appCore = ApplicationCore::instance();
    if (!appCore) {
        LOG_ERROR("ApplicationCore is not initialized", "MainWindow");
        return;
    }
    
    ThemeManager* themeManager = ThemeManager::instance();
    if (!themeManager) {
        LOG_ERROR("ThemeManager is not initialized", "MainWindow");
        return;
    }
    
    // Connect theme change signal
    connect(themeManager, &ThemeManager::themeChanged, this, [this](ThemeType type) {
        // Update UI based on theme
        // TODO: Update ribbon bar theme
        Q_UNUSED(type);
    });
}

bool MainWindow::saveFile(const QString &filePath)
{
    LOG_INFO(QString("Saving file: %1").arg(filePath), "MainWindow");
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("Save Error"),
                             tr("Cannot save file %1:\n%2.")
                             .arg(QDir::toNativeSeparators(filePath),
                                  file.errorString()));
        return false;
    }
    
    QTextStream out(&file);
    
    // TODO: Write file content
    
    file.close();
    
    // Update file path and window title
    setCurrentFile(filePath);
    
    // Update recent files list
    ApplicationCore* appCore = ApplicationCore::instance();
    if (!appCore) {
        LOG_ERROR("ApplicationCore is not initialized", "MainWindow");
        return true;
    }
    
    ConfigManager *configManager = appCore->getConfigManager();
    if (configManager) {
        configManager->addRecentFile(filePath);
        updateRecentFilesMenu();
    }
    
    LOG_INFO(QString("File saved successfully: %1").arg(filePath), "MainWindow");
    
    return true;
}

bool MainWindow::loadFile(const QString &filePath)
{
    LOG_INFO(QString("Loading file: %1").arg(filePath), "MainWindow");
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, tr("Open Error"),
                             tr("Cannot open file %1:\n%2.")
                             .arg(QDir::toNativeSeparators(filePath),
                                  file.errorString()));
        return false;
    }
    
    QTextStream in(&file);
    
    // TODO: Read file content
    
    file.close();
    
    // Update file path and window title
    setCurrentFile(filePath);
    
    // Update recent files list
    ApplicationCore* appCore = ApplicationCore::instance();
    if (!appCore) {
        LOG_ERROR("ApplicationCore is not initialized", "MainWindow");
        return true;
    }
    
    ConfigManager *configManager = appCore->getConfigManager();
    if (configManager) {
        configManager->addRecentFile(filePath);
        updateRecentFilesMenu();
        
        // Save the directory for future use
        QFileInfo fileInfo(filePath);
        configManager->setLastDirectory(fileInfo.absolutePath());
    }
    
    LOG_INFO(QString("File loaded successfully: %1").arg(filePath), "MainWindow");
    
    return true;
}

bool MainWindow::maybeSave()
{
    if (!fileModified) {
        return true;
    }
    
    const QMessageBox::StandardButton ret = QMessageBox::warning(
        this,
        tr("Optimize Application"),
        tr("The document has been modified.\n"
           "Do you want to save your changes?"),
        QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel
    );
    
    switch (ret) {
    case QMessageBox::Save:
        return on_actionSave_triggered(), true;
    case QMessageBox::Cancel:
        return false;
    default:
        break;
    }
    
    return true;
}

void MainWindow::setCurrentFile(const QString &filePath)
{
    LOG_INFO(QString("Setting current file: %1").arg(filePath.isEmpty() ? "none" : filePath), "MainWindow");
    
    currentFilePath = filePath;
    setModified(false);
    
    updateWindowTitle(filePath);
}

void MainWindow::updateWindowTitle(const QString &filePath)
{
    QString title;
    
    if (filePath.isEmpty()) {
        title = tr("Untitled - %1").arg(AppConstants::APP_NAME);
    } else {
        QFileInfo fileInfo(filePath);
        title = tr("%1 - %2").arg(fileInfo.fileName(), AppConstants::APP_NAME);
    }
    
    setWindowTitle(title);
}

void MainWindow::setModified(bool modified)
{
    if (fileModified != modified) {
        fileModified = modified;
        
        // Update window title to show modified state
        QString title = windowTitle();
        if (modified && !title.startsWith('*')) {
            setWindowTitle("*" + title);
        } else if (!modified && title.startsWith('*')) {
            setWindowTitle(title.mid(1));
        }
    }
} 