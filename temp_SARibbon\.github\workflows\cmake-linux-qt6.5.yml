name: CMake-Linux-Qt6.5LTS

on: [pull_request,push]

env:
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release

jobs:
  build:
    runs-on: ubuntu-${{ matrix.ubuntu_version }}
    name: Ubuntu-${{ matrix.ubuntu_version }}-Qt-${{ matrix.qt_version }}-shared-${{ matrix.shared }}
    strategy:
      fail-fast: false
      matrix:
        ubuntu_version: [latest]
        qt_version: [6.5.*]
        shared: [ON]

    steps:
    - name: Checkout
      uses: actions/checkout@v3

    - name: Install Qt
      uses: jurplel/install-qt-action@v3
      with:
          version: ${{ matrix.qt_version }}
          cache: 'true'
          cache-key-prefix: ${{ runner.os }}-Qt-Cache-${{ matrix.qt_version }}
          dir: ${{ github.workspace }}/Qt

    - name: Install Linux dependencies
      run: |
        sudo apt install -y libgl1-mesa-dev libxcb1-dev libgtk-3-dev

    - name: Configure CMake
      run: cmake -DCMAKE_BUILD_TYPE="${{env.BUILD_TYPE}}" -DBUILD_SHARED_LIBS=${{ matrix.shared }} -DBUILD_EXAMPLES=OFF -B "${{github.workspace}}/build"

    - name: Build
      run: cmake --build ${{github.workspace}}/build --config ${{env.BUILD_TYPE}}