#ifndef EVENTBUS_H
#define EVENTBUS_H

#include <QObject>
#include <QMap>
#include <QList>
#include <QMetaObject>
#include <QString>
#include <functional>
#include <typeinfo>

/**
 * @brief 事件总线类，实现发布-订阅模式
 * 
 * EventBus提供了一个中心化的事件发布和订阅机制，
 * 允许应用程序组件之间进行松耦合的通信。
 */
class EventBus : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     */
    EventBus();

    /**
     * @brief 析构函数
     */
    ~EventBus();

    /**
     * @brief 发布事件
     * @tparam EventType 事件类型
     * @param event 事件实例
     */
    template<typename EventType>
    void publish(const EventType& event)
    {
        QString eventTypeName = QString(typeid(EventType).name());
        
        // 发送事件信号
        emit eventPublished(eventTypeName, QVariant::fromValue(event));
        
        // 直接调用已注册的处理函数
        if (m_eventHandlers.contains(eventTypeName)) {
            for (const auto& handler : m_eventHandlers[eventTypeName]) {
                handler(event);
            }
        }
    }

    /**
     * @brief 订阅事件
     * @tparam EventType 事件类型
     * @tparam Receiver 接收者类型
     * @param receiver 接收者实例
     * @param method 接收者方法
     * @return 订阅ID，用于取消订阅
     */
    template<typename EventType, typename Receiver>
    int subscribe(Receiver* receiver, void (Receiver::*method)(const EventType&))
    {
        if (!receiver || !method) {
            return -1;
        }

        QString eventTypeName = QString(typeid(EventType).name());
        
        // 创建Lambda处理函数
        auto handler = [receiver, method](const EventType& event) {
            (receiver->*method)(event);
        };
        
        // 生成唯一ID
        int handlerId = m_nextHandlerId++;
        
        // 存储处理函数
        m_eventHandlers[eventTypeName].append(std::function<void(const EventType&)>(handler));
        m_handlerIds[handlerId] = eventTypeName;
        
        return handlerId;
    }

    /**
     * @brief 取消订阅
     * @param handlerId 订阅ID
     * @return 是否成功取消
     */
    bool unsubscribe(int handlerId);

signals:
    /**
     * @brief 事件发布信号
     * @param eventType 事件类型名称
     * @param eventData 事件数据
     */
    void eventPublished(const QString& eventType, const QVariant& eventData);

private:
    // 事件处理函数映射
    QMap<QString, QList<std::function<void(const QVariant&)>>> m_eventHandlers;
    
    // 处理函数ID映射
    QMap<int, QString> m_handlerIds;
    
    // 下一个处理函数ID
    int m_nextHandlerId;
};

#endif // EVENTBUS_H
