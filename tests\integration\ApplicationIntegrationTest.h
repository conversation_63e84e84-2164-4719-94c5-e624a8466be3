#ifndef APPLICATIONINTEGRATIONTEST_H
#define APPLICATIONINTEGRATIONTEST_H

#include <QtTest>
#include <QObject>
#include <QTemporaryDir>
#include "../../src/core/application/ApplicationCore.h"

/**
 * @brief 应用程序集成测试类
 * 
 * 测试应用程序各组件之间的集成和交互，包括核心服务的初始化、
 * 配置管理、主题切换、事件系统等。
 */
class ApplicationIntegrationTest : public QObject
{
    Q_OBJECT

private slots:
    /**
     * @brief 测试初始化
     */
    void initTestCase();

    /**
     * @brief 测试清理
     */
    void cleanupTestCase();

    /**
     * @brief 每个测试前的初始化
     */
    void init();

    /**
     * @brief 每个测试后的清理
     */
    void cleanup();

    /**
     * @brief 测试应用程序核心初始化
     */
    void testApplicationInitialization();

    /**
     * @brief 测试服务定位器功能
     */
    void testServiceLocator();

    /**
     * @brief 测试配置管理集成
     */
    void testConfigurationIntegration();

    /**
     * @brief 测试主题管理集成
     */
    void testThemeManagementIntegration();

    /**
     * @brief 测试日志管理集成
     */
    void testLogManagementIntegration();

    /**
     * @brief 测试事件系统集成
     */
    void testEventSystemIntegration();

    /**
     * @brief 测试插件系统集成
     */
    void testPluginSystemIntegration();

    /**
     * @brief 测试错误处理集成
     */
    void testErrorHandlingIntegration();

    /**
     * @brief 测试数据模型集成
     */
    void testDataModelIntegration();

    /**
     * @brief 测试文件解析器集成
     */
    void testFileParserIntegration();

    /**
     * @brief 测试数据仓库集成
     */
    void testRepositoryIntegration();

    /**
     * @brief 测试完整工作流程
     */
    void testCompleteWorkflow();

    /**
     * @brief 测试并发访问
     */
    void testConcurrentAccess();

    /**
     * @brief 测试内存管理
     */
    void testMemoryManagement();

    /**
     * @brief 测试性能
     */
    void testPerformance();

private:
    /**
     * @brief 创建测试事件
     */
    struct TestEvent {
        QString message;
        int value;
        
        TestEvent(const QString& msg = QString(), int val = 0)
            : message(msg), value(val) {}
    };

    /**
     * @brief 测试事件接收器
     */
    class TestEventReceiver : public QObject {
        Q_OBJECT
    public:
        QStringList receivedMessages;
        QList<int> receivedValues;
        
        void handleTestEvent(const TestEvent& event) {
            receivedMessages.append(event.message);
            receivedValues.append(event.value);
        }
    };

    /**
     * @brief 验证应用程序状态
     * @return 验证是否通过
     */
    bool verifyApplicationState();

    /**
     * @brief 创建测试数据文件
     * @param content 文件内容
     * @return 文件路径
     */
    QString createTestDataFile(const QString& content);

    QTemporaryDir* m_tempDir;
    ApplicationCore* m_appCore;
    TestEventReceiver* m_eventReceiver;
};

#endif // APPLICATIONINTEGRATIONTEST_H
