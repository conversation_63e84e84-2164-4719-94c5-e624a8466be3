﻿/*
这是模仿office2016的主题模板
通过替换占位符实现主题的设置
{Ribbon.BKColor} : ribbon的背景颜色，决定标题栏等位置的颜色

{Category.BKColor} : Category的背景颜色，SARibbonStackedWidget的背景颜色应该和SARibbonCategory的背景颜色一致
{Category.BorderColor}:Category的边框颜色，默认{Category.BKColor}一致

{CategoryScrollButton.BorderColor}:Category的移动按钮的边框颜色，建议等于{ToolButton.BorderColor:hover}
{CategoryScrollButton.BKColor}:Category的移动按钮的背景颜色，建议等于{ToolButton.BKColor}
{CategoryScrollButton.BKColor:hover}:Category的移动按钮hover下的背景颜色，建议等于{ToolButton.BKColor:hover}

{ApplicationButton.BKColor}:建议和{Ribbon.BKColor}一致
{ApplicationButton.BKColor:hover}:{ApplicationButton.BKColor}hover状态的颜色
{ApplicationButton.BKColor:pressed}:{ApplicationButton.BKColor}pressed状态的颜色

{Tab.Color} : Tab的文字颜色，建议和{Ribbon.BKColor}颜色的反色，区分度要高
{Tab.Color:selected}: 选中Tab的文字颜色，建议和{Ribbon.BKColor}一致
{Tab.BorderColor:selected}:选中Tab的边框颜色，建议和{Tab.Color:selected}一致
{Tab.BKColor:selected}:选中Tab的背景颜色，建议和{Category.BKColor}一致
{Tab.BorderColor:hover:!selected}:tab没被选中，但出于hover状态下的边框颜色，建议和{Tab.Color}一致
{Tab.Color:hover:!selected}:tab没被选中，但出于hover状态下的文字颜色，建议和{Tab.Color}一致

{ToolButton.BKColor}:按钮的背景颜色，应该和{Category.BKColor}一致
{ToolButton.Color}:按钮的文字色
{ToolButton.Color:pressed}:按钮按下状态的文字色
{ToolButton.BorderColor:pressed}:按钮按下状态的边框颜色
{ToolButton.BKColor:pressed}:按钮按下状态的背景颜色
{ToolButton.Color:checked}:按钮选中状态的文字色
{ToolButton.BorderColor:checked}:按钮选中状态的边框颜色
{ToolButton.BKColor:checked}:按钮选中状态的背景颜色
{ToolButton.Color:hover}:按钮hover状态的文字色
{ToolButton.BorderColor:hover}:按钮hover状态的边框颜色
{ToolButton.BKColor:hover}:按钮hover状态的背景颜色

{PannelButtonGroup.BorderColor}:Pannel下的buttongroup的边框颜色，建议和{ToolButton.BorderColor:hover}一致

{ControlButton.BKColor}:工具栏按钮的背景颜色，建议为透明
{ControlButton.BorderColor}:工具栏按钮的边框颜色，建议为透明
{ControlButton.BorderColor:pressed}:工具栏按钮按下后的边框颜色
{ControlButton.BKColor:pressed}:工具栏按钮按下后的背景颜色
{ControlButton.BorderColor:checked}:工具栏按钮checked状态的背景颜色
{ControlButton.BKColor:checked}:工具栏按钮checked状态的背景颜色
{ControlButton.BorderColor:hover}:工具栏按钮hover状态的背景颜色
{ControlButton.BKColor:hover}:工具栏按钮hover状态的背景颜色

{GalleryButton.BKColor}:GalleryButton按钮的背景颜色，建议为透明
{GalleryButton.BorderColor}:GalleryButton按钮的边框颜色，建议为透明
{GalleryButton.BorderColor:hover}:GalleryButton按钮hover状态的背景颜色
{GalleryButton.BKColor:hover}:GalleryButton按钮hover状态的背景颜色
{Gallery.BorderColor}:Gallery的边框颜色，建议为{ToolButton.BKColor:hover}
{Gallery.BKColor}:Gallery的背景颜色，建议为transparent
{Gallery.Color}:Gallery的文字颜色，建议为{ToolButton.Color}
{GalleryGroup.BKColor}:GalleryGroup的背景颜色，建议为transparent
{GalleryGroup.BorderColor}:GalleryGroup的BorderColor，建议为{Gallery.BorderColor}
{GalleryGroup.BKColor:selected}:GalleryGroup选中的条目的背景颜色建议为{ToolButton.BKColor:selected}
{GalleryGroup.Color:selected}:GalleryGroup选中的条目的文字颜色建议为{ToolButton.Color}

{Menu.Color}:菜单文字颜色，建议为{ToolButton.Color}
{Menu.BKColor}:菜单背景颜色，建议为{ToolButton.Color}
{Menu.BorderColor}:菜单边框颜色，建议为{Menu.BKColor}
{Menu.BKColor:selected}菜单选中条目的背景颜色，建议为{ToolButton.BKColor:checked}
{Menu.BKColor:hover}菜单hover条目的背景颜色，建议为 {ToolButton.BKColor:hover}

{OptionButton.BKColor}:pannel下的OptionButton的背景颜色，建议为transparent
{OptionButton.Color}:pannel下的OptionButton的文字颜色，建议为{ToolButton.Color}
{OptionButton.BKColor:hover}:pannel下的OptionButton在hover状态下的背景颜色，建议为{ToolButton.BKColor:hover}
*/

/*SARibbonApplicationWidget*/
SARibbonApplicationWidget{
    background-color: #e5e3e5;/* {Ribbon.BKColor} */
    border: solid #e5e3e5;
    color:#242424;
}

/*SARibbonBar*/
SARibbonBar {
  background-color: #e5e3e5;/* {Ribbon.BKColor} */
  border: solid #e5e3e5;
  color:#242424;
}


/*SARibbonQuickAccessBar*/
SARibbonQuickAccessBar {
  background-color: transparent;
}

/*SARibbonCtrlContainer*/
SARibbonCtrlContainer {
  background-color: transparent;
}

/*
SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
如果想给不同的部件下的分割线设置不同风格，可以使用如SARibbonCategory > SARibbonSeparatorWidget的方式，参考theme-dark2.qss
*/
SARibbonSeparatorWidget {
  color: #bec0c2;
}

/*SARibbonCategory*/
SARibbonCategory:focus {
  outline: none;
}

SARibbonCategory {
  background-color: #ffffff;/* {Category.BKColor} */
}

/*
SARibbonCategory下的SARibbonSeparatorWidget
注意，SARibbonSeparatorWidget的背景颜色是使用color
*/
SARibbonCategory > SARibbonSeparatorWidget {
  color: #bec0c2;
  margin-top:3px;
  margin-bottom:3px;
}

/*SARibbonPannel*/

SARibbonPannel {
  background-color: #ffffff;
  border: none;
  color: #666666;
}

/*SARibbonButtonGroupWidget*/
SARibbonButtonGroupWidget {
  background-color: transparent;
}

SARibbonPannel > SARibbonButtonGroupWidget {
  border: 1px solid #606060;/* {PannelButtonGroup.BorderColor} */
}

/*SARibbonPannelLabel Pannel下标题栏*/

SARibbonPannelLabel {
    background-color: transparent;
    color: #666666;
}

/*SARibbonPannelOptionButton*/

SARibbonPannelOptionButton {
  background-color: transparent;/*{OptionButton.BKColor}*/
  color: #333;/*{OptionButton.Color}*/
}

SARibbonPannelOptionButton:hover {
  background-color: #c5c5c5;/*{OptionButton.BKColor:hover}*/
  border: 0px;
}




/*SARibbonCategoryScrollButton*/

SARibbonCategoryScrollButton {
  border: 1px solid #c5c5c5;/*{CategoryScrollButton.BorderColor}*/
  background-color: #f1f1f1;/*{CategoryScrollButton.BKColor}*/
}

SARibbonCategoryScrollButton[arrowType="3"] {
  border-right-width: 1px;
}

SARibbonCategoryScrollButton[arrowType="4"] {
  border-left-width: 1px;
}

SARibbonCategoryScrollButton:hover {
  background-color: #c5c5c5;/*{CategoryScrollButton.BKColor:hover}*/
}


/*SARibbonStackedWidget*/
SARibbonStackedWidget {
  border: 1pt solid #ffffff;/* {Category.BorderColor} */
  border-top-width: 0px;
}

SARibbonStackedWidget:focus {
  outline: none;
}

/*SARibbonApplicationButton*/
SARibbonApplicationButton {
  border:none;
  color: #242424;
  background-color: transparent;/* {ApplicationButton.BKColor} */
  padding-left: 0px;
  padding-right: 0px;
  padding-top: -5px;
  padding-bottom: -5px;
}

SARibbonApplicationButton:hover {
  /* 上下等高文字不移动 */
  border-top:5px solid transparent;
  border-bottom: 5px solid #d0ced1;
  border-right:none;
  border-left:none;
}

SARibbonApplicationButton:pressed {
  /* 上下等高文字不移动 */
  border-top:5px solid transparent;
  border-bottom: 5px solid #2760a7;
  border-right:none;
  border-left:none;
}

SARibbonApplicationButton:focus {
  outline: none;
}

SARibbonApplicationButton::menu-indicator {
  /*subcontrol-position: right;*/
  width:0px;
}

/*SARibbonTabBar*/
SARibbonTabBar {
  background-color: transparent;
}


SARibbonTabBar::tab {
  color: #242424;/* {Tab.Color} */
  border: none;
  background: transparent;
  margin-top: 0px;
  margin-right: 5px;
  margin-left: 5px;
  margin-bottom: 0px;
  min-width: 50px;
  padding-left: 0px;
  padding-right: 0px;
  padding-top: -4px;
  padding-bottom: -4px;
}

SARibbonTabBar::tab:selected {
  color: #2760a7;/* {Tab.Color:selected} */
  /*顶部要给一个透明的border高度，否则文字会向上偏移*/
  border-top:4px solid transparent;
  border-bottom: 4px solid #2760a7;
}

SARibbonTabBar::tab:hover:!selected {
  /*顶部要给一个透明的border高度，否则文字会向上偏移*/
  border-top:4px solid transparent;
  border-bottom: 4px solid #d0ced1;
  color: #242424;/* {Tab.Color:hover:!selected} */
}


/*SARibbonToolButton*/
/* 
SARibbonToolButton的背景颜色必须和pannel或者category的颜色一致
不能设置为transparent，否则在ActionMenu模式下，上下区分的按钮无法显示 
*/
SARibbonToolButton {
  border-radius:4px;
  border: 1px solid #ffffff;
  color: #242424;/* {ToolButton.Color} */
  background-color: #ffffff;/* {ToolButton.BKColor} */
}

SARibbonToolButton:pressed {
  border-radius:4px;
  border: 1px solid #ffffff;
  color: #000;/* {ToolButton.Color:pressed} */
  background-color: #e1e1e1;/* {ToolButton.BKColor:pressed} */
}

SARibbonToolButton:hover {
  border-radius:4px;
  border: 1px solid #ffffff;
  color: #e1e1e1;/* {ToolButton.Color:hover} */
  background-color: #f5f6f6;/* {ToolButton.BKColor:hover} */
}

SARibbonToolButton:checked {
  border: 1px solid #5f5f5f;/* {ToolButton.BorderColor:checked} */
  border-radius:4px;
  color: #242424;/* {ToolButton.Color:checked} */
  background-color: #ebebeb;/* {ToolButton.BKColor:checked} */
}

SARibbonToolButton:checked:hover {
  border: 1px solid #5f5f5f;/* {ToolButton.BorderColor:checked} */
  border-radius:4px;
  color: #242424;/* {ToolButton.Color:checked} */
  background-color: #e1e1e1;/* {ToolButton.BKColor:checked} */
}

/*SARibbonControlToolButton*/

SARibbonControlToolButton {
  background-color: #e5e3e5;/*{ControlButton.BKColor}*/
  color: #333;
  border: 1px solid #e5e3e5;
}

SARibbonControlToolButton:pressed {
  border: 1px solid transparent;/*{ControlButton.BorderColor:pressed}*/
  background-color: #f0f0f0;/*{ControlButton.BKColor:pressed}*/
}

SARibbonControlToolButton:checked {
  border: 1px solid #5f5f5f;/*{ControlButton.BorderColor:checked}*/
  border-radius:4px;
  background-color: #ebebeb;/*{ControlButton.BKColor:checked}*/
}

SARibbonControlToolButton:hover {
  border: 1px solid #d5d3d5;/*{ControlButton.BorderColor:hover}*/
  color: #e1e1e1;/* {ToolButton.Color:hover} */
  background-color: #f5f6f6;/*{ControlButton.BKColor:hover}*/
}

/*SARibbonControlButton*/

SARibbonControlButton {
  background-color: transparent;/*{ControlButton.BKColor}*/
  border: 1px solid transparent;/*{ControlButton.BorderColor}*/
  color: #333;
}

SARibbonControlButton:pressed {
  border: 1px solid transparent;/*{ControlButton.BorderColor:pressed}*/
  background-color: #e1e1e1;/*{ControlButton.BKColor:pressed}*/
}

SARibbonControlButton:checked {
  border: 1px solid #5f5f5f;/*{ControlButton.BorderColor:checked}*/
  border-radius:4px;
  background-color: #ebebeb;/*{ControlButton.BKColor:checked}*/
}

SARibbonControlButton:hover {
  border: 1px solid transparent;/*{ControlButton.BorderColor:hover}*/
  background-color: #f5f6f6;/*{ControlButton.BKColor:hover}*/
}

SARibbonGalleryButton {
  border: 1px solid #f1f1f1;/*{GalleryButton.BorderColor}*/
  background-color: #f1f1f1;/*{GalleryButton.BKColor}*/
}
SARibbonGalleryButton:hover {
  border: 1px solid #c5c5c5;/*{GalleryButton.BorderColor:hover}*/
  background-color: #c5c5c5;/*{GalleryButton.BKColor:hover}*/
}



/*SARibbonGallery*/

SARibbonGallery {
  border: 1px solid #c5c5c5;/*{Gallery.BorderColor}*/
  background-color: transparent;/*{Gallery.BKColor}*/
  color: #333;/*{Gallery.Color}*/
}

/*SARibbonGalleryGroup*/

SARibbonGalleryGroup {
  show-decoration-selected: 1;
  background-color: transparent;/*{GalleryGroup.BKColor}*/
  color: #333;
  border: 1px solid #c5c5c5;/*{GalleryGroup.BorderColor}*/
}

SARibbonGalleryGroup::item:selected {
  background-color: #c5c5c5;/*{GalleryGroup.BKColor:selected}*/
  color: black;/*{GalleryGroup.Color:selected}*/
}

SARibbonGalleryGroup::item:hover {
  border: 1px solid #c5c5c5;
  background-color: #c5c5c5;
}

/*RibbonGalleryViewport*/

SARibbonGalleryViewport {
  background-color: #ffffff;
}

/*SARibbonMenu*/

SARibbonMenu {
  color: #333;/*{Menu.Color}*/
  background-color: #f1f1f1;/*{Menu.BKColor}*/
  border: 1px solid #FCFCFC;/*{Menu.BorderColor}*/
}

SARibbonMenu::item {
  padding: 5px 5px 5px 5px;
  background-color: transparent;
}

SARibbonMenu::item:selected {
  background-color: #c6c6c6;/*{Menu.BKColor:selected}*/
}

SARibbonMenu::item:hover {
  color: #000;
  background-color: #c5c5c5;/*{Menu.BKColor:hover}*/
  border: 1px solid #c5c5c5;/*{Menu.BorderColor:hover}*/
}

SARibbonMenu::icon {
  margin-left: 1px;
}


/*SARibbonLineEdit*/

SARibbonLineEdit {
  border: 1px solid #C0C2C4;
  background: #FFF;
  selection-background-color: #9BBBF7;
  selection-color: #000;
}

/*SARibbonComboBox*/

SARibbonComboBox {
  border: 1px solid #c2d0df;
  background-color: white;
}

SARibbonComboBox:hover {
  border: 1px solid #269bf4;
  color: #000;
}

SARibbonComboBox:editable {
  color: #000;
  background: white;
  selection-background-color: #9BBBF7;
  selection-color: #000;
}

SARibbonComboBox::drop-down {
  subcontrol-origin: padding;
  subcontrol-position: top right;
  width: 1em;
  border: none;
}

SARibbonComboBox::drop-down:hover {
  border: 1px solid #FDEEB3;
  background-color: #9ed2f9;
}

SARibbonComboBox::down-arrow {
  image: url(:/SARibbon/image/resource/ArrowDown.png);
}

/*SARibbonSeparatorWidget*/

SARibbonSeparatorWidget {
  /*background-color: transparent;*/
  background-color: white;
}

/*SARibbonSystemToolButton*/

SARibbonSystemToolButton {
  background-color: transparent;
  border:none;
}

SARibbonSystemToolButton:focus {
  outline: none;
}
/*Min*/
SARibbonSystemToolButton#SAMinimizeWindowButton {
  background-position:center;
  background-repeat: no-repeat;
  background-image: url(:/SARibbon/image/resource/Titlebar_Min.svg);
}

SARibbonSystemToolButton#SAMinimizeWindowButton:hover{
  background-color: #f5f6f6;
}
SARibbonSystemToolButton#SAMinimizeWindowButton:pressed{
  background-color: #cacacb;

}
/*Max*/
SARibbonSystemToolButton#SAMaximizeWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/SARibbon/image/resource/Titlebar_Max.svg);
}
SARibbonSystemToolButton#SAMaximizeWindowButton:hover {
    background-color: #f5f6f6;
}
SARibbonSystemToolButton#SAMaximizeWindowButton:checked {
    background-image: url(:/SARibbon/image/resource/Titlebar_Normal.svg) center;
}


/*Close*/
SARibbonSystemToolButton#SACloseWindowButton {
    background-position:center;
    background-repeat: no-repeat;
    background-image: url(:/SARibbon/image/resource/Titlebar_Close.svg);
}
SARibbonSystemToolButton#SACloseWindowButton:hover {
  background-color: #e81123;
}
SARibbonSystemToolButton#SACloseWindowButton:pressed {
    background-color: #f1707a;
}

