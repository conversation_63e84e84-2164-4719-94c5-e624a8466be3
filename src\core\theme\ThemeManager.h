#ifndef THEMEMANAGER_H
#define THEMEMANAGER_H

#include <QObject>
#include <QString>
#include <QMap>
#include <QColor>
#include <QPalette>
#include <QStyle>

/**
 * @brief 主题类型枚举
 */
enum class ThemeType {
    Light,
    Dark,
    System,
    Custom
};

/**
 * @brief 主题管理器类，负责应用程序主题的管理和切换
 * 
 * ThemeManager提供了主题的加载、切换和自定义功能，
 * 支持亮色、暗色和自定义主题。
 */
class ThemeManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     */
    ThemeManager();

    /**
     * @brief 析构函数
     */
    ~ThemeManager();

    /**
     * @brief 初始化主题管理器
     * @return 初始化是否成功
     */
    bool initialize();

    /**
     * @brief 设置当前主题
     * @param themeName 主题名称
     * @return 设置是否成功
     */
    bool setTheme(const QString& themeName);

    /**
     * @brief 设置主题类型
     * @param type 主题类型
     * @return 设置是否成功
     */
    bool setThemeType(ThemeType type);

    /**
     * @brief 获取当前主题名称
     * @return 当前主题名称
     */
    QString currentTheme() const;

    /**
     * @brief 获取当前主题类型
     * @return 当前主题类型
     */
    ThemeType currentThemeType() const;

    /**
     * @brief 获取可用主题列表
     * @return 主题名称列表
     */
    QStringList availableThemes() const;

    /**
     * @brief 加载主题
     * @param themeName 主题名称
     * @return 加载是否成功
     */
    bool loadTheme(const QString& themeName);

    /**
     * @brief 保存当前主题
     * @param themeName 主题名称
     * @return 保存是否成功
     */
    bool saveTheme(const QString& themeName);

    /**
     * @brief 创建自定义主题
     * @param themeName 主题名称
     * @param baseTheme 基础主题名称
     * @return 创建是否成功
     */
    bool createCustomTheme(const QString& themeName, const QString& baseTheme);

    /**
     * @brief 删除自定义主题
     * @param themeName 主题名称
     * @return 删除是否成功
     */
    bool deleteCustomTheme(const QString& themeName);

    /**
     * @brief 设置主题颜色
     * @param role 颜色角色
     * @param color 颜色值
     */
    void setThemeColor(QPalette::ColorRole role, const QColor& color);

    /**
     * @brief 获取主题颜色
     * @param role 颜色角色
     * @return 颜色值
     */
    QColor themeColor(QPalette::ColorRole role) const;

    /**
     * @brief 应用主题到应用程序
     */
    void applyTheme();

signals:
    /**
     * @brief 主题变更信号
     * @param themeName 新的主题名称
     */
    void themeChanged(const QString& themeName);

private:
    /**
     * @brief 加载内置主题
     */
    void loadBuiltinThemes();

    /**
     * @brief 加载自定义主题
     */
    void loadCustomThemes();

    /**
     * @brief 应用亮色主题
     */
    void applyLightTheme();

    /**
     * @brief 应用暗色主题
     */
    void applyDarkTheme();

    /**
     * @brief 应用系统主题
     */
    void applySystemTheme();

    /**
     * @brief 应用自定义主题
     * @param themeName 主题名称
     */
    void applyCustomTheme(const QString& themeName);

    /**
     * @brief 获取主题样式表
     * @param themeName 主题名称
     * @return 样式表内容
     */
    QString getThemeStyleSheet(const QString& themeName) const;

    QString m_currentTheme;
    ThemeType m_currentThemeType;
    QMap<QString, QString> m_themeStyleSheets;
    QMap<QString, QPalette> m_themePalettes;
    QPalette m_currentPalette;
};

#endif // THEMEMANAGER_H
