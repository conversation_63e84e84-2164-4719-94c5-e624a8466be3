#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF to Word Converter
Converts PDF files to Word format using pdf2docx library
"""

import os
import sys
from pdf2docx import Converter
from pathlib import Path

def convert_pdf_to_word(pdf_path, output_path=None):
    """
    Convert PDF file to Word document
    
    Args:
        pdf_path (str): Path to input PDF file
        output_path (str): Path for output Word file (optional)
    
    Returns:
        str: Path to the converted Word file
    """
    
    # Check if input file exists
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")
    
    # Generate output path if not provided
    if output_path is None:
        pdf_file = Path(pdf_path)
        output_path = pdf_file.parent / f"{pdf_file.stem}.docx"
    
    print(f"Converting PDF to Word...")
    print(f"Input file: {pdf_path}")
    print(f"Output file: {output_path}")
    
    try:
        # Create converter instance
        cv = Converter(pdf_path)
        
        # Convert PDF to Word
        cv.convert(output_path, start=0, end=None)
        
        # Close converter
        cv.close()
        
        print(f"✅ Conversion completed successfully!")
        print(f"Word file saved to: {output_path}")
        
        return str(output_path)
        
    except Exception as e:
        print(f"❌ Error during conversion: {str(e)}")
        raise

def main():
    """Main function"""
    
    # Define the PDF file path
    pdf_file_path = r"D:\核动力院\RELAP5\relap5输入卡介绍中文.pdf"
    
    # Define output path
    output_file_path = r"D:\核动力院\RELAP5\relap5输入卡介绍中文.docx"
    
    try:
        # Convert PDF to Word
        result = convert_pdf_to_word(pdf_file_path, output_file_path)
        
        print("\n" + "="*60)
        print("🎉 PDF to Word conversion completed!")
        print(f"📄 Original PDF: {pdf_file_path}")
        print(f"📝 Word document: {result}")
        print("="*60)
        
    except FileNotFoundError as e:
        print(f"❌ File not found error: {e}")
        print("Please check if the PDF file exists at the specified path.")
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        print("Please check the PDF file and try again.")

if __name__ == "__main__":
    main() 