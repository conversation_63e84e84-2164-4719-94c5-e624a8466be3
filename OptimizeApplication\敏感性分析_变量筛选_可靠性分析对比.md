# 敏感性分析 vs 变量筛选 vs 可靠性分析

## 概述

本文档详细解释敏感性分析、变量筛选和可靠性分析这三个概念的区别，以及它们在Dakota中的应用。

## 1. 敏感性分析 (Sensitivity Analysis)

### 定义与目的
- **定义**: 研究输入变量的变化对输出响应影响程度的分析方法
- **目的**: 量化输入不确定性如何传播到输出，识别关键影响因素

### 主要特征
- **范围**: 全面分析所有输入变量的影响
- **结果**: 提供定量的敏感性指标（如Sobol指数、敏感性系数）
- **深度**: 深入分析变量影响的机制和程度

### 典型应用
- 模型验证和校准
- 不确定性量化
- 风险评估
- 系统理解和洞察

### Dakota中的方法
```dakota
method
  sampling
    sample_type lhs
    samples = 1000
    variance_based_decomp  # 计算Sobol指数
```

## 2. 变量筛选 (Variable Screening)

### 定义与目的
- **定义**: 从大量输入变量中快速识别重要变量，排除不重要变量的过程
- **目的**: 降低问题维度，为后续详细分析做准备

### 主要特征
- **范围**: 关注变量重要性的排序和分类
- **结果**: 提供变量重要性排序，通常是定性结果
- **深度**: 初步筛选，不追求精确量化

### 典型应用
- 高维问题的预处理
- 模型简化
- 计算成本控制
- 实验设计优化

### Dakota中的方法
```dakota
method
  psuade_moat  # Morris方法进行变量筛选
    samples = 100
    seed = 12345
```

## 3. 可靠性分析 (Reliability Analysis)

### 定义与目的
- **定义**: 评估系统在不确定性条件下满足性能要求的概率
- **目的**: 量化失效概率，评估系统安全性和可靠性

### 主要特征
- **范围**: 关注系统性能边界和失效模式
- **结果**: 提供失效概率、可靠性指标、安全因子
- **深度**: 专注于极值行为和风险评估

### 典型应用
- 结构安全评估
- 风险管理
- 设计余量确定
- 质量控制

### Dakota中的方法
```dakota
method
  local_reliability  # FORM/SORM方法
    mpp_search x_taylor_mean
    integration first_order
```

## 三者关系与区别对比

### 分析对象
| 方法 | 分析对象 | 关注点 |
|------|----------|--------|
| **敏感性分析** | 输入-输出关系 | 变量影响程度 |
| **变量筛选** | 变量重要性 | 重要变量识别 |
| **可靠性分析** | 系统性能边界 | 失效概率 |

### 输出结果
| 方法 | 主要输出 | 结果类型 |
|------|----------|----------|
| **敏感性分析** | Sobol指数、敏感性系数 | 定量指标 |
| **变量筛选** | 重要性排序、筛选结果 | 定性/半定量 |
| **可靠性分析** | 失效概率、可靠性指标 | 概率值 |

### 计算复杂度
| 方法 | 计算成本 | 样本需求 |
|------|----------|----------|
| **敏感性分析** | 高 | 数百到数千 |
| **变量筛选** | 低到中 | 几十到几百 |
| **可靠性分析** | 中到高 | 取决于方法 |

### 应用阶段
| 方法 | 典型应用阶段 | 作用 |
|------|--------------|------|
| **变量筛选** | 分析前期 | 问题简化 |
| **敏感性分析** | 分析中期 | 深入理解 |
| **可靠性分析** | 设计验证 | 安全评估 |

## 实际应用中的关系

### 顺序关系
```
变量筛选 → 敏感性分析 → 可靠性分析
   ↓           ↓            ↓
问题简化    深入理解      安全评估
```

### 组合使用示例

#### 工程设计流程
1. **变量筛选阶段**:
   ```dakota
   method
     psuade_moat
       samples = 50
   ```
   - 从30个设计变量中筛选出8个重要变量

2. **敏感性分析阶段**:
   ```dakota
   method
     sampling
       samples = 1000
       variance_based_decomp
   ```
   - 对筛选出的8个变量进行详细敏感性分析

3. **可靠性分析阶段**:
   ```dakota
   method
     local_reliability
   ```
   - 基于敏感性分析结果进行可靠性评估

## 选择指导原则

### 根据问题特征选择

#### 高维问题 (>20变量)
- **首选**: 变量筛选 (Morris方法)
- **目的**: 降维，识别关键变量
- **后续**: 对重要变量进行敏感性分析

#### 中维问题 (5-20变量)
- **首选**: 敏感性分析 (Sobol方法)
- **目的**: 全面理解变量影响
- **扩展**: 可结合可靠性分析

#### 低维问题 (<5变量)
- **可选**: 直接进行详细敏感性分析
- **优势**: 计算成本可接受
- **深度**: 可进行全面分析

### 根据分析目的选择

#### 模型理解
- **主要**: 敏感性分析
- **辅助**: 变量筛选（如果维度高）

#### 模型简化
- **主要**: 变量筛选
- **验证**: 敏感性分析

#### 风险评估
- **主要**: 可靠性分析
- **支撑**: 敏感性分析提供输入

#### 设计优化
- **组合**: 变量筛选 + 敏感性分析 + 可靠性分析
- **流程**: 筛选 → 理解 → 优化 → 验证

## Dakota敏感性分析算法使用场景

### 1. 采样方法 (Sampling Methods)

#### 方差分解 (Variance-Based Decomposition)
- **适用场景**:
  - 需要量化各输入变量对输出不确定性贡献的问题
  - 高维问题的变量筛选和重要性排序
  - 复杂非线性系统的全局敏感性分析
  - 模型简化前的变量重要性评估

- **优点**:
  - 提供定量的敏感性指标（Sobol指数）
  - 能处理变量间的交互作用
  - 适用于任何类型的模型（线性/非线性）
  - 结果易于解释和比较

- **缺点**:
  - 计算成本高（N*(dim+2)次仿真）
  - 需要大量样本（数百到数千个）
  - 对于高维问题计算量巨大

- **推荐使用条件**:
  - 变量数量 < 20
  - 模型计算成本适中
  - 需要精确的敏感性量化

#### Morris方法 (Morris One-at-a-Time)
- **适用场景**:
  - 高维问题的初步变量筛选
  - 计算资源有限的敏感性分析
  - 模型简化的预处理步骤
  - 快速识别重要和不重要变量

- **优点**:
  - 计算成本相对较低
  - 适合高维问题（可处理几十到上百个变量）
  - 能识别变量的单调性和交互作用
  - 实现简单，结果直观

- **缺点**:
  - 提供的是定性而非定量结果
  - 对于强非线性问题可能不够准确
  - 无法给出精确的敏感性指数

- **推荐使用条件**:
  - 变量数量 > 20
  - 需要快速筛选变量
  - 作为详细分析的预处理步骤

### 2. 局部可靠性方法 (Local Reliability)

#### FORM/SORM方法
- **适用场景**:
  - 可靠性分析中的敏感性评估
  - 失效概率对设计变量的敏感性
  - 安全系数和可靠性指标的敏感性分析
  - 结构工程和风险评估

- **优点**:
  - 计算效率高
  - 提供精确的局部敏感性信息
  - 适合可靠性导向的设计优化
  - 数学理论成熟

- **缺点**:
  - 仅提供设计点附近的局部信息
  - 需要梯度信息
  - 对于多峰值问题可能遗漏重要信息
  - 线性化假设可能不适用于强非线性问题

- **推荐使用条件**:
  - 可靠性分析问题
  - 梯度信息可获得
  - 关注特定设计点附近的敏感性

### 3. 多项式混沌展开 (PCE)

#### 全局代理模型方法
- **适用场景**:
  - 中等维度问题的全局敏感性分析
  - 需要同时进行不确定性量化和敏感性分析
  - 响应面构建和敏感性分析的结合
  - 多次查询的敏感性分析

- **优点**:
  - 一次构建，多次使用
  - 提供全局敏感性信息
  - 支持维度自适应
  - 可同时获得统计矩和敏感性指数

- **缺点**:
  - 对于高维问题维数灾难
  - 需要足够的训练数据
  - 对于非光滑函数效果可能不佳

- **推荐使用条件**:
  - 变量数量 < 15
  - 响应相对光滑
  - 需要多次敏感性查询
  - 同时需要UQ和SA结果

### 4. 随机配置 (Stochastic Collocation)

#### 插值方法
- **适用场景**:
  - 类似PCE的应用场景
  - 对于某些类型的不确定性分布更适合
  - 需要精确插值的问题
  - 中等维度的全局敏感性分析

- **优点**:
  - 精确插值训练点
  - 对某些问题收敛速度快
  - 支持维度自适应
  - 实现相对简单

- **缺点**:
  - 类似PCE的维数问题
  - 对训练点选择敏感
  - 外推能力有限

### 5. 活动子空间 (Active Subspaces)

#### 降维敏感性分析
- **适用场景**:
  - 高维问题的降维分析
  - 识别重要的变量组合方向
  - 复杂系统的敏感性可视化
  - 模型降维和简化

- **优点**:
  - 能处理高维问题
  - 识别变量组合的重要性
  - 支持敏感性可视化
  - 为后续分析提供降维基础

- **缺点**:
  - 需要大量梯度计算
  - 结果解释相对复杂
  - 对于某些问题可能无法有效降维

- **推荐使用条件**:
  - 高维问题（变量数量 > 10）
  - 梯度信息可获得
  - 怀疑存在低维结构
  - 需要降维分析

## 算法选择建议

### 按问题维度选择
- **低维 (< 10变量)**: Sobol方差分解、PCE、随机配置
- **中维 (10-20变量)**: Morris筛选 + PCE、活动子空间
- **高维 (> 20变量)**: Morris筛选、活动子空间

### 按计算预算选择
- **预算充足**: Sobol方差分解、PCE
- **预算有限**: Morris方法、局部可靠性方法
- **预算极限**: 基于梯度的局部方法

### 按分析目的选择
- **变量筛选**: Morris方法
- **精确量化**: Sobol方差分解
- **可靠性分析**: 局部可靠性方法
- **降维分析**: 活动子空间
- **多目标分析**: PCE或随机配置

### 组合使用策略
1. **两阶段策略**: Morris筛选 → Sobol精确分析
2. **降维策略**: 活动子空间 → 低维详细分析
3. **验证策略**: 多种方法交叉验证结果

## 总结

这三种方法虽然都涉及不确定性分析，但各有不同的侧重点：

- **变量筛选**: 快速识别，问题简化
- **敏感性分析**: 深入理解，量化影响  
- **可靠性分析**: 风险评估，安全验证

在实际应用中，它们往往是互补的，形成一个完整的不确定性分析工作流程。选择哪种方法取决于具体的分析目的、问题特征和可用资源。

选择合适的敏感性分析算法需要综合考虑问题特征、计算资源、精度要求和分析目的。

---

*文档创建时间: 2024年*
*适用于: Dakota不确定性分析* 