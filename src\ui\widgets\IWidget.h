#ifndef IWIDGET_H
#define IWIDGET_H

#include <QString>
#include <QIcon>
#include <QVariantMap>
#include <QSettings>

/**
 * @brief 基础控件接口
 * 
 * 所有自定义控件都应该实现此接口，以提供统一的控件管理功能。
 */
class IWidget
{
public:
    /**
     * @brief 虚析构函数
     */
    virtual ~IWidget() = default;

    /**
     * @brief 获取控件名称
     * @return 控件名称
     */
    virtual QString widgetName() const = 0;

    /**
     * @brief 获取控件图标
     * @return 控件图标
     */
    virtual QIcon widgetIcon() const = 0;

    /**
     * @brief 获取控件分类
     * @return 控件分类
     */
    virtual QString widgetCategory() const = 0;

    /**
     * @brief 保存控件状态
     * @param settings 设置对象
     */
    virtual void saveState(QSettings& settings) = 0;

    /**
     * @brief 恢复控件状态
     * @param settings 设置对象
     */
    virtual void restoreState(const QSettings& settings) = 0;

    /**
     * @brief 获取控件描述
     * @return 控件描述
     */
    virtual QString widgetDescription() const { return QString(); }

    /**
     * @brief 获取控件版本
     * @return 控件版本
     */
    virtual QString widgetVersion() const { return "1.0.0"; }

    /**
     * @brief 检查控件是否有效
     * @return 是否有效
     */
    virtual bool isValid() const { return true; }
};

#endif // IWIDGET_H
