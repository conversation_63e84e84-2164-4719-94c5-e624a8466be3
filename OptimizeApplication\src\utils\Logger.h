#ifndef LOGGER_H
#define LOGGER_H

#include <QObject>
#include <QString>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QMutex>
#include "Common.h"

class Logger : public QObject
{
    Q_OBJECT

public:
    static Logger* instance();
    
    void setLogLevel(LogLevel level);
    LogLevel getLogLevel() const;
    
    void setLogToFile(bool enabled);
    bool isLogToFileEnabled() const;
    
    void setLogFilePath(const QString& path);
    QString getLogFilePath() const;
    
    void log(LogLevel level, const QString& message, const QString& category = QString());
    
    // 便捷方法
    void debug(const QString& message, const QString& category = QString());
    void info(const QString& message, const QString& category = QString());
    void warning(const QString& message, const QString& category = QString());
    void error(const QString& message, const QString& category = QString());
    void critical(const QString& message, const QString& category = QString());

signals:
    void logMessage(LogLevel level, const QString& message, const QString& category);

private:
    explicit Logger(QObject *parent = nullptr);
    ~Logger();
    
    void writeToFile(const QString& formattedMessage);
    QString formatMessage(LogLevel level, const QString& message, const QString& category);
    QString logLevelToString(LogLevel level);
    
    static Logger* m_instance;
    static QMutex m_mutex;
    
    LogLevel m_logLevel;
    bool m_logToFile;
    QString m_logFilePath;
    QFile* m_logFile;
    QTextStream* m_logStream;
    QMutex m_fileMutex;
};

// 全局日志宏
#define LOG_DEBUG(msg, category) Logger::instance()->debug(msg, category)
#define LOG_INFO(msg, category) Logger::instance()->info(msg, category)
#define LOG_WARNING(msg, category) Logger::instance()->warning(msg, category)
#define LOG_ERROR(msg, category) Logger::instance()->error(msg, category)
#define LOG_CRITICAL(msg, category) Logger::instance()->critical(msg, category)

#endif // LOGGER_H 