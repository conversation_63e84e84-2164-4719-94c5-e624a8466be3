QT       += core gui widgets charts

TARGET = OptimizeApplication
TEMPLATE = app
CONFIG += c++17

# 版本信息
VERSION = 1.0.0
DEFINES += APP_VERSION=\\\"$$VERSION\\\"

# 输出目录
CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/bin/debug
} else {
    DESTDIR = $$PWD/bin/release
}

# 中间文件目录
OBJECTS_DIR = $$DESTDIR/.obj
MOC_DIR = $$DESTDIR/.moc
RCC_DIR = $$DESTDIR/.rcc
UI_DIR = $$DESTDIR/.ui

# 包含路径
INCLUDEPATH += \
    $$PWD/src \
    $$PWD/include \
    $$PWD/3rdparty/SARibbonBar

# 源文件
SOURCES += \
    src/app/main.cpp \
    src/core/application/ApplicationCore.cpp \
    src/core/application/EventBus.cpp \
    src/core/application/PluginManager.cpp \
    src/core/application/ServiceLocator.cpp \
    src/core/config/ConfigManager.cpp \
    src/core/theme/ThemeManager.cpp \
    src/data/models/BaseModel.cpp \
    src/data/models/OptimizationParameters.cpp \
    src/data/parsers/IFileParser.cpp \
    src/ui/mainwindow/MainWindow.cpp \
    src/ui/mainwindow/RibbonManager.cpp \
    src/ui/mainwindow/WorkspaceManager.cpp \
    src/ui/widgets/ParameterWidgetBase.cpp \
    src/utils/logging/LogManager.cpp \
    src/utils/common/ErrorHandler.cpp \
    src/utils/common/MemoryUtils.cpp \
    src/utils/common/PerformanceProfiler.cpp

# 头文件
HEADERS += \
    src/core/application/ApplicationCore.h \
    src/core/application/EventBus.h \
    src/core/application/PluginManager.h \
    src/core/application/ServiceLocator.h \
    src/core/config/ConfigManager.h \
    src/core/theme/ThemeManager.h \
    src/data/models/BaseModel.h \
    src/data/models/OptimizationParameters.h \
    src/data/parsers/IFileParser.h \
    src/data/repositories/FileRepository.h \
    src/data/repositories/IRepository.h \
    src/ui/mainwindow/MainWindow.h \
    src/ui/mainwindow/RibbonManager.h \
    src/ui/mainwindow/WorkspaceManager.h \
    src/ui/widgets/IWidget.h \
    src/ui/widgets/ParameterWidgetBase.h \
    src/utils/logging/LogManager.h \
    src/utils/common/ErrorHandler.h \
    src/utils/common/MemoryUtils.h \
    src/utils/common/PerformanceProfiler.h

# UI文件
FORMS += \
    src/ui/mainwindow/MainWindow.ui

# 资源文件
RESOURCES += \
    resources/icons.qrc

# SARibbonBar库
include(3rdparty/SARibbonBar/SARibbonBar.pri)

# 平台特定配置
win32 {
    DEFINES += WIN32_LEAN_AND_MEAN NOMINMAX
    RC_ICONS = resources/icons/app.ico
    QMAKE_TARGET_COMPANY = "OptimizeApp"
    QMAKE_TARGET_PRODUCT = "OptimizeApplication"
    QMAKE_TARGET_DESCRIPTION = "Optimization Application"
    QMAKE_TARGET_COPYRIGHT = "Copyright (C) 2024"
    RC_CODEPAGE = 0x04b0
    QMAKE_CXXFLAGS += /MP
}

macx {
    ICON = resources/icons/app.icns
    QMAKE_INFO_PLIST = resources/Info.plist
    QMAKE_CXXFLAGS += -stdlib=libc++
    QMAKE_LFLAGS += -stdlib=libc++
}

linux {
    QMAKE_CXXFLAGS += -std=c++17
}

# 调试/发布配置
CONFIG(debug, debug|release) {
    DEFINES += DEBUG_MODE
    TARGET = $${TARGET}_debug
} else {
    DEFINES += QT_NO_DEBUG_OUTPUT
    QMAKE_CXXFLAGS_RELEASE -= -O2
    QMAKE_CXXFLAGS_RELEASE += -O3
}

# 安装配置
target.path = $$[QT_INSTALL_BINS]
INSTALLS += target

# 自定义构建步骤
QMAKE_POST_LINK += $$quote($$QMAKE_COPY_DIR $$shell_path($$PWD/resources) $$shell_path($$DESTDIR/resources))

# 额外依赖
# 如果有额外的库依赖，在这里添加
# LIBS += -L$$PWD/lib -lsomelib

# 插件支持
DEFINES += PLUGIN_SUPPORT
PLUGIN_PATH = $$DESTDIR/plugins
DEFINES += PLUGIN_PATH=\\\"$$PLUGIN_PATH\\\"

# 文档
OTHER_FILES += \
    README.md \
    REFACTOR_PLAN.md
