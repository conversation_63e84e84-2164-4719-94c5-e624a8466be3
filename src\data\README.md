# InputData 数据结构

## 概述

`InputData` 是一个专门用于处理 `input` 目录中各种输入文件的数据结构类。它能够解析和管理热工水力分析软件（如RELAP5）的输入文件，提供了完整的数据访问和验证功能。

## 支持的文件类型

- **NC Welander** - NC Welander问题
- **Discharge Air** - 排放空气分析
- **Discharge Water** - 排放水分析  
- **Critical Flow Air** - 临界流空气
- **Critical Flow Water** - 临界流水
- **1D Wave** - 一维波分析
- **NC 2Phase** - NC两相流分析

## 主要功能

### 1. 通用文件加载和解析
```cpp
InputData inputData;
bool success = inputData.loadFromFile("input/NC_Welander.i");
if (success) {
    qDebug() << "文件加载成功";
}
```

### 2. .i文件专用读写功能

#### 2.1 加载.i文件
```cpp
InputData inputData;
bool success = inputData.loadFromIFile("input/NC_Welander.i");
if (success) {
    qDebug() << "成功加载.i文件";
    qDebug() << "问题标题:" << inputData.problemTitle;
    qDebug() << "组件总数:" << inputData.getTotalComponents();
}
```

#### 2.2 保存.i文件（保持原始格式）
```cpp
// 保存为新文件，保持原始格式和注释
bool success = inputData.saveToIFile("output/copy.i");
```

#### 2.3 导出格式化的.i文件
```cpp
// 导出格式化版本，可选择是否保留注释
bool success = inputData.exportToIFile("output/formatted.i", false); // 不保留注释
```

#### 2.4 自定义格式选项保存
```cpp
InputData::IFileFormatOptions options;
options.preserveComments = true;        // 保留注释
options.addSectionHeaders = true;       // 添加章节标题
options.preserveOriginalSpacing = false; // 不保持原始间距
options.sortComponents = false;          // 不排序组件
options.lineEnding = "\r\n";           // Windows行结束符

bool success = inputData.saveToIFileWithOptions("output/custom.i", options);
```

#### 2.5 生成.i文件内容
```cpp
// 生成完整的.i文件内容字符串
QString content = inputData.generateIFileContent();
qDebug() << content;
```

#### 2.6 验证.i文件格式
```cpp
// 验证文件格式的完整性和正确性
bool isValid = inputData.validateIFileFormat();

// 检查数据一致性
QStringList issues = inputData.checkIFileConsistency();
if (!issues.isEmpty()) {
    for (const QString& issue : issues) {
        qDebug() << "问题:" << issue;
    }
}
```

### 3. 创建新的.i文件

```cpp
InputData newData;

// 设置基本信息
newData.problemTitle = "New Test Problem";
newData.fileType = InputFileType::NC_WELANDER;

// 设置控制卡
newData.controlCard.problemNumber = 100;
newData.controlCard.problemType = "new";
newData.controlCard.analysisType = "transnt";
newData.controlCard.endTime = 1000.0;
newData.controlCard.minTimeStep = 1.0e-6;
newData.controlCard.maxTimeStep = 0.1;

// 设置单位系统
newData.unitSystem.inputUnits = "si";
newData.unitSystem.outputUnits = "si";
newData.unitSystem.workingFluid = "water";

// 创建组件
PipeComponent pipe;
pipe.componentId = "1000000";
pipe.componentName = "main_pipe";
pipe.componentType = "pipe";
pipe.numberOfVolumes = 10;
newData.pipes.append(pipe);

// 保存新文件
bool success = newData.exportToIFile("output/new_problem.i", true);
```

### 4. 数据结构组件

#### 控制卡信息 (ControlCard)
- 问题编号、类型、分析类型
- 时间控制参数（结束时间、时间步长）
- 编辑和重启频率

#### 单位系统 (UnitSystem)
- 输入/输出单位系统
- 工作流体类型
- 比例因子

#### 几何组件
- **管道组件 (PipeComponent)** - 管道系统建模
- **分支组件 (BranchComponent)** - 分支连接
- **单体积组件 (SingleVolumeComponent)** - 单一体积元素
- **连接组件 (JunctionComponent)** - 组件间连接

#### 热结构 (HeatStructure)
- 热传导建模
- 边界条件
- 材料属性

#### 输出控制
- **绘图变量 (PlotVariable)** - 图形输出变量
- **小编辑变量 (MinorEditVariable)** - 详细输出变量

### 5. 查询和统计功能

```cpp
// 获取组件统计
int totalComponents = inputData.getTotalComponents();
int pipeCount = inputData.getComponentCount("pipe");

// 查找特定组件
GeometryComponent* component = inputData.findComponent("1000000");

// 按类型获取组件
QVector<GeometryComponent*> pipes = inputData.getComponentsByType("pipe");

// 获取组件类型列表
QStringList types = inputData.getComponentTypes();
```

### 6. 数据验证

```cpp
// 检查数据有效性
bool isValid = inputData.isValid();

// 获取验证错误
QStringList errors = inputData.validateData();
if (!errors.isEmpty()) {
    for (const QString& error : errors) {
        qDebug() << "错误:" << error;
    }
}
```

### 7. 文件类型检测

```cpp
// 自动检测文件类型
InputFileType type = InputData::detectFileType("input/NC_Welander.i");
QString typeName = InputData::fileTypeToString(type);
```

## .i文件格式化选项详解

### IFileFormatOptions 结构
```cpp
struct IFileFormatOptions {
    bool preserveComments = true;        // 是否保留注释
    bool preserveOriginalSpacing = false; // 是否保持原始间距
    bool addSectionHeaders = true;       // 是否添加章节标题
    bool sortComponents = false;         // 是否按ID排序组件
    int indentSize = 4;                 // 缩进大小
    QString lineEnding = "\n";          // 行结束符
};
```

### 格式化选项说明

1. **preserveComments**: 控制是否在输出文件中保留原始注释
2. **preserveOriginalSpacing**: 保持原始文件的空格和制表符格式
3. **addSectionHeaders**: 在各个章节之间添加分隔线和标题
4. **sortComponents**: 按组件ID对组件进行排序
5. **indentSize**: 设置缩进的空格数量
6. **lineEnding**: 设置行结束符（"\n" for Unix, "\r\n" for Windows）

## .i文件验证功能

### 结构验证
- 检查必需的控制卡是否存在
- 验证时间控制参数的合理性
- 确保单位系统设置正确

### 引用验证
- 验证连接组件引用的组件是否存在
- 检查热结构边界条件引用的组件
- 确保表格引用的正确性

### 一致性检查
- 检查组件ID的唯一性
- 验证组件间连接的完整性
- 检查数据类型的一致性

## 数据结构详细说明

### 几何组件基类 (GeometryComponent)
```cpp
struct GeometryComponent {
    QString componentId;        // 组件ID
    QString componentName;      // 组件名称  
    QString componentType;      // 组件类型
    QMap<QString, QVariant> properties; // 组件属性
};
```

### 管道组件 (PipeComponent)
继承自 GeometryComponent，包含：
- 体积数量和数组
- 长度、高程、粗糙度数组
- 水力直径和角度数组
- 初始条件（压力、温度、干度等）

### 分支组件 (BranchComponent)
继承自 GeometryComponent，包含：
- 连接数量和体积数量
- 几何参数（体积、长度、高程、角度）
- 连接数据（来源/目标组件、面积、损失系数）

### 热结构 (HeatStructure)
```cpp
struct HeatStructure {
    QString heatStructureId;    // 热结构ID
    int numberOfAxialNodes;     // 轴向节点数
    int numberOfRadialNodes;    // 径向节点数
    int geometryType;           // 几何类型
    QVector<double> meshPoints; // 网格点
    QVector<int> materialIds;   // 材料ID
    QVector<BoundaryCondition> boundaryConditions; // 边界条件
};
```

## 使用示例

### 基本.i文件读写
```cpp
#include "InputData.h"

int main() {
    InputData inputData;
    
    // 加载.i文件
    if (inputData.loadFromIFile("input/NC_Welander.i")) {
        // 显示基本信息
        qDebug() << "文件:" << inputData.fileName;
        qDebug() << "类型:" << InputData::fileTypeToString(inputData.fileType);
        qDebug() << "标题:" << inputData.problemTitle;
        
        // 显示组件统计
        qDebug() << "总组件数:" << inputData.getTotalComponents();
        qDebug() << "管道数:" << inputData.getComponentCount("pipe");
        
        // 验证数据
        if (inputData.validateIFileFormat()) {
            qDebug() << ".i文件格式验证通过";
        }
        
        // 保存为新文件
        inputData.saveToIFile("output/copy.i");
        
        // 导出格式化版本
        inputData.exportToIFile("output/formatted.i", false);
    }
    
    return 0;
}
```

### 批量处理.i文件
```cpp
QStringList files = {"NC_Welander.i", "Discharge_Air.i", "1D_Wave.i"};

for (const QString& file : files) {
    InputData data;
    if (data.loadFromIFile("input/" + file)) {
        qDebug() << QString("%1: %2 个组件")
                    .arg(file)
                    .arg(data.getTotalComponents());
        
        // 生成格式化版本
        QString outputFile = QString("output/%1_formatted.i")
                            .arg(QFileInfo(file).baseName());
        data.exportToIFile(outputFile, false);
    }
}
```

## 文件结构

```
src/data/
├── InputData.h              # 头文件 - 数据结构定义
├── InputData.cpp            # 实现文件 - 解析和处理逻辑
├── InputDataExample.cpp     # 通用使用示例
├── IFileExample.cpp         # .i文件专用示例
└── README.md               # 说明文档
```

## 扩展性

该数据结构设计具有良好的扩展性：

1. **新文件类型支持** - 在 `InputFileType` 枚举中添加新类型
2. **新组件类型** - 继承 `GeometryComponent` 创建新的组件类
3. **自定义解析** - 重写或扩展解析方法
4. **数据验证** - 在 `validateData()` 中添加新的验证规则
5. **格式化选项** - 扩展 `IFileFormatOptions` 结构

## 注意事项

1. 文件编码默认为UTF-8
2. 解析过程中会保留原始文件内容（`rawLines`）
3. 注释行（以*开头）会被单独存储
4. 数据验证包括基本的完整性检查
5. 支持组件间连接关系验证
6. .i文件格式严格按照RELAP5标准
7. 支持多种行结束符格式
8. 提供详细的错误报告和验证信息

## 依赖项

- Qt Core (QString, QVector, QMap, QDateTime等)
- Qt正则表达式支持 (QRegularExpression)
- 文件I/O支持 (QFile, QTextStream)
- 目录操作支持 (QDir, QFileInfo)

这个数据结构为OptimizeApplication项目提供了强大的输入文件处理能力，特别是对.i文件的专业级读写支持，满足复杂的热工水力分析数据管理需求。 