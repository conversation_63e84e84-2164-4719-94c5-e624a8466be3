#ifndef RIBBONWIDGET_H
#define RIBBONWIDGET_H

#include <QWidget>
#include <QTabWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QToolButton>
#include <QGroupBox>
#include <QMap>
#include <QString>
#include <QIcon>
#include <QDebug>
#include <QMenu>
#include <QStringList>
#include <QFont>
#include <QColor>

// Enum for button styles
enum RibbonButtonStyle {
    StandardStyle,
    LargeStyle,
    CompactStyle,
    AccentStyle
};

class RibbonWidget : public QWidget
{
    Q_OBJECT

public:
    explicit RibbonWidget(QWidget *parent = nullptr);

    // Tab management
    void addTab(const QString& tabName);
    void addGroup(const QString& tabName, const QString& groupName);
    
    // Button management
    QToolButton* addButton(
        const QString& tabName, 
        const QString& groupName, 
        const QString& buttonText, 
        const QIcon& icon, 
        const QObject* receiver = nullptr, 
        const char* slot = nullptr,
        RibbonButtonStyle style = StandardStyle
    );
    
    // Dropdown button with more options
    QToolButton* addDropdownButton(
        const QString& tabName, 
        const QString& groupName, 
        const QString& buttonText, 
        const QIcon& icon, 
        const QStringList& dropdownItems,
        const QObject* receiver = nullptr, 
        const char* slot = nullptr,
        RibbonButtonStyle style = StandardStyle
    );

    // Button styling
    void applyButtonStyle(QToolButton* button, RibbonButtonStyle style);

    // Advanced styling methods
    void setButtonColor(QToolButton* button, const QColor& color);
    void setButtonFont(QToolButton* button, const QFont& font);
    void setButtonTooltip(QToolButton* button, const QString& tooltip);

private:
    QTabWidget* m_ribbonTabs;
    QMap<QString, QWidget*> m_tabContents;
    QMap<QString, QVBoxLayout*> m_groupLayouts;
};

#endif // RIBBONWIDGET_H 