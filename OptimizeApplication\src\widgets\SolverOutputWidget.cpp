#include "SolverOutputWidget.h"
#include "ui_SolverOutputWidget.h"
#include <QMessageBox>
#include <QHeaderView>
#include <QInputDialog>
#include <QFileDialog>
#include <QStandardPaths>

SolverOutputWidget::SolverOutputWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::SolverOutputWidget)
    , m_selectedVariableRow(-1)
{
    ui->setupUi(this);
    setupOutputVariableTable();
    setupConnections();
    populateFormatComboBoxes();
    updateButtonStates();
    
    // Set default output directory
    QString defaultDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/OptimizeApplication/Output";
    ui->outputDirEdit->setText(defaultDir);
}

SolverOutputWidget::~SolverOutputWidget()
{
    delete ui;
}

void SolverOutputWidget::setupConnections()
{
    // Output directory connections
    connect(ui->browseDirBtn, &QPushButton::clicked,
            this, &SolverOutputWidget::onBrowseOutputDirectory);
    connect(ui->outputDirEdit, &QLineEdit::textChanged,
            this, &SolverOutputWidget::onOutputDirectoryChanged);
    connect(ui->filePatternEdit, &QLineEdit::textChanged,
            this, &SolverOutputWidget::onFileNamePatternChanged);
    connect(ui->formatCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SolverOutputWidget::onOutputFormatChanged);
    
    // Auto-save connections
    connect(ui->autoSaveCheck, &QCheckBox::toggled,
            this, &SolverOutputWidget::onAutoSaveToggled);
    connect(ui->intervalSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SolverOutputWidget::onAutoSaveIntervalChanged);
    
    // Compression connections
    connect(ui->compressionCheck, &QCheckBox::toggled,
            this, &SolverOutputWidget::onCompressionToggled);
    connect(ui->compressionLevelCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SolverOutputWidget::onCompressionLevelChanged);
    
    // Report connections
    connect(ui->reportCheck, &QCheckBox::toggled,
            this, &SolverOutputWidget::onReportToggled);
    connect(ui->reportFormatCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SolverOutputWidget::onReportFormatChanged);
    connect(ui->includeChartsCheck, &QCheckBox::toggled,
            this, &SolverOutputWidget::onIncludeChartsToggled);
    connect(ui->includeStatsCheck, &QCheckBox::toggled,
            this, &SolverOutputWidget::onIncludeStatisticsToggled);
    
    // Output variable connections
    connect(ui->addVariableBtn, &QPushButton::clicked,
            this, &SolverOutputWidget::onAddOutputVariable);
    connect(ui->updateVariableBtn, &QPushButton::clicked,
            this, &SolverOutputWidget::onUpdateOutputVariable);
    connect(ui->deleteVariableBtn, &QPushButton::clicked,
            this, &SolverOutputWidget::onDeleteOutputVariable);
    connect(ui->variablesTable, &QTableWidget::itemSelectionChanged,
            this, &SolverOutputWidget::onOutputVariableTableSelectionChanged);
}

void SolverOutputWidget::setupOutputVariableTable()
{
    // Setup output variable table headers
    ui->variablesTable->setColumnCount(3);
    QStringList headers;
    headers << "Variable Name" << "Type" << "Description";
    ui->variablesTable->setHorizontalHeaderLabels(headers);
    
    // Configure table properties
    ui->variablesTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->variablesTable->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->variablesTable->horizontalHeader()->setStretchLastSection(true);
    ui->variablesTable->setAlternatingRowColors(true);
    
    // Set column widths
    ui->variablesTable->setColumnWidth(0, 150);
    ui->variablesTable->setColumnWidth(1, 100);
}

void SolverOutputWidget::populateFormatComboBoxes()
{
    // Format combo is already populated in UI file
    // Set default selection
    ui->formatCombo->setCurrentIndex(0); // Dakota format
    ui->compressionLevelCombo->setCurrentIndex(1); // Medium compression
    ui->reportFormatCombo->setCurrentIndex(0); // HTML format
}

void SolverOutputWidget::updateButtonStates()
{
    bool hasSelection = m_selectedVariableRow >= 0;
    ui->updateVariableBtn->setEnabled(hasSelection);
    ui->deleteVariableBtn->setEnabled(hasSelection);
    
    // Enable/disable compression level based on compression checkbox
    ui->compressionLevelCombo->setEnabled(ui->compressionCheck->isChecked());
    ui->compressionLevelLabel->setEnabled(ui->compressionCheck->isChecked());
    
    // Enable/disable auto-save interval based on auto-save checkbox
    ui->intervalSpinBox->setEnabled(ui->autoSaveCheck->isChecked());
    ui->intervalLabel->setEnabled(ui->autoSaveCheck->isChecked());
    
    // Enable/disable report options based on report checkbox
    bool reportEnabled = ui->reportCheck->isChecked();
    ui->reportFormatCombo->setEnabled(reportEnabled);
    ui->reportFormatLabel->setEnabled(reportEnabled);
    ui->includeChartsCheck->setEnabled(reportEnabled);
    ui->includeStatsCheck->setEnabled(reportEnabled);
}

// Output directory and file settings
QString SolverOutputWidget::getOutputDirectory() const
{
    return ui->outputDirEdit->text();
}

QString SolverOutputWidget::getFileNamePattern() const
{
    return ui->filePatternEdit->text();
}

QString SolverOutputWidget::getOutputFormat() const
{
    QString format = ui->formatCombo->currentText();
    if (format.contains("Dakota")) return "dat";
    else if (format.contains("CSV")) return "csv";
    else if (format.contains("JSON")) return "json";
    else if (format.contains("XML")) return "xml";
    return "dat";
}

void SolverOutputWidget::setOutputDirectory(const QString &directory)
{
    ui->outputDirEdit->setText(directory);
}

void SolverOutputWidget::setFileNamePattern(const QString &pattern)
{
    ui->filePatternEdit->setText(pattern);
}

void SolverOutputWidget::setOutputFormat(const QString &format)
{
    for (int i = 0; i < ui->formatCombo->count(); ++i) {
        QString itemText = ui->formatCombo->itemText(i);
        if ((format == "dat" && itemText.contains("Dakota")) ||
            (format == "csv" && itemText.contains("CSV")) ||
            (format == "json" && itemText.contains("JSON")) ||
            (format == "xml" && itemText.contains("XML"))) {
            ui->formatCombo->setCurrentIndex(i);
            break;
        }
    }
}

// Auto-save settings
bool SolverOutputWidget::isAutoSaveEnabled() const
{
    return ui->autoSaveCheck->isChecked();
}

int SolverOutputWidget::getAutoSaveInterval() const
{
    return ui->intervalSpinBox->value();
}

void SolverOutputWidget::setAutoSaveEnabled(bool enabled)
{
    ui->autoSaveCheck->setChecked(enabled);
}

void SolverOutputWidget::setAutoSaveInterval(int interval)
{
    ui->intervalSpinBox->setValue(interval);
}

// Compression settings
bool SolverOutputWidget::isCompressionEnabled() const
{
    return ui->compressionCheck->isChecked();
}

QString SolverOutputWidget::getCompressionLevel() const
{
    return ui->compressionLevelCombo->currentText().toLower();
}

void SolverOutputWidget::setCompressionEnabled(bool enabled)
{
    ui->compressionCheck->setChecked(enabled);
}

void SolverOutputWidget::setCompressionLevel(const QString &level)
{
    QString levelCap = level;
    levelCap[0] = levelCap[0].toUpper();
    int index = ui->compressionLevelCombo->findText(levelCap);
    if (index >= 0) {
        ui->compressionLevelCombo->setCurrentIndex(index);
    }
}

// Report settings
bool SolverOutputWidget::isReportEnabled() const
{
    return ui->reportCheck->isChecked();
}

QString SolverOutputWidget::getReportFormat() const
{
    return ui->reportFormatCombo->currentText().toLower();
}

bool SolverOutputWidget::includeCharts() const
{
    return ui->includeChartsCheck->isChecked();
}

bool SolverOutputWidget::includeStatistics() const
{
    return ui->includeStatsCheck->isChecked();
}

void SolverOutputWidget::setReportEnabled(bool enabled)
{
    ui->reportCheck->setChecked(enabled);
}

void SolverOutputWidget::setReportFormat(const QString &format)
{
    QString formatCap = format;
    formatCap[0] = formatCap[0].toUpper();
    int index = ui->reportFormatCombo->findText(formatCap);
    if (index >= 0) {
        ui->reportFormatCombo->setCurrentIndex(index);
    }
}

void SolverOutputWidget::setIncludeCharts(bool include)
{
    ui->includeChartsCheck->setChecked(include);
}

void SolverOutputWidget::setIncludeStatistics(bool include)
{
    ui->includeStatsCheck->setChecked(include);
}

// Output variables management
void SolverOutputWidget::addOutputVariable(const QString &name, const QString &type, const QString &description)
{
    int row = ui->variablesTable->rowCount();
    ui->variablesTable->insertRow(row);
    
    ui->variablesTable->setItem(row, 0, new QTableWidgetItem(name));
    ui->variablesTable->setItem(row, 1, new QTableWidgetItem(type));
    ui->variablesTable->setItem(row, 2, new QTableWidgetItem(description));
    
    emit outputVariableAdded(name);
    emit parametersChanged();
}

void SolverOutputWidget::updateOutputVariable(int row, const QString &name, const QString &type, const QString &description)
{
    if (row >= 0 && row < ui->variablesTable->rowCount()) {
        ui->variablesTable->setItem(row, 0, new QTableWidgetItem(name));
        ui->variablesTable->setItem(row, 1, new QTableWidgetItem(type));
        ui->variablesTable->setItem(row, 2, new QTableWidgetItem(description));
        emit parametersChanged();
    }
}

void SolverOutputWidget::deleteOutputVariable(int row)
{
    if (row >= 0 && row < ui->variablesTable->rowCount()) {
        QString name = ui->variablesTable->item(row, 0)->text();
        ui->variablesTable->removeRow(row);
        emit outputVariableRemoved(name);
        emit parametersChanged();
    }
}

void SolverOutputWidget::clearOutputVariables()
{
    ui->variablesTable->setRowCount(0);
    emit parametersChanged();
}

// Data access methods
QStringList SolverOutputWidget::getOutputVariableNames() const
{
    QStringList names;
    for (int i = 0; i < ui->variablesTable->rowCount(); ++i) {
        if (ui->variablesTable->item(i, 0)) {
            names << ui->variablesTable->item(i, 0)->text();
        }
    }
    return names;
}

int SolverOutputWidget::getOutputVariableCount() const
{
    return ui->variablesTable->rowCount();
}

// Slots implementation
void SolverOutputWidget::onBrowseOutputDirectory()
{
    QString currentDir = ui->outputDirEdit->text();
    if (currentDir.isEmpty()) {
        currentDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    }
    
    QString directory = QFileDialog::getExistingDirectory(this, 
        tr("Select Output Directory"), currentDir);
    
    if (!directory.isEmpty()) {
        ui->outputDirEdit->setText(directory);
        emit parametersChanged();
    }
}

void SolverOutputWidget::onOutputDirectoryChanged()
{
    emit parametersChanged();
}

void SolverOutputWidget::onFileNamePatternChanged()
{
    emit parametersChanged();
}

void SolverOutputWidget::onOutputFormatChanged()
{
    emit parametersChanged();
}

void SolverOutputWidget::onAutoSaveToggled(bool enabled)
{
    updateButtonStates();
    emit parametersChanged();
}

void SolverOutputWidget::onAutoSaveIntervalChanged(int interval)
{
    Q_UNUSED(interval)
    emit parametersChanged();
}

void SolverOutputWidget::onCompressionToggled(bool enabled)
{
    updateButtonStates();
    emit parametersChanged();
}

void SolverOutputWidget::onCompressionLevelChanged()
{
    emit parametersChanged();
}

void SolverOutputWidget::onReportToggled(bool enabled)
{
    updateButtonStates();
    emit parametersChanged();
}

void SolverOutputWidget::onReportFormatChanged()
{
    emit parametersChanged();
}

void SolverOutputWidget::onIncludeChartsToggled(bool include)
{
    Q_UNUSED(include)
    emit parametersChanged();
}

void SolverOutputWidget::onIncludeStatisticsToggled(bool include)
{
    Q_UNUSED(include)
    emit parametersChanged();
}

void SolverOutputWidget::onAddOutputVariable()
{
    bool ok;
    QString name = QInputDialog::getText(this, tr("Add Output Variable"),
                                       tr("Variable name:"), QLineEdit::Normal,
                                       QString(), &ok);
    if (ok && !name.isEmpty()) {
        QString type = QInputDialog::getItem(this, tr("Variable Type"),
                                           tr("Select variable type:"),
                                           QStringList() << "Scalar" << "Vector" << "Matrix" << "Function",
                                           0, false, &ok);
        if (ok) {
            QString description = QInputDialog::getText(this, tr("Variable Description"),
                                                      tr("Description:"), QLineEdit::Normal,
                                                      QString(), &ok);
            if (ok) {
                addOutputVariable(name, type, description);
            }
        }
    }
}

void SolverOutputWidget::onUpdateOutputVariable()
{
    if (m_selectedVariableRow >= 0) {
        QTableWidgetItem* nameItem = ui->variablesTable->item(m_selectedVariableRow, 0);
        QTableWidgetItem* typeItem = ui->variablesTable->item(m_selectedVariableRow, 1);
        QTableWidgetItem* descItem = ui->variablesTable->item(m_selectedVariableRow, 2);
        
        if (nameItem && typeItem && descItem) {
            bool ok;
            QString name = QInputDialog::getText(this, tr("Update Output Variable"),
                                               tr("Variable name:"), QLineEdit::Normal,
                                               nameItem->text(), &ok);
            if (ok && !name.isEmpty()) {
                QString type = QInputDialog::getItem(this, tr("Variable Type"),
                                                   tr("Select variable type:"),
                                                   QStringList() << "Scalar" << "Vector" << "Matrix" << "Function",
                                                   0, false, &ok);
                if (ok) {
                    QString description = QInputDialog::getText(this, tr("Variable Description"),
                                                              tr("Description:"), QLineEdit::Normal,
                                                              descItem->text(), &ok);
                    if (ok) {
                        updateOutputVariable(m_selectedVariableRow, name, type, description);
                    }
                }
            }
        }
    }
}

void SolverOutputWidget::onDeleteOutputVariable()
{
    if (m_selectedVariableRow >= 0) {
        QTableWidgetItem* nameItem = ui->variablesTable->item(m_selectedVariableRow, 0);
        if (nameItem) {
            int ret = QMessageBox::question(this, tr("Delete Output Variable"),
                                          tr("Are you sure you want to delete variable '%1'?")
                                          .arg(nameItem->text()),
                                          QMessageBox::Yes | QMessageBox::No);
            if (ret == QMessageBox::Yes) {
                deleteOutputVariable(m_selectedVariableRow);
                m_selectedVariableRow = -1;
                updateButtonStates();
            }
        }
    }
}

void SolverOutputWidget::onOutputVariableTableSelectionChanged()
{
    QList<QTableWidgetItem*> selectedItems = ui->variablesTable->selectedItems();
    if (!selectedItems.isEmpty()) {
        m_selectedVariableRow = selectedItems.first()->row();
    } else {
        m_selectedVariableRow = -1;
    }
    updateButtonStates();
} 