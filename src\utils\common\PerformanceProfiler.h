#ifndef PERFORMANCEPROFILER_H
#define PERFORMANCEPROFILER_H

#include <QString>
#include <QElapsedTimer>
#include <QMap>
#include <QMutex>
#include <chrono>

/**
 * @brief 性能分析器类
 * 
 * 提供函数执行时间测量和性能分析功能。
 */
class PerformanceProfiler
{
public:
    /**
     * @brief 性能统计信息
     */
    struct ProfileInfo {
        QString name;           // 函数名称
        qint64 totalTime;       // 总执行时间（微秒）
        qint64 minTime;         // 最小执行时间（微秒）
        qint64 maxTime;         // 最大执行时间（微秒）
        qint64 avgTime;         // 平均执行时间（微秒）
        int callCount;          // 调用次数
        
        ProfileInfo() : totalTime(0), minTime(LLONG_MAX), maxTime(0), avgTime(0), callCount(0) {}
    };

    /**
     * @brief 获取单例实例
     * @return 单例实例引用
     */
    static PerformanceProfiler& instance();

    /**
     * @brief 开始性能测量
     * @param name 函数名称
     */
    void startProfile(const QString& name);

    /**
     * @brief 结束性能测量
     * @param name 函数名称
     */
    void endProfile(const QString& name);

    /**
     * @brief 获取性能统计信息
     * @param name 函数名称
     * @return 性能统计信息
     */
    ProfileInfo getProfileInfo(const QString& name) const;

    /**
     * @brief 获取所有性能统计信息
     * @return 性能统计信息映射
     */
    QMap<QString, ProfileInfo> getAllProfileInfo() const;

    /**
     * @brief 清空性能统计信息
     */
    void clearProfiles();

    /**
     * @brief 打印性能报告
     */
    void printReport() const;

    /**
     * @brief 设置是否启用性能分析
     * @param enabled 是否启用
     */
    void setEnabled(bool enabled);

    /**
     * @brief 检查是否启用性能分析
     * @return 是否启用
     */
    bool isEnabled() const;

private:
    /**
     * @brief 构造函数
     */
    PerformanceProfiler();

    /**
     * @brief 析构函数
     */
    ~PerformanceProfiler();

    // 禁止拷贝和赋值
    PerformanceProfiler(const PerformanceProfiler&) = delete;
    PerformanceProfiler& operator=(const PerformanceProfiler&) = delete;

    mutable QMutex m_mutex;
    QMap<QString, ProfileInfo> m_profiles;
    QMap<QString, std::chrono::high_resolution_clock::time_point> m_startTimes;
    bool m_enabled;
};

/**
 * @brief RAII性能测量类
 * 
 * 使用RAII模式自动测量代码块的执行时间。
 */
class ScopedProfiler
{
public:
    /**
     * @brief 构造函数
     * @param name 函数名称
     */
    explicit ScopedProfiler(const QString& name);

    /**
     * @brief 析构函数
     */
    ~ScopedProfiler();

private:
    QString m_name;
};

// 便利宏定义
#ifdef DEBUG_MODE
#define PROFILE_FUNCTION() \
    ScopedProfiler __profiler(__FUNCTION__)

#define PROFILE_SCOPE(name) \
    ScopedProfiler __profiler(name)

#define START_PROFILE(name) \
    PerformanceProfiler::instance().startProfile(name)

#define END_PROFILE(name) \
    PerformanceProfiler::instance().endProfile(name)

#define PRINT_PROFILE_REPORT() \
    PerformanceProfiler::instance().printReport()
#else
#define PROFILE_FUNCTION()
#define PROFILE_SCOPE(name)
#define START_PROFILE(name)
#define END_PROFILE(name)
#define PRINT_PROFILE_REPORT()
#endif

#endif // PERFORMANCEPROFILER_H
