#ifndef BASEMODEL_H
#define BASEMODEL_H

#include <QVariant>
#include <QString>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonDocument>

/**
 * @brief 基础数据模型类
 * 
 * 所有数据模型都应该继承此基类，以提供统一的数据序列化和验证功能。
 */
class BaseModel
{
public:
    /**
     * @brief 虚析构函数
     */
    virtual ~BaseModel() = default;

    /**
     * @brief 转换为QVariant
     * @return QVariant表示
     */
    virtual QVariant toVariant() const = 0;

    /**
     * @brief 从QVariant加载
     * @param variant QVariant数据
     */
    virtual void fromVariant(const QVariant& variant) = 0;

    /**
     * @brief 验证模型数据
     * @return 数据是否有效
     */
    virtual bool isValid() const = 0;

    /**
     * @brief 转换为JSON对象
     * @return JSON对象
     */
    virtual QJsonObject toJson() const;

    /**
     * @brief 从JSON对象加载
     * @param json JSON对象
     */
    virtual void fromJson(const QJsonObject& json);

    /**
     * @brief 转换为JSON字符串
     * @return JSON字符串
     */
    QString toJsonString() const;

    /**
     * @brief 从JSON字符串加载
     * @param jsonString JSON字符串
     * @return 加载是否成功
     */
    bool fromJsonString(const QString& jsonString);

    /**
     * @brief 获取模型ID
     * @return 模型ID
     */
    QString id() const;

    /**
     * @brief 设置模型ID
     * @param id 模型ID
     */
    void setId(const QString& id);

    /**
     * @brief 获取创建时间
     * @return 创建时间
     */
    QDateTime createdAt() const;

    /**
     * @brief 设置创建时间
     * @param dateTime 创建时间
     */
    void setCreatedAt(const QDateTime& dateTime);

    /**
     * @brief 获取更新时间
     * @return 更新时间
     */
    QDateTime updatedAt() const;

    /**
     * @brief 设置更新时间
     * @param dateTime 更新时间
     */
    void setUpdatedAt(const QDateTime& dateTime);

    /**
     * @brief 获取模型名称
     * @return 模型名称
     */
    virtual QString modelName() const = 0;

    /**
     * @brief 获取模型版本
     * @return 模型版本
     */
    virtual QString modelVersion() const { return "1.0.0"; }

    /**
     * @brief 克隆模型
     * @return 克隆的模型指针
     */
    virtual BaseModel* clone() const = 0;

    /**
     * @brief 比较两个模型是否相等
     * @param other 另一个模型
     * @return 是否相等
     */
    virtual bool equals(const BaseModel& other) const;

    /**
     * @brief 获取模型哈希值
     * @return 哈希值
     */
    virtual uint hash() const;

protected:
    /**
     * @brief 构造函数
     */
    BaseModel();

    /**
     * @brief 拷贝构造函数
     * @param other 另一个模型
     */
    BaseModel(const BaseModel& other);

    /**
     * @brief 赋值操作符
     * @param other 另一个模型
     * @return 当前模型引用
     */
    BaseModel& operator=(const BaseModel& other);

private:
    QString m_id;
    QDateTime m_createdAt;
    QDateTime m_updatedAt;
};

#endif // BASEMODEL_H
