<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
<Qt_DEFINES_>_WINDOWS;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;QT_CHARTS_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB</Qt_DEFINES_>
<Qt_INCLUDEPATH_>D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCharts;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\mkspecs\win32-msvc</Qt_INCLUDEPATH_>
<Qt_STDCPP_></Qt_STDCPP_>
<Qt_RUNTIME_>MultiThreadedDebugDLL</Qt_RUNTIME_>
<Qt_CL_OPTIONS_>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus</Qt_CL_OPTIONS_>
<Qt_LIBS_>D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Chartsd.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Widgetsd.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Guid.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\Qt5Cored.lib;D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\qtmaind.lib;shell32.lib</Qt_LIBS_>
<Qt_LINK_OPTIONS_>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"</Qt_LINK_OPTIONS_>
<QMake_QT_SYSROOT_></QMake_QT_SYSROOT_>
<QMake_QT_INSTALL_PREFIX_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64</QMake_QT_INSTALL_PREFIX_>
<QMake_QT_INSTALL_ARCHDATA_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64</QMake_QT_INSTALL_ARCHDATA_>
<QMake_QT_INSTALL_DATA_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64</QMake_QT_INSTALL_DATA_>
<QMake_QT_INSTALL_DOCS_>D:/Qt/Qt5.14.2/Docs/Qt-5.14.2</QMake_QT_INSTALL_DOCS_>
<QMake_QT_INSTALL_HEADERS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include</QMake_QT_INSTALL_HEADERS_>
<QMake_QT_INSTALL_LIBS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib</QMake_QT_INSTALL_LIBS_>
<QMake_QT_INSTALL_LIBEXECS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin</QMake_QT_INSTALL_LIBEXECS_>
<QMake_QT_INSTALL_BINS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin</QMake_QT_INSTALL_BINS_>
<QMake_QT_INSTALL_TESTS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/tests</QMake_QT_INSTALL_TESTS_>
<QMake_QT_INSTALL_PLUGINS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/plugins</QMake_QT_INSTALL_PLUGINS_>
<QMake_QT_INSTALL_IMPORTS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/imports</QMake_QT_INSTALL_IMPORTS_>
<QMake_QT_INSTALL_QML_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/qml</QMake_QT_INSTALL_QML_>
<QMake_QT_INSTALL_TRANSLATIONS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/translations</QMake_QT_INSTALL_TRANSLATIONS_>
<QMake_QT_INSTALL_CONFIGURATION_></QMake_QT_INSTALL_CONFIGURATION_>
<QMake_QT_INSTALL_EXAMPLES_>D:/Qt/Qt5.14.2/Examples/Qt-5.14.2</QMake_QT_INSTALL_EXAMPLES_>
<QMake_QT_INSTALL_DEMOS_>D:/Qt/Qt5.14.2/Examples/Qt-5.14.2</QMake_QT_INSTALL_DEMOS_>
<QMake_QT_HOST_PREFIX_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64</QMake_QT_HOST_PREFIX_>
<QMake_QT_HOST_DATA_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64</QMake_QT_HOST_DATA_>
<QMake_QT_HOST_BINS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin</QMake_QT_HOST_BINS_>
<QMake_QT_HOST_LIBS_>D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib</QMake_QT_HOST_LIBS_>
<QMake_QMAKE_SPEC_>win32-msvc</QMake_QMAKE_SPEC_>
<QMake_QMAKE_XSPEC_>win32-msvc</QMake_QMAKE_XSPEC_>
<QMake_QMAKE_VERSION_>3.1</QMake_QMAKE_VERSION_>
<QMake_QT_VERSION_>5.14.2</QMake_QT_VERSION_>
<QtBkup_QtHash>jVFBagMxDPyKXpClpTnmoJVUR4nXdmWZEErp/39R77JloZf6YATWzEgz+lSW5OrPy4efTy9vp1egbAKhKTyUg3gFuqL1MmvaHsu7JqmXqVi+CfmkLsv2qa45BcutTBQpL0WjdJR0IEmt2Q5UBU0UG0tBv45KIfM2AuNOZrW+QDaVCtWZShmViphCwyDVMTEag7UewyKj/B0edTa0J1D8zmV1PxzL4WUnQtf6h91t36eD2MOT1M9Hq/21N7bDH5Xf8SxzC18/</QtBkup_QtHash>
    <QtVersion>5.14.2</QtVersion>
    <QtVersionMajor>5</QtVersionMajor>
    <QtVersionMinor>14</QtVersionMinor>
    <QtVersionPatch>2</QtVersionPatch>
  </PropertyGroup>
</Project>
