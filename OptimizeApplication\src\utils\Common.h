#ifndef COMMON_H
#define COMMON_H

#include <QString>
#include <QDebug>
#include <QApplication>
#include <QMessageBox>
#include <QDir>
#include <QStandardPaths>

// Application constants
namespace AppConstants {
    extern const QString APP_NAME;
    extern const QString APP_VERSION;
    extern const QString ORGANIZATION_NAME;
    extern const QString ORGANIZATION_DOMAIN;
    
    // Configuration file paths
    extern const QString CONFIG_DIR;
    extern const QString LOG_DIR;
    extern const QString DATA_DIR;
}

// Node type enumeration
enum class NodeType {
    Root,
    Optimize,
    Sensitivity,
    UQ,
    Input,
    Output,
    Solver,
    Pipe,
    Tube,
    Unknown
};

// Log level enumeration
enum class LogLevel {
    Debug,
    Info,
    Warning,
    Error,
    Critical
};

// Utility macros
#define SAFE_DELETE(ptr) \
    do { \
        if (ptr) { \
            delete ptr; \
            ptr = nullptr; \
        } \
    } while(0)

#define SAFE_DELETE_ARRAY(ptr) \
    do { \
        if (ptr) { \
            delete[] ptr; \
            ptr = nullptr; \
        } \
    } while(0)

// Debug macros
#ifdef DEBUG_MODE
    #define DEBUG_LOG(msg) qDebug() << "[DEBUG]" << msg
    #define INFO_LOG(msg) qDebug() << "[INFO]" << msg
    #define WARNING_LOG(msg) qWarning() << "[WARNING]" << msg
    #define ERROR_LOG(msg) qCritical() << "[ERROR]" << msg
#else
    #define DEBUG_LOG(msg)
    #define INFO_LOG(msg)
    #define WARNING_LOG(msg)
    #define ERROR_LOG(msg)
#endif

// Utility function declarations
namespace Utils {
    QString getAppDataPath();
    QString getConfigPath();
    QString getLogPath();
    bool ensureDirectoryExists(const QString& path);
    void showErrorMessage(const QString& title, const QString& message);
    void showInfoMessage(const QString& title, const QString& message);
}

#endif // COMMON_H 
