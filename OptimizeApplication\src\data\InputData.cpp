#include "InputData.h"
#include <QFile>
#include <QTextStream>
#include <QFileInfo>
#include <QRegularExpression>
#include <QDebug>

InputData::InputData()
    : fileType(InputFileType::UNKNOWN)
    , m_isValid(false)
{
    clear();
}

InputData::~InputData()
{
}

void InputData::clear()
{
    fileName.clear();
    filePath.clear();
    fileType = InputFileType::UNKNOWN;
    lastModified = QDateTime();
    problemTitle.clear();
    
    // Clear control information
    controlCard = ControlCard();
    unitSystem = UnitSystem();
    
    // Clear output control
    plotVariables.clear();
    minorEditVars.clear();
    
    // Clear geometry components
    pipes.clear();
    branches.clear();
    volumes.clear();
    junctions.clear();
    
    // Clear heat structures
    heatStructures.clear();
    materials.clear();
    
    // Clear table data
    tables.clear();
    
    // Clear comments and raw data
    comments.clear();
    rawLines.clear();
    
    m_isValid = false;
    m_errors.clear();
}

bool InputData::loadFromFile(const QString& filePath)
{
    clear();
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        m_errors << QString("Cannot open file: %1").arg(filePath);
        return false;
    }
    
    // Set basic information
    QFileInfo fileInfo(filePath);
    this->filePath = filePath;
    this->fileName = fileInfo.fileName();
    this->lastModified = fileInfo.lastModified();
    this->fileType = detectFileType(filePath);
    
    // Read file content
    QTextStream in(&file);
    in.setCodec("UTF-8");
    
    QStringList lines;
    while (!in.atEnd()) {
        QString line = in.readLine();
        lines.append(line);
        rawLines.append(line);
    }
    file.close();
    
    // Parse file content
    bool success = true;
    
    // Extract problem title
    for (const QString& line : lines) {
        if (line.startsWith("=") && !line.startsWith("=*")) {
            problemTitle = line.mid(1).trimmed();
            break;
        }
    }
    
    // Parse sections
    success &= parseControlCard(lines);
    success &= parseUnitSystem(lines);
    success &= parsePlotVariables(lines);
    success &= parseMinorEditVariables(lines);
    success &= parseGeometryComponents(lines);
    success &= parseHeatStructures(lines);
    success &= parseMaterials(lines);
    success &= parseTables(lines);
    
    parseComments(lines);
    
    m_isValid = success && m_errors.isEmpty();
    return m_isValid;
}

bool InputData::saveToFile(const QString& filePath) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    
    // Write original lines (preserve original format)
    for (const QString& line : rawLines) {
        out << line << "\n";
    }
    
    file.close();
    return true;
}

bool InputData::isValid() const
{
    return m_isValid;
}

GeometryComponent* InputData::findComponent(const QString& componentId)
{
    // Search in pipes
    for (auto& pipe : pipes) {
        if (pipe.componentId == componentId) {
            return &pipe;
        }
    }
    
    // Search in branches
    for (auto& branch : branches) {
        if (branch.componentId == componentId) {
            return &branch;
        }
    }
    
    // Search in volumes
    for (auto& volume : volumes) {
        if (volume.componentId == componentId) {
            return &volume;
        }
    }
    
    // Search in junctions
    for (auto& junction : junctions) {
        if (junction.componentId == componentId) {
            return &junction;
        }
    }
    
    return nullptr;
}

const GeometryComponent* InputData::findComponent(const QString& componentId) const
{
    // Search in pipes
    for (const auto& pipe : pipes) {
        if (pipe.componentId == componentId) {
            return &pipe;
        }
    }
    
    // Search in branches
    for (const auto& branch : branches) {
        if (branch.componentId == componentId) {
            return &branch;
        }
    }
    
    // Search in volumes
    for (const auto& volume : volumes) {
        if (volume.componentId == componentId) {
            return &volume;
        }
    }
    
    // Search in junctions
    for (const auto& junction : junctions) {
        if (junction.componentId == componentId) {
            return &junction;
        }
    }
    
    return nullptr;
}

QVector<GeometryComponent*> InputData::getComponentsByType(const QString& type)
{
    QVector<GeometryComponent*> result;
    
    if (type == "pipe") {
        for (auto& pipe : pipes) {
            result.append(&pipe);
        }
    } else if (type == "branch") {
        for (auto& branch : branches) {
            result.append(&branch);
        }
    } else if (type == "snglvol") {
        for (auto& volume : volumes) {
            result.append(&volume);
        }
    } else if (type == "sngljun") {
        for (auto& junction : junctions) {
            result.append(&junction);
        }
    }
    
    return result;
}

TableData* InputData::findTable(const QString& tableId)
{
    for (auto& table : tables) {
        if (table.tableId == tableId) {
            return &table;
        }
    }
    return nullptr;
}

int InputData::getTotalComponents() const
{
    return pipes.size() + branches.size() + volumes.size() + junctions.size();
}

int InputData::getComponentCount(const QString& type) const
{
    if (type == "pipe") return pipes.size();
    if (type == "branch") return branches.size();
    if (type == "snglvol") return volumes.size();
    if (type == "sngljun") return junctions.size();
    return 0;
}

QStringList InputData::getComponentTypes() const
{
    QStringList types;
    if (!pipes.isEmpty()) types << "pipe";
    if (!branches.isEmpty()) types << "branch";
    if (!volumes.isEmpty()) types << "snglvol";
    if (!junctions.isEmpty()) types << "sngljun";
    return types;
}

QStringList InputData::validateData() const
{
    QStringList errors;
    
    // Validate control card
    if (controlCard.endTime <= 0) {
        errors << "End time must be greater than 0";
    }
    if (controlCard.minTimeStep <= 0) {
        errors << "Minimum time step must be greater than 0";
    }
    if (controlCard.maxTimeStep <= controlCard.minTimeStep) {
        errors << "Maximum time step must be greater than minimum time step";
    }
    
    // Validate component connections
    for (const auto& junction : junctions) {
        if (findComponent(junction.fromComponent) == nullptr) {
            errors << QString("Junction component %1 from component %2 does not exist")
                      .arg(junction.componentId, junction.fromComponent);
        }
        if (findComponent(junction.toComponent) == nullptr) {
            errors << QString("Junction component %1 to component %2 does not exist")
                      .arg(junction.componentId, junction.toComponent);
        }
    }
    
    return errors;
}

bool InputData::hasErrors() const
{
    return !m_errors.isEmpty() || !validateData().isEmpty();
}

InputFileType InputData::detectFileType(const QString& filePath)
{
    QFileInfo fileInfo(filePath);
    QString baseName = fileInfo.baseName().toLower();
    
    if (baseName.contains("welander")) {
        return InputFileType::NC_WELANDER;
    } else if (baseName.contains("discharge") && baseName.contains("air")) {
        return InputFileType::DISCHARGE_AIR;
    } else if (baseName.contains("discharge") && baseName.contains("water")) {
        return InputFileType::DISCHARGE_WATER;
    } else if (baseName.contains("critical") && baseName.contains("air")) {
        return InputFileType::CRITICAL_FLOW_AIR;
    } else if (baseName.contains("critical") && baseName.contains("water")) {
        return InputFileType::CRITICAL_FLOW_WATER;
    } else if (baseName.contains("wave") || baseName.contains("1d")) {
        return InputFileType::WAVE_1D;
    } else if (baseName.contains("2phase")) {
        return InputFileType::NC_2PHASE;
    }
    
    return InputFileType::UNKNOWN;
}

QString InputData::fileTypeToString(InputFileType type)
{
    switch (type) {
    case InputFileType::NC_WELANDER: return "NC Welander";
    case InputFileType::DISCHARGE_AIR: return "Discharge Air";
    case InputFileType::DISCHARGE_WATER: return "Discharge Water";
    case InputFileType::CRITICAL_FLOW_AIR: return "Critical Flow Air";
    case InputFileType::CRITICAL_FLOW_WATER: return "Critical Flow Water";
    case InputFileType::WAVE_1D: return "1D Wave";
    case InputFileType::NC_2PHASE: return "NC 2Phase";
    case InputFileType::UNKNOWN: return "Unknown";
    }
    return "Unknown";
}

bool InputData::parseControlCard(const QStringList& lines)
{
    QRegularExpression controlRegex(R"(^(\d+)\s+(\w+)\s+(\w+))");
    QRegularExpression timeRegex(R"(^(\d+)\s+([\d.e+-]+)\s+([\d.e+-]+)\s+([\d.e+-]+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+))");
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QRegularExpressionMatch controlMatch = controlRegex.match(trimmedLine);
        if (controlMatch.hasMatch()) {
            controlCard.problemNumber = controlMatch.captured(1).toInt();
            controlCard.problemType = controlMatch.captured(2);
            controlCard.analysisType = controlMatch.captured(3);
            continue;
        }
        
        QRegularExpressionMatch timeMatch = timeRegex.match(trimmedLine);
        if (timeMatch.hasMatch()) {
            controlCard.endTime = timeMatch.captured(2).toDouble();
            controlCard.minTimeStep = timeMatch.captured(3).toDouble();
            controlCard.maxTimeStep = timeMatch.captured(4).toDouble();
            controlCard.controlOption = timeMatch.captured(5).toInt();
            controlCard.minorEdit = timeMatch.captured(6).toInt();
            controlCard.majorEdit = timeMatch.captured(7).toInt();
            controlCard.restartFreq = timeMatch.captured(8).toInt();
            return true;
        }
    }
    
    return true; // Not all files have complete control cards
}

bool InputData::parseUnitSystem(const QStringList& lines)
{
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QStringList tokens = tokenizeLine(trimmedLine);
        if (tokens.size() >= 3 && tokens[0].toInt() == 102) {
            unitSystem.inputUnits = tokens[1];
            unitSystem.outputUnits = tokens[2];
        } else if (tokens.size() >= 2 && tokens[0].toInt() == 110) {
            unitSystem.workingFluid = tokens[1];
        } else if (tokens.size() >= 2 && tokens[0].toInt() == 115) {
            unitSystem.scaleFactor = tokens[1].toDouble();
        }
    }
    
    return true;
}

bool InputData::parsePlotVariables(const QStringList& lines)
{
    QRegularExpression plotRegex(R"(^(\d+)\s+(\w+)\s+(\d+)\s+(\d+))");
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QRegularExpressionMatch match = plotRegex.match(trimmedLine);
        if (match.hasMatch() && match.captured(1).startsWith("203")) {
            PlotVariable var;
            var.variableId = match.captured(1).toInt();
            var.variableType = match.captured(2);
            var.componentId = match.captured(3);
            var.plotFlag = match.captured(4).toInt();
            plotVariables.append(var);
        }
    }
    
    return true;
}

bool InputData::parseMinorEditVariables(const QStringList& lines)
{
    QRegularExpression editRegex(R"(^(\d+)\s+(\w+)\s+(\d+))");
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QRegularExpressionMatch match = editRegex.match(trimmedLine);
        if (match.hasMatch() && match.captured(1).toInt() >= 301 && match.captured(1).toInt() < 400) {
            MinorEditVariable var;
            var.editId = match.captured(1).toInt();
            var.variableType = match.captured(2);
            var.componentId = match.captured(3);
            minorEditVars.append(var);
        }
    }
    
    return true;
}

bool InputData::parseGeometryComponents(const QStringList& lines)
{
    QString currentComponentId;
    QString currentComponentType;
    
    for (int i = 0; i < lines.size(); ++i) {
        QString trimmedLine = lines[i].trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QStringList tokens = tokenizeLine(trimmedLine);
        if (tokens.isEmpty()) continue;
        
        QString firstToken = tokens[0];
        
        // Check if this is a component definition line
        if (firstToken.length() == 7 && firstToken.endsWith("000")) {
            currentComponentId = firstToken;
            if (tokens.size() >= 3) {
                currentComponentType = tokens[2].toLower();
                
                if (currentComponentType == "pipe") {
                    PipeComponent pipe;
                    pipe.componentId = currentComponentId;
                    pipe.componentName = tokens.size() > 1 ? tokens[1] : "";
                    pipe.componentType = currentComponentType;
                    pipes.append(pipe);
                } else if (currentComponentType == "branch") {
                    BranchComponent branch;
                    branch.componentId = currentComponentId;
                    branch.componentName = tokens.size() > 1 ? tokens[1] : "";
                    branch.componentType = currentComponentType;
                    branches.append(branch);
                } else if (currentComponentType == "snglvol") {
                    SingleVolumeComponent volume;
                    volume.componentId = currentComponentId;
                    volume.componentName = tokens.size() > 1 ? tokens[1] : "";
                    volume.componentType = currentComponentType;
                    volumes.append(volume);
                } else if (currentComponentType == "sngljun") {
                    JunctionComponent junction;
                    junction.componentId = currentComponentId;
                    junction.componentName = tokens.size() > 1 ? tokens[1] : "";
                    junction.componentType = currentComponentType;
                    junctions.append(junction);
                }
            }
        }
    }
    
    return true;
}

bool InputData::parseHeatStructures(const QStringList& lines)
{
    // Parse heat structures (simplified implementation)
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QStringList tokens = tokenizeLine(trimmedLine);
        if (tokens.size() >= 6 && tokens[0].length() == 8 && tokens[0].startsWith("1")) {
            // This might be a heat structure definition
            // Specific parsing logic to be implemented as needed
        }
    }
    
    return true;
}

bool InputData::parseMaterials(const QStringList& lines)
{
    // Parse material properties (simplified implementation)
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QStringList tokens = tokenizeLine(trimmedLine);
        if (tokens.size() >= 2 && tokens[0].startsWith("201")) {
            MaterialProperty material;
            material.materialId = tokens[0];
            material.materialName = tokens.size() > 1 ? tokens[1] : "";
            materials.append(material);
        }
    }
    
    return true;
}

bool InputData::parseTables(const QStringList& lines)
{
    // Parse table data (simplified implementation)
    QString currentTableId;
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QStringList tokens = tokenizeLine(trimmedLine);
        if (tokens.size() >= 2 && tokens[0].startsWith("202")) {
            if (tokens[0].endsWith("000")) {
                // New table starts
                TableData table;
                table.tableId = tokens[0];
                table.tableType = tokens.size() > 1 ? tokens[1] : "";
                tables.append(table);
                currentTableId = tokens[0];
            } else if (!currentTableId.isEmpty() && tokens.size() >= 3) {
                // Table data point
                TableData* table = findTable(currentTableId);
                if (table) {
                    double x = tokens[1].toDouble();
                    double y = tokens[2].toDouble();
                    table->dataPoints.append(qMakePair(x, y));
                }
            }
        }
    }
    
    return true;
}

void InputData::parseComments(const QStringList& lines)
{
    for (const QString& line : lines) {
        if (line.trimmed().startsWith("*")) {
            comments.append(line);
        }
    }
}

QString InputData::extractComponentId(const QString& line) const
{
    QStringList tokens = tokenizeLine(line);
    if (!tokens.isEmpty()) {
        return tokens[0];
    }
    return QString();
}

QString InputData::extractComponentType(const QString& line) const
{
    QStringList tokens = tokenizeLine(line);
    if (tokens.size() >= 3) {
        return tokens[2];
    }
    return QString();
}

QStringList InputData::tokenizeLine(const QString& line) const
{
    QString cleanLine = line.trimmed();
    
    // Remove comments
    int commentPos = cleanLine.indexOf("*");
    if (commentPos >= 0) {
        cleanLine = cleanLine.left(commentPos).trimmed();
    }
    
    // Split tokens
    QStringList tokens = cleanLine.split(QRegularExpression("\\s+"), Qt::SkipEmptyParts);
    return tokens;
}

// =============================================================================
// .i file specific read/write functionality implementation
// =============================================================================

bool InputData::loadFromIFile(const QString& filePath)
{
    clear();
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        m_errors << QString("Cannot open .i file: %1").arg(filePath);
        return false;
    }
    
    // Set basic information
    QFileInfo fileInfo(filePath);
    this->filePath = filePath;
    this->fileName = fileInfo.fileName();
    this->lastModified = fileInfo.lastModified();
    this->fileType = detectFileType(filePath);
    
    // Read file content
    QTextStream in(&file);
    in.setCodec("UTF-8");
    
    QStringList lines;
    while (!in.atEnd()) {
        QString line = in.readLine();
        lines.append(line);
        rawLines.append(line);
    }
    file.close();
    
    // Use specialized .i file parsing methods
    bool success = true;
    
    success &= parseIFileHeader(lines);
    success &= parseIFileControlCards(lines);
    success &= parseIFileComponents(lines);
    success &= parseIFileHeatStructures(lines);
    success &= parseIFileTables(lines);
    
    parseComments(lines);
    
    // Validate .i file format
    success &= validateIFileFormat();
    
    m_isValid = success && m_errors.isEmpty();
    return m_isValid;
}

bool InputData::saveToIFile(const QString& filePath) const
{
    IFileFormatOptions defaultOptions;
    return saveToIFileWithOptions(filePath, defaultOptions);
}

bool InputData::exportToIFile(const QString& filePath, bool preserveComments) const
{
    IFileFormatOptions options;
    options.preserveComments = preserveComments;
    options.addSectionHeaders = true;
    options.preserveOriginalSpacing = false;
    
    return saveToIFileWithOptions(filePath, options);
}

QString InputData::generateIFileContent() const
{
    QString content;
    
    content += generateIFileHeader();
    content += generateIFileControlCards();
    content += generateIFileOutputControl();
    content += generateIFileComponents();
    content += generateIFileHeatStructures();
    content += generateIFileMaterials();
    content += generateIFileTables();
    content += generateIFileFooter();
    
    return content;
}

bool InputData::validateIFileFormat() const
{
    if (!validateIFileStructure()) {
        return false;
    }
    
    if (!validateComponentReferences()) {
        return false;
    }
    
    if (!validateHeatStructureReferences()) {
        return false;
    }
    
    return true;
}

bool InputData::saveToIFileWithOptions(const QString& filePath, const IFileFormatOptions& options) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    
    if (options.preserveOriginalSpacing && !rawLines.isEmpty()) {
        // Preserve original format
        for (const QString& line : rawLines) {
            if (!options.preserveComments && line.trimmed().startsWith("*")) {
                continue;
            }
            out << line << options.lineEnding;
        }
    } else {
        // Generate formatted content
        QString content = generateIFileContent();
        
        if (!options.preserveComments) {
            // 移除注释行
            QStringList lines = content.split('\n');
            QStringList filteredLines;
            for (const QString& line : lines) {
                if (!line.trimmed().startsWith("*")) {
                    filteredLines.append(line);
                }
            }
            content = filteredLines.join(options.lineEnding);
        }
        
        out << content;
    }
    
    file.close();
    return true;
}

// =============================================================================
// .i文件解析方法实现
// =============================================================================

bool InputData::parseIFileHeader(const QStringList& lines)
{
    for (int i = 0; i < lines.size() && i < 20; ++i) {
        const QString& line = lines[i];
        
        // 查找问题标题
        if (line.startsWith("=") && !line.startsWith("=*")) {
            problemTitle = line.mid(1).trimmed();
            break;
        }
    }
    
    return true;
}

bool InputData::parseIFileControlCards(const QStringList& lines)
{
    QRegularExpression controlRegex(R"(^(\d+)\s+(\w+)\s+(\w+))");
    QRegularExpression timeRegex(R"(^(\d+)\s+([\d.e+-]+)\s+([\d.e+-]+)\s+([\d.e+-]+)\s+(\d+)\s+(\d+)\s+(\d+)\s+(\d+))");
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        // 解析控制卡
        QRegularExpressionMatch controlMatch = controlRegex.match(trimmedLine);
        if (controlMatch.hasMatch()) {
            int cardNumber = controlMatch.captured(1).toInt();
            if (cardNumber == 100) {
                controlCard.problemNumber = cardNumber;
                controlCard.problemType = controlMatch.captured(2);
                controlCard.analysisType = controlMatch.captured(3);
            }
            continue;
        }
        
        // 解析时间控制卡
        QRegularExpressionMatch timeMatch = timeRegex.match(trimmedLine);
        if (timeMatch.hasMatch()) {
            int cardNumber = timeMatch.captured(1).toInt();
            if (cardNumber >= 201 && cardNumber <= 299) {
                controlCard.endTime = timeMatch.captured(2).toDouble();
                controlCard.minTimeStep = timeMatch.captured(3).toDouble();
                controlCard.maxTimeStep = timeMatch.captured(4).toDouble();
                controlCard.controlOption = timeMatch.captured(5).toInt();
                controlCard.minorEdit = timeMatch.captured(6).toInt();
                controlCard.majorEdit = timeMatch.captured(7).toInt();
                controlCard.restartFreq = timeMatch.captured(8).toInt();
            }
        }
        
        // 解析单位系统
        QStringList tokens = tokenizeLine(trimmedLine);
        if (tokens.size() >= 3 && tokens[0].toInt() == 102) {
            unitSystem.inputUnits = tokens[1];
            unitSystem.outputUnits = tokens[2];
        } else if (tokens.size() >= 2 && tokens[0].toInt() == 110) {
            unitSystem.workingFluid = tokens[1];
        } else if (tokens.size() >= 2 && tokens[0].toInt() == 115) {
            unitSystem.scaleFactor = tokens[1].toDouble();
        }
    }
    
    return true;
}

bool InputData::parseIFileComponents(const QStringList& lines)
{
    QString currentComponentId;
    QString currentComponentType;
    PipeComponent* currentPipe = nullptr;
    BranchComponent* currentBranch = nullptr;
    SingleVolumeComponent* currentVolume = nullptr;
    JunctionComponent* currentJunction = nullptr;
    
    for (int i = 0; i < lines.size(); ++i) {
        QString trimmedLine = lines[i].trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QStringList tokens = tokenizeLine(trimmedLine);
        if (tokens.isEmpty()) continue;
        
        QString firstToken = tokens[0];
        
        // 检查是否是组件定义行 (CCCXXXX格式，7位数字，以000结尾)
        if (firstToken.length() == 7 && firstToken.endsWith("000") && firstToken.left(6).toInt() > 0) {
            currentComponentId = firstToken;
            currentPipe = nullptr;
            currentBranch = nullptr;
            currentVolume = nullptr;
            currentJunction = nullptr;
            
            if (tokens.size() >= 3) {
                currentComponentType = tokens[2].toLower();
                qDebug() << "Found component:" << currentComponentId << "Type:" << currentComponentType << "Name:" << (tokens.size() > 1 ? tokens[1] : "");
                
                // Clean component name (remove quotes)
                QString componentName = tokens.size() > 1 ? tokens[1] : "";
                if (componentName.startsWith("\"") && componentName.endsWith("\"")) {
                    componentName = componentName.mid(1, componentName.length() - 2);
                }
                
                if (currentComponentType == "pipe") {
                    PipeComponent pipe;
                    pipe.componentId = currentComponentId;
                    pipe.componentName = componentName;
                    pipe.componentType = currentComponentType;
                    pipes.append(pipe);
                    currentPipe = &pipes.last();
                } else if (currentComponentType == "branch" || currentComponentType == "separatr") {
                    BranchComponent branch;
                    branch.componentId = currentComponentId;
                    branch.componentName = componentName;
                    branch.componentType = currentComponentType;
                    branches.append(branch);
                    currentBranch = &branches.last();
                } else if (currentComponentType == "snglvol" || currentComponentType == "tmdpvol") {
                    SingleVolumeComponent volume;
                    volume.componentId = currentComponentId;
                    volume.componentName = componentName;
                    volume.componentType = currentComponentType;
                    volumes.append(volume);
                    currentVolume = &volumes.last();
                } else if (currentComponentType == "sngljun" || currentComponentType == "tmdpjun") {
                    JunctionComponent junction;
                    junction.componentId = currentComponentId;
                    junction.componentName = componentName;
                    junction.componentType = currentComponentType;
                    junctions.append(junction);
                    currentJunction = &junctions.last();
                } else if (currentComponentType == "valve") {
                    JunctionComponent junction;
                    junction.componentId = currentComponentId;
                    junction.componentName = componentName;
                    junction.componentType = currentComponentType;
                    junctions.append(junction);
                    currentJunction = &junctions.last();
                } else if (currentComponentType == "pump") {
                    // 泵组件可以作为特殊的junction处理
                    JunctionComponent junction;
                    junction.componentId = currentComponentId;
                    junction.componentName = componentName;
                    junction.componentType = currentComponentType;
                    junctions.append(junction);
                    currentJunction = &junctions.last();
                } else if (currentComponentType == "annulus") {
                    // 环管作为特殊的pipe处理
                    PipeComponent pipe;
                    pipe.componentId = currentComponentId;
                    pipe.componentName = componentName;
                    pipe.componentType = currentComponentType;
                    pipes.append(pipe);
                    currentPipe = &pipes.last();
                } else if (currentComponentType == "mtpljun") {
                    // 多接管作为特殊的junction处理
                    JunctionComponent junction;
                    junction.componentId = currentComponentId;
                    junction.componentName = componentName;
                    junction.componentType = currentComponentType;
                    junctions.append(junction);
                    currentJunction = &junctions.last();
                }
            }
        }
        // 解析组件的详细数据卡
        else if (!currentComponentId.isEmpty() && firstToken.startsWith(currentComponentId.left(6))) {
            QString cardType = firstToken.mid(6, 1);
            QString cardNumber = firstToken.mid(6);
            
            if (currentPipe) {
                if (cardType == "0" && cardNumber == "001") {
                    // 管道控制体数量 (CCC0001)
                    if (tokens.size() >= 2) {
                        currentPipe->numberOfVolumes = tokens[1].toInt();
                        // 初始化数组
                        currentPipe->volumes.resize(currentPipe->numberOfVolumes);
                        currentPipe->lengths.resize(currentPipe->numberOfVolumes);
                        currentPipe->elevations.resize(currentPipe->numberOfVolumes);
                        currentPipe->roughness.resize(currentPipe->numberOfVolumes);
                        currentPipe->hydraulicDiameters.resize(currentPipe->numberOfVolumes);
                        currentPipe->angles.resize(currentPipe->numberOfVolumes);
                        currentPipe->initialConditions.resize(currentPipe->numberOfVolumes);
                    }
                } else if (cardType == "1" && cardNumber == "101") {
                    // 控制体面积 (CCC0101)
                    if (tokens.size() >= 3) {
                        double area = tokens[1].toDouble();
                        int count = tokens[2].toInt();
                        for (int i = 0; i < qMin(count, currentPipe->numberOfVolumes); ++i) {
                            currentPipe->volumes[i] = area; // 暂时用面积填充体积
                        }
                    }
                } else if (cardType == "2" && cardNumber == "201") {
                    // 内部接管面积 (CCC0201)
                    // 暂时跳过，因为我们的数据结构中没有单独的接管面积
                } else if (cardType == "3" && cardNumber == "301") {
                    // 控制体长度 (CCC0301)
                    if (tokens.size() >= 3) {
                        double length = tokens[1].toDouble();
                        int count = tokens[2].toInt();
                        for (int i = 0; i < qMin(count, currentPipe->numberOfVolumes); ++i) {
                            currentPipe->lengths[i] = length;
                        }
                    }
                } else if (cardType == "6" && cardNumber == "601") {
                    // 垂直倾斜角 (CCC0601)
                    if (tokens.size() >= 3) {
                        double angle = tokens[1].toDouble();
                        int count = tokens[2].toInt();
                        for (int i = 0; i < qMin(count, currentPipe->numberOfVolumes); ++i) {
                            currentPipe->angles[i] = angle;
                        }
                    }
                } else if (cardType == "7" && cardNumber == "701") {
                    // 高度变化 (CCC0701)
                    if (tokens.size() >= 3) {
                        double elevation = tokens[1].toDouble();
                        int count = tokens[2].toInt();
                        for (int i = 0; i < qMin(count, currentPipe->numberOfVolumes); ++i) {
                            currentPipe->elevations[i] = elevation;
                        }
                    }
                } else if (cardType == "8" && cardNumber == "801") {
                    // 壁面粗糙度、水力直径 (CCC0801)
                    if (tokens.size() >= 4) {
                        double roughness = tokens[1].toDouble();
                        double diameter = tokens[2].toDouble();
                        int count = tokens[3].toInt();
                        for (int i = 0; i < qMin(count, currentPipe->numberOfVolumes); ++i) {
                            currentPipe->roughness[i] = roughness;
                            currentPipe->hydraulicDiameters[i] = diameter;
                        }
                    }
                } else if (cardType == "1" && cardNumber.length() == 3 && cardNumber.startsWith("20")) {
                    // 控制体初始条件 (CCC1201)
                    if (tokens.size() >= 7) {
                        int thermoState = tokens[1].toInt();
                        double pressure = tokens[2].toDouble();
                        double temperature = tokens[3].toDouble();
                        int count = tokens[6].toInt();
                        for (int i = 0; i < qMin(count, currentPipe->numberOfVolumes); ++i) {
                            currentPipe->initialConditions[i].thermodynamicState = thermoState;
                            currentPipe->initialConditions[i].pressure = pressure;
                            currentPipe->initialConditions[i].temperature = temperature;
                            currentPipe->initialConditions[i].quality = tokens.size() > 4 ? tokens[4].toDouble() : 0.0;
                            currentPipe->initialConditions[i].velocity = tokens.size() > 5 ? tokens[5].toDouble() : 0.0;
                            currentPipe->initialConditions[i].boronDensity = 0.0; // RELAP5中通常不在此处定义
                        }
                    }
                }
                // 兼容旧格式的解析
                else if (cardType == "1" && cardNumber.length() == 3 && cardNumber.toInt() >= 101 && cardNumber.toInt() <= 199) {
                    // 管道几何数据 (1000101-1000110)
                    int volumeIndex = cardNumber.mid(2).toInt() - 1;
                    if (volumeIndex >= 0 && volumeIndex < currentPipe->numberOfVolumes && tokens.size() >= 7) {
                        currentPipe->volumes[volumeIndex] = tokens[1].toDouble();
                        currentPipe->lengths[volumeIndex] = tokens[2].toDouble();
                        currentPipe->elevations[volumeIndex] = tokens[3].toDouble();
                        currentPipe->roughness[volumeIndex] = tokens[4].toDouble();
                        currentPipe->hydraulicDiameters[volumeIndex] = tokens[5].toDouble();
                        currentPipe->angles[volumeIndex] = tokens[6].toDouble();
                    }
                } else if (cardType == "2" && cardNumber.length() == 3 && cardNumber.toInt() >= 201 && cardNumber.toInt() <= 299) {
                    // 管道初始条件 (1000201-1000210)
                    int volumeIndex = cardNumber.mid(2).toInt() - 1;
                    if (volumeIndex >= 0 && volumeIndex < currentPipe->numberOfVolumes && tokens.size() >= 7) {
                        currentPipe->initialConditions[volumeIndex].thermodynamicState = tokens[1].toInt();
                        currentPipe->initialConditions[volumeIndex].pressure = tokens[2].toDouble();
                        currentPipe->initialConditions[volumeIndex].temperature = tokens[3].toDouble();
                        currentPipe->initialConditions[volumeIndex].quality = tokens[4].toDouble();
                        currentPipe->initialConditions[volumeIndex].velocity = tokens[5].toDouble();
                        currentPipe->initialConditions[volumeIndex].boronDensity = tokens[6].toDouble();
                    }
                }
            } else if (currentBranch && cardType == "1") {
                // 分支数据
                if (tokens.size() >= 3) {
                    currentBranch->numberOfJunctions = tokens[1].toInt();
                    currentBranch->numberOfVolumes = tokens[2].toInt();
                }
            } else if (currentVolume) {
                if (cardType == "1" && cardNumber == "101") {
                    // 单体积几何数据 (2000101)
                    if (tokens.size() >= 8) {
                        currentVolume->volume = tokens[1].toDouble();
                        currentVolume->length = tokens[2].toDouble();
                        currentVolume->elevation = tokens[3].toDouble();
                        currentVolume->angle = tokens[4].toDouble();
                        currentVolume->roughness = tokens[5].toDouble();
                        currentVolume->hydraulicDiameter = tokens[6].toDouble();
                        // tokens[7] is flags
                    }
                } else if (cardType == "2" && cardNumber == "200") {
                    // 单体积初始条件 (2000200)
                    if (tokens.size() >= 5) {
                        currentVolume->thermodynamicState = tokens[1].toInt();
                        currentVolume->pressure = tokens[2].toDouble();
                        currentVolume->temperature = tokens[3].toDouble();
                        currentVolume->quality = tokens[4].toDouble();
                    }
                }
            } else if (currentJunction) {
                if (cardType == "1" && cardNumber == "101") {
                    // 连接几何数据 (3000101)
                    if (tokens.size() >= 7) {
                        currentJunction->fromComponent = tokens[1];
                        currentJunction->toComponent = tokens[2];
                        currentJunction->area = tokens[3].toDouble();
                        currentJunction->forwardLoss = tokens[4].toDouble();
                        currentJunction->reverseLoss = tokens[5].toDouble();
                        currentJunction->flags = tokens[6].toInt();
                    }
                } else if (cardType == "2" && cardNumber == "201") {
                    // 连接初始条件 (3000201)
                    if (tokens.size() >= 5) {
                        currentJunction->velocityFlag = tokens[1].toInt();
                        currentJunction->velocity = tokens[2].toDouble();
                        currentJunction->interfaceVelocity = tokens[3].toDouble();
                        currentJunction->quality = tokens[4].toDouble();
                    }
                }
            }
        }
    }
    
    return true;
}

bool InputData::parseIFileHeatStructures(const QStringList& lines)
{
    QString currentHeatStructureId;
    HeatStructure* currentHS = nullptr;
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QStringList tokens = tokenizeLine(trimmedLine);
        if (tokens.isEmpty()) continue;
        
        QString firstToken = tokens[0];
        
        // 检查是否是热结构定义行
        if (firstToken.length() == 8 && tokens.size() >= 6) {
            HeatStructure hs;
            hs.heatStructureId = firstToken;
            hs.numberOfAxialNodes = tokens[1].toInt();
            hs.numberOfRadialNodes = tokens[2].toInt();
            hs.geometryType = tokens[3].toInt();
            hs.steadyStateFlag = tokens[4].toInt();
            hs.leftBoundary = tokens[5].toDouble();
            if (tokens.size() > 6) {
                hs.rightBoundary = tokens[6].toDouble();
            }
            
            heatStructures.append(hs);
            currentHS = &heatStructures.last();
            currentHeatStructureId = firstToken;
        }
    }
    
    return true;
}

bool InputData::parseIFileTables(const QStringList& lines)
{
    QString currentTableId;
    TableData* currentTable = nullptr;
    
    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();
        if (trimmedLine.startsWith("*") || trimmedLine.isEmpty()) {
            continue;
        }
        
        QStringList tokens = tokenizeLine(trimmedLine);
        if (tokens.size() >= 2 && tokens[0].startsWith("202")) {
            if (tokens[0].endsWith("000")) {
                // 新表格开始
                TableData table;
                table.tableId = tokens[0];
                table.tableType = tokens.size() > 1 ? tokens[1] : "";
                tables.append(table);
                currentTable = &tables.last();
                currentTableId = tokens[0];
            } else if (currentTable && tokens.size() >= 3) {
                // 表格数据点
                double x = tokens[1].toDouble();
                double y = tokens[2].toDouble();
                currentTable->dataPoints.append(qMakePair(x, y));
            }
        }
    }
    
    return true;
}

// =============================================================================
// .i文件生成方法实现
// =============================================================================

QString InputData::generateIFileHeader() const
{
    QString header;
    
    header += "*======================================================================\n";
    if (!problemTitle.isEmpty()) {
        header += QString("=%1\n").arg(problemTitle);
    } else {
        header += "=Generated Input File\n";
    }
    header += "*======================================================================\n\n";
    
    return header;
}

QString InputData::generateIFileControlCards() const
{
    QString content;
    
    // 问题控制卡
    if (controlCard.problemNumber > 0) {
        content += formatIFileLine(QString::number(controlCard.problemNumber),
                                   {controlCard.problemType, controlCard.analysisType},
                                   "Problem control card");
    }
    
    // 单位系统
    if (!unitSystem.inputUnits.isEmpty()) {
        content += formatIFileLine("102", {unitSystem.inputUnits, unitSystem.outputUnits}, "Unit system");
    }
    if (!unitSystem.workingFluid.isEmpty()) {
        content += formatIFileLine("110", {unitSystem.workingFluid}, "Working fluid");
    }
    if (unitSystem.scaleFactor > 0) {
        content += formatIFileLine("115", {QString::number(unitSystem.scaleFactor)}, "Scale factor");
    }
    
    // 时间控制卡
    if (controlCard.endTime > 0) {
        QStringList timeValues = {
            QString::number(controlCard.endTime, 'e', 2),
            QString::number(controlCard.minTimeStep, 'e', 2),
            QString::number(controlCard.maxTimeStep, 'e', 2),
            QString::number(controlCard.controlOption),
            QString::number(controlCard.minorEdit),
            QString::number(controlCard.majorEdit),
            QString::number(controlCard.restartFreq)
        };
        content += formatIFileLine("201", timeValues, "Time control card");
    }
    
    content += "\n";
    return content;
}

QString InputData::generateIFileOutputControl() const
{
    QString content;
    
    if (!plotVariables.isEmpty() || !minorEditVars.isEmpty()) {
        content += "*======================================================================\n";
        content += "*                                绘图变量\n";
        content += "*======================================================================\n";
        
        for (const auto& var : plotVariables) {
            QStringList values = {
                var.variableType,
                var.componentId,
                QString::number(var.plotFlag)
            };
            content += formatIFileLine(QString::number(var.variableId), values);
        }
        
        content += "*======================================================================\n";
        content += "*                                小编辑\n";
        content += "*======================================================================\n";
        
        for (const auto& var : minorEditVars) {
            QStringList values = {
                var.variableType,
                var.componentId
            };
            content += formatIFileLine(QString::number(var.editId), values);
        }
        
        content += "\n";
    }
    
    return content;
}

QString InputData::generateIFileComponents() const
{
    QString content;
    
    if (getTotalComponents() > 0) {
        content += "*======================================================================\n";
        content += "*                               水力组件\n";
        content += "*======================================================================\n";
        
        // 生成管道组件
        for (const auto& pipe : pipes) {
            content += formatPipeComponent(pipe);
            content += "*----------------------------------------------------------------------\n";
        }
        
        // 生成分支组件
        for (const auto& branch : branches) {
            content += formatBranchComponent(branch);
            content += "*----------------------------------------------------------------------\n";
        }
        
        // 生成单体积组件
        for (const auto& volume : volumes) {
            content += formatSingleVolumeComponent(volume);
            content += "*----------------------------------------------------------------------\n";
        }
        
        // 生成连接组件
        for (const auto& junction : junctions) {
            content += formatJunctionComponent(junction);
            content += "*----------------------------------------------------------------------\n";
        }
        
        content += "\n";
    }
    
    return content;
}

QString InputData::generateIFileHeatStructures() const
{
    QString content;
    
    if (!heatStructures.isEmpty()) {
        content += "*======================================================================\n";
        content += "*                              热结构\n";
        content += "*======================================================================\n";
        
        for (const auto& hs : heatStructures) {
            content += formatHeatStructureCard(hs);
        }
        
        content += "\n";
    }
    
    return content;
}

QString InputData::generateIFileMaterials() const
{
    QString content;
    
    if (!materials.isEmpty()) {
        content += "*======================================================================\n";
        content += "*                              材料\n";
        content += "*======================================================================\n";
        
        for (const auto& material : materials) {
            content += formatIFileLine(material.materialId, {material.materialName});
        }
        
        content += "\n";
    }
    
    return content;
}

QString InputData::generateIFileTables() const
{
    QString content;
    
    if (!tables.isEmpty()) {
        content += "*======================================================================\n";
        content += "*                              表格\n";
        content += "*======================================================================\n";
        
        for (const auto& table : tables) {
            content += formatTableCard(table);
        }
        
        content += "\n";
    }
    
    return content;
}

QString InputData::generateIFileFooter() const
{
    QString footer;
    footer += "*======================================================================\n";
    footer += ".End of input\n";
    return footer;
}

// =============================================================================
// .i文件格式化辅助方法实现
// =============================================================================

QString InputData::formatIFileLine(const QString& cardId, const QStringList& values, const QString& comment) const
{
    QString line = cardId;
    
    for (const QString& value : values) {
        line += "   " + value;
    }
    
    if (!comment.isEmpty()) {
        line += "   *" + comment;
    }
    
    line += "\n";
    return line;
}

QString InputData::formatComponentCard(const GeometryComponent& component) const
{
    QString content;
    content += formatIFileLine(component.componentId,
                               {QString("\"%1\"").arg(component.componentName), component.componentType});
    return content;
}

QString InputData::formatPipeComponent(const PipeComponent& pipe) const
{
    QString content;
    
    // 组件定义行
    content += formatComponentCard(pipe);
    
    // 体积数量
    if (pipe.numberOfVolumes > 0) {
        content += formatIFileLine(pipe.componentId + "001", {QString::number(pipe.numberOfVolumes)});
    }
    
    // 其他管道数据卡可以根据需要添加
    
    return content;
}

QString InputData::formatBranchComponent(const BranchComponent& branch) const
{
    QString content;
    
    // 组件定义行
    content += formatComponentCard(branch);
    
    // 分支数据
    if (branch.numberOfJunctions > 0) {
        QStringList values = {
            QString::number(branch.numberOfJunctions),
            QString::number(branch.numberOfVolumes)
        };
        content += formatIFileLine(branch.componentId + "001", values);
    }
    
    return content;
}

QString InputData::formatSingleVolumeComponent(const SingleVolumeComponent& volume) const
{
    QString content;
    
    // 组件定义行
    content += formatComponentCard(volume);
    
    // 体积数据
    QStringList values = {
        QString::number(volume.volume, 'e', 3),
        QString::number(volume.length, 'e', 3),
        QString::number(volume.elevation, 'e', 3)
    };
    content += formatIFileLine(volume.componentId + "101", values);
    
    return content;
}

QString InputData::formatJunctionComponent(const JunctionComponent& junction) const
{
    QString content;
    
    // 组件定义行
    content += formatComponentCard(junction);
    
    // 连接数据
    QStringList values = {
        junction.fromComponent,
        junction.toComponent,
        QString::number(junction.area, 'e', 3),
        QString::number(junction.forwardLoss, 'e', 3),
        QString::number(junction.reverseLoss, 'e', 3),
        "000000"
    };
    content += formatIFileLine(junction.componentId + "101", values);
    
    return content;
}

QString InputData::formatHeatStructureCard(const HeatStructure& heatStructure) const
{
    QString content;
    
    QStringList values = {
        QString::number(heatStructure.numberOfAxialNodes),
        QString::number(heatStructure.numberOfRadialNodes),
        QString::number(heatStructure.geometryType),
        QString::number(heatStructure.steadyStateFlag),
        QString::number(heatStructure.leftBoundary, 'e', 3)
    };
    
    if (heatStructure.rightBoundary > 0) {
        values.append(QString::number(heatStructure.rightBoundary, 'e', 3));
    }
    
    content += formatIFileLine(heatStructure.heatStructureId + "000", values);
    
    return content;
}

QString InputData::formatTableCard(const TableData& table) const
{
    QString content;
    
    // 表格定义行
    content += formatIFileLine(table.tableId, {table.tableType});
    
    // 表格数据点
    for (int i = 0; i < table.dataPoints.size(); ++i) {
        const auto& point = table.dataPoints[i];
        QString pointId = table.tableId.left(6) + QString("%1").arg(i + 1, 3, 10, QChar('0'));
        QStringList values = {
            QString::number(point.first, 'e', 3),
            QString::number(point.second, 'e', 3)
        };
        content += formatIFileLine(pointId, values);
    }
    
    return content;
}

// =============================================================================
// .i文件验证方法实现
// =============================================================================

bool InputData::validateIFileStructure() const
{
    // 检查基本结构
    if (controlCard.problemNumber <= 0) {
        const_cast<InputData*>(this)->m_errors << "缺少有效的问题控制卡";
        return false;
    }
    
    if (controlCard.endTime <= 0) {
        const_cast<InputData*>(this)->m_errors << "缺少有效的时间控制卡";
        return false;
    }
    
    return true;
}

bool InputData::validateComponentReferences() const
{
    // 验证连接组件的引用
    for (const auto& junction : junctions) {
        if (!junction.fromComponent.isEmpty()) {
            if (findComponent(junction.fromComponent) == nullptr) {
                const_cast<InputData*>(this)->m_errors << 
                    QString("连接组件 %1 引用了不存在的组件 %2")
                    .arg(junction.componentId, junction.fromComponent);
                return false;
            }
        }
        
        if (!junction.toComponent.isEmpty()) {
            if (findComponent(junction.toComponent) == nullptr) {
                const_cast<InputData*>(this)->m_errors << 
                    QString("连接组件 %1 引用了不存在的组件 %2")
                    .arg(junction.componentId, junction.toComponent);
                return false;
            }
        }
    }
    
    return true;
}

bool InputData::validateHeatStructureReferences() const
{
    // 验证热结构的边界条件引用
    for (const auto& hs : heatStructures) {
        for (const auto& bc : hs.boundaryConditions) {
            if (!bc.componentId.isEmpty()) {
                if (findComponent(bc.componentId) == nullptr) {
                    const_cast<InputData*>(this)->m_errors << 
                        QString("热结构 %1 引用了不存在的组件 %2")
                        .arg(hs.heatStructureId, bc.componentId);
                    return false;
                }
            }
        }
    }
    
    return true;
}

QStringList InputData::checkIFileConsistency() const
{
    QStringList issues;
    
    // 检查组件ID的唯一性
    QStringList allIds;
    for (const auto& pipe : pipes) allIds << pipe.componentId;
    for (const auto& branch : branches) allIds << branch.componentId;
    for (const auto& volume : volumes) allIds << volume.componentId;
    for (const auto& junction : junctions) allIds << junction.componentId;
    
    QStringList duplicates;
    for (int i = 0; i < allIds.size(); ++i) {
        for (int j = i + 1; j < allIds.size(); ++j) {
            if (allIds[i] == allIds[j] && !duplicates.contains(allIds[i])) {
                duplicates << allIds[i];
            }
        }
    }
    
    for (const QString& duplicate : duplicates) {
        issues << QString("重复的组件ID: %1").arg(duplicate);
    }
    
    return issues;
}
