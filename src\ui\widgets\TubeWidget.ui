<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TubeWidget</class>
 <widget class="QWidget" name="TubeWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Junction Component Editor</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <item>
    <widget class="QGroupBox" name="basicGroup">
     <property name="title">
      <string>Basic Information</string>
     </property>
     <layout class="QFormLayout" name="basicLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="componentIdLabel">
        <property name="text">
         <string>Component ID:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="componentIdEdit">
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="componentNameLabel">
        <property name="text">
         <string>Component Name:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="componentNameEdit"/>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="fromComponentLabel">
        <property name="text">
         <string>From Component:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="fromComponentEdit"/>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="toComponentLabel">
        <property name="text">
         <string>To Component:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QLineEdit" name="toComponentEdit"/>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="componentTypeLabel">
        <property name="text">
         <string>Component Type:</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QComboBox" name="componentTypeCombo">
        <item>
         <property name="text">
          <string>sngljun</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>tmdpjun</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>valve</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>pump</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>mtpljun</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="geometryGroup">
     <property name="title">
      <string>Geometry Data</string>
     </property>
     <layout class="QFormLayout" name="geometryLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="areaLabel">
        <property name="text">
         <string>Area (m²):</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QDoubleSpinBox" name="areaSpin">
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="minimum">
         <double>0.000001000000000</double>
        </property>
        <property name="maximum">
         <double>1000.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.001000000000000</double>
        </property>
        <property name="value">
         <double>0.010000000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="forwardLossLabel">
        <property name="text">
         <string>Forward Loss:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="forwardLossSpin">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>0.000000000000000</double>
        </property>
        <property name="maximum">
         <double>1000.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.100000000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="reverseLossLabel">
        <property name="text">
         <string>Reverse Loss:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QDoubleSpinBox" name="reverseLossSpin">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>0.000000000000000</double>
        </property>
        <property name="maximum">
         <double>1000.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.100000000000000</double>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="flagsLabel">
        <property name="text">
         <string>Flags:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QSpinBox" name="flagsSpin">
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>999999</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="controlFlagsGroup">
     <property name="title">
      <string>Control Flags</string>
     </property>
     <layout class="QGridLayout" name="controlFlagsLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="junctionControlLabel">
        <property name="text">
         <string>Junction Control Flags (jefvcahs):</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="junctionControlEdit">
        <property name="text">
         <string>00000000</string>
        </property>
        <property name="maxLength">
         <number>8</number>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="criticalFlowLabel">
        <property name="text">
         <string>Critical Flow Coefficients:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <layout class="QHBoxLayout" name="criticalFlowLayout">
        <item>
         <widget class="QDoubleSpinBox" name="criticalFlow1Spin">
          <property name="decimals">
           <number>4</number>
          </property>
          <property name="minimum">
           <double>0.000000000000000</double>
          </property>
          <property name="maximum">
           <double>10.000000000000000</double>
          </property>
          <property name="value">
           <double>1.000000000000000</double>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QDoubleSpinBox" name="criticalFlow2Spin">
          <property name="decimals">
           <number>4</number>
          </property>
          <property name="minimum">
           <double>0.000000000000000</double>
          </property>
          <property name="maximum">
           <double>10.000000000000000</double>
          </property>
          <property name="value">
           <double>1.000000000000000</double>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="initialConditionGroup">
     <property name="title">
      <string>Initial Conditions</string>
     </property>
     <layout class="QFormLayout" name="initialConditionLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="velocityFlagLabel">
        <property name="text">
         <string>Velocity Flag:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QSpinBox" name="velocityFlagSpin">
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>10</number>
        </property>
        <property name="value">
         <number>1</number>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="velocityLabel">
        <property name="text">
         <string>Velocity (m/s):</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="velocitySpin">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>-1000.000000000000000</double>
        </property>
        <property name="maximum">
         <double>1000.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.100000000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="interfaceVelocityLabel">
        <property name="text">
         <string>Interface Velocity (m/s):</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QDoubleSpinBox" name="interfaceVelocitySpin">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>-1000.000000000000000</double>
        </property>
        <property name="maximum">
         <double>1000.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.100000000000000</double>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="qualityLabel">
        <property name="text">
         <string>Quality:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QDoubleSpinBox" name="qualitySpin">
        <property name="decimals">
         <number>4</number>
        </property>
        <property name="minimum">
         <double>0.000000000000000</double>
        </property>
        <property name="maximum">
         <double>1.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.010000000000000</double>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="operationsGroup">
     <property name="title">
      <string>Operations</string>
     </property>
     <layout class="QHBoxLayout" name="operationsLayout">
      <item>
       <widget class="QPushButton" name="validateBtn">
        <property name="text">
         <string>Validate Data</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="generateCardsBtn">
        <property name="text">
         <string>Generate RELAP5 Cards</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="resetBtn">
        <property name="text">
         <string>Reset to Defaults</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="operationsHorizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 