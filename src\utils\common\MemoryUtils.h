#ifndef MEMORYUTILS_H
#define MEMORYUTILS_H

#include <memory>
#include <QObject>
#include <QScopedPointer>
#include <QSharedPointer>
#include <QWeakPointer>
#include <QFile>
#include <QMutex>
#include <QMutexLocker>

/**
 * @brief 内存管理工具类
 * 
 * 提供内存管理相关的工具函数和RAII包装器。
 */
namespace MemoryUtils {

/**
 * @brief 创建QObject的智能指针
 * @tparam T QObject子类类型
 * @tparam Args 构造函数参数类型
 * @param args 构造函数参数
 * @return 智能指针
 */
template<typename T, typename... Args>
std::unique_ptr<T> makeUnique(Args&&... args)
{
    return std::unique_ptr<T>(new T(std::forward<Args>(args)...));
}

/**
 * @brief 创建QObject的共享指针
 * @tparam T QObject子类类型
 * @tparam Args 构造函数参数类型
 * @param args 构造函数参数
 * @return 共享指针
 */
template<typename T, typename... Args>
QSharedPointer<T> makeShared(Args&&... args)
{
    return QSharedPointer<T>(new T(std::forward<Args>(args)...));
}

/**
 * @brief 文件资源守卫类
 * 
 * 使用RAII模式管理文件资源，确保文件正确关闭。
 */
class FileGuard
{
public:
    /**
     * @brief 构造函数
     * @param file 文件对象引用
     */
    explicit FileGuard(QFile& file) : m_file(file) {}

    /**
     * @brief 析构函数
     */
    ~FileGuard()
    {
        if (m_file.isOpen()) {
            m_file.close();
        }
    }

private:
    QFile& m_file;
    
    // 禁止拷贝和赋值
    Q_DISABLE_COPY(FileGuard)
};

/**
 * @brief 互斥锁守卫类
 * 
 * 使用RAII模式管理互斥锁，确保锁正确释放。
 */
class MutexGuard
{
public:
    /**
     * @brief 构造函数
     * @param mutex 互斥锁引用
     */
    explicit MutexGuard(QMutex& mutex) : m_mutex(mutex)
    {
        m_mutex.lock();
    }

    /**
     * @brief 析构函数
     */
    ~MutexGuard()
    {
        m_mutex.unlock();
    }

private:
    QMutex& m_mutex;
    
    // 禁止拷贝和赋值
    Q_DISABLE_COPY(MutexGuard)
};

/**
 * @brief 内存泄漏检测类
 * 
 * 用于调试模式下检测内存泄漏。
 */
class MemoryLeakDetector
{
public:
    /**
     * @brief 获取单例实例
     * @return 单例实例引用
     */
    static MemoryLeakDetector& instance();

    /**
     * @brief 注册分配
     * @param ptr 指针
     * @param size 大小
     * @param file 文件名
     * @param line 行号
     */
    void registerAllocation(void* ptr, size_t size, const char* file, int line);

    /**
     * @brief 注册释放
     * @param ptr 指针
     */
    void registerDeallocation(void* ptr);

    /**
     * @brief 打印泄漏报告
     */
    void printLeakReport();

private:
    /**
     * @brief 构造函数
     */
    MemoryLeakDetector();

    /**
     * @brief 析构函数
     */
    ~MemoryLeakDetector();

    struct AllocationInfo {
        size_t size;
        const char* file;
        int line;
    };

    QMutex m_mutex;
    QMap<void*, AllocationInfo> m_allocations;
};

} // namespace MemoryUtils

// 内存分配跟踪宏（仅在调试模式下启用）
#ifdef DEBUG_MODE
#define TRACK_NEW new(__FILE__, __LINE__)
#define TRACK_DELETE delete
#else
#define TRACK_NEW new
#define TRACK_DELETE delete
#endif

// 重载全局new和delete操作符（仅在调试模式下启用）
#ifdef DEBUG_MODE
void* operator new(size_t size, const char* file, int line);
void* operator new[](size_t size, const char* file, int line);
void operator delete(void* ptr) noexcept;
void operator delete[](void* ptr) noexcept;
#endif

#endif // MEMORYUTILS_H
