<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Widget</class>
 <widget class="QWidget" name="Widget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>666</width>
    <height>592</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Widget</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="SAColorGridWidget" name="colorGrid1" native="true"/>
   </item>
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>ToolButtonIconOnly</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="label">
         <property name="text">
          <string>No Icon</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyNoIcon">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyNoIcon2">
         <property name="text">
          <string/>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>Have Icon</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIcon">
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIcon2">
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>Have Icon(small)</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconSmall">
         <property name="maximumSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconSmall2">
         <property name="maximumSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>Have Icon(large)</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconLarge">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="text">
          <string>ToolButtonIconOnly,Have Icon(large)</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconLarge2">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="text">
          <string>ToolButtonIconOnly,Have Icon(large)</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="label_5">
         <property name="text">
          <string>MenuPopMode</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconMenuPopupMode">
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::MenuButtonPopup</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconMenuPopupMode2">
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::MenuButtonPopup</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="label_6">
         <property name="text">
          <string>InstantPop</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconInstantPop">
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::InstantPopup</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="5" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconInstantPop2">
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::InstantPopup</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="label_7">
         <property name="text">
          <string>DelayedPop</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconDelayedPop">
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::DelayedPopup</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="6" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyHaveIconDelayedPop2">
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::DelayedPopup</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>ToolButtonTextOnly</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0">
        <widget class="QLabel" name="label_12">
         <property name="text">
          <string>No Icon</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyText">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyText_1">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_10">
         <property name="text">
          <string>Have Icon</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIcon">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIcon_1">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_9">
         <property name="text">
          <string>Have Icon(small)</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconSmall">
         <property name="maximumSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconSmall_1">
         <property name="maximumSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_11">
         <property name="text">
          <string>Have Icon(large)</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconLarge">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconLarge_1">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="label_14">
         <property name="text">
          <string>MenuPopMode</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconMenuPopupMode">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::MenuButtonPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconMenuPopupMode_1">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::MenuButtonPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="label_13">
         <property name="text">
          <string>InstantPop</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconInstantPop">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::InstantPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="5" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconInstantPop_1">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::InstantPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="label_8">
         <property name="text">
          <string>DelayedPop</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconDelayedPop">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::DelayedPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="6" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconOnlyTextHaveIconDelayedPop_1">
         <property name="text">
          <string>Text Only</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::DelayedPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextOnly</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_3">
      <attribute name="title">
       <string>TextBesideIcon</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <item row="0" column="0">
        <widget class="QLabel" name="label_18">
         <property name="text">
          <string>No Icon</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="SAColorToolButton" name="toolButtonIconTextBeside">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="SAColorToolButton" name="toolButtonIconTextBeside1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_17">
         <property name="text">
          <string>Have Icon</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIcon">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIcon1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_20">
         <property name="text">
          <string>Have Icon(small)</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIconSmall">
         <property name="maximumSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIconSmall1">
         <property name="maximumSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_21">
         <property name="text">
          <string>Have Icon(large)</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIconLarge">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIconLarge1">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="label_16">
         <property name="text">
          <string>MenuPopMode</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextBesideMenuPopupMode">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::MenuButtonPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextBesideMenuPopupMode1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::MenuButtonPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="label_15">
         <property name="text">
          <string>InstantPop</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIconInstantPop">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::InstantPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="5" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIconInstantPop1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::InstantPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="label_19">
         <property name="text">
          <string>DelayedPop</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIconDelayedPop">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::DelayedPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="6" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextBesideHaveIconDelayedPop1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::DelayedPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextBesideIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_4">
      <attribute name="title">
       <string>TextUnderIcon</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_5">
       <item row="0" column="0">
        <widget class="QLabel" name="label_27">
         <property name="text">
          <string>No Icon</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIcon">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIcon1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="label_23">
         <property name="text">
          <string>Have Icon</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconHaveIcon">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="1" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconHaveIcon1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_22">
         <property name="text">
          <string>Have Icon(small)</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconHaveIconSmall">
         <property name="maximumSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="2" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconHaveIconSmall1">
         <property name="maximumSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="label_25">
         <property name="text">
          <string>Have Icon(large)</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconHaveIconLarge">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="3" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconHaveIconLarge1">
         <property name="minimumSize">
          <size>
           <width>60</width>
           <height>60</height>
          </size>
         </property>
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="label_28">
         <property name="text">
          <string>MenuPopMode</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconMenuPopupMode">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::MenuButtonPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="4" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconMenuPopupMode1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::MenuButtonPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="label_24">
         <property name="text">
          <string>InstantPop</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconInstantPop">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::InstantPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="5" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconInstantPop1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::InstantPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="label_26">
         <property name="text">
          <string>DelayedPop</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconDelayedPop">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::DelayedPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item row="6" column="2">
        <widget class="SAColorToolButton" name="toolButtonTextUnderIconDelayedPop1">
         <property name="text">
          <string>Text</string>
         </property>
         <property name="icon">
          <iconset resource="icon.qrc">
           <normaloff>:/colorWidgetTst/icon/text.svg</normaloff>:/colorWidgetTst/icon/text.svg</iconset>
         </property>
         <property name="popupMode">
          <enum>QToolButton::DelayedPopup</enum>
         </property>
         <property name="toolButtonStyle">
          <enum>Qt::ToolButtonTextUnderIcon</enum>
         </property>
         <property name="autoRaise">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_5">
      <attribute name="title">
       <string>ColorGridWidget</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLabel" name="label_30">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>row=5,col=5</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="SAColorGridWidget" name="colorGrid2" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_31">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>colorPaletteGrid</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="SAColorPaletteGridWidget" name="colorGrid2_2" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>SAColorToolButton</class>
   <extends>QToolButton</extends>
   <header>SAColorToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>SAColorGridWidget</class>
   <extends>QWidget</extends>
   <header>SAColorGridWidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>SAColorPaletteGridWidget</class>
   <extends>QWidget</extends>
   <header>SAColorPaletteGridWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="icon.qrc"/>
 </resources>
 <connections/>
</ui>
