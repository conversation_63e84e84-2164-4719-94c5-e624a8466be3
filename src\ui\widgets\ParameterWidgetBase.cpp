#include "ParameterWidgetBase.h"
#include <QSettings>

ParameterWidgetBase::ParameterWidgetBase(QWidget* parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(6, 6, 6, 6);
    m_mainLayout->setSpacing(6);
    setLayout(m_mainLayout);
}

ParameterWidgetBase::~ParameterWidgetBase()
{
}

void ParameterWidgetBase::clearParameters()
{
    setParameters(QVariantMap());
}

void ParameterWidgetBase::resetParameters()
{
    setParameters(getDefaultParameters());
}

bool ParameterWidgetBase::validateParameters() const
{
    QVariantMap params = getParameters();
    QVariantMap ranges = getParameterRanges();

    for (auto it = params.begin(); it != params.end(); ++it) {
        const QString& key = it.key();
        const QVariant& value = it.value();

        if (ranges.contains(key)) {
            QVariantMap range = ranges[key].toMap();
            if (range.contains("min") && value < range["min"]) {
                return false;
            }
            if (range.contains("max") && value > range["max"]) {
                return false;
            }
        }
    }

    return true;
}

QVariantMap ParameterWidgetBase::getParameterDescriptions() const
{
    return m_parameterDescriptions;
}

QVariantMap ParameterWidgetBase::getDefaultParameters() const
{
    return m_defaultParameters;
}

QVariantMap ParameterWidgetBase::getParameterRanges() const
{
    return m_parameterRanges;
}

QVariantMap ParameterWidgetBase::getParameterUnits() const
{
    return m_parameterUnits;
}

void ParameterWidgetBase::saveState(QSettings& settings)
{
    settings.beginGroup(widgetName());
    
    QVariantMap params = getParameters();
    for (auto it = params.begin(); it != params.end(); ++it) {
        settings.setValue(it.key(), it.value());
    }
    
    settings.endGroup();
}

void ParameterWidgetBase::restoreState(const QSettings& settings)
{
    QVariantMap params;
    
    const_cast<QSettings&>(settings).beginGroup(widgetName());
    
    QStringList keys = const_cast<QSettings&>(settings).allKeys();
    for (const QString& key : keys) {
        params[key] = const_cast<QSettings&>(settings).value(key);
    }
    
    const_cast<QSettings&>(settings).endGroup();
    
    if (!params.isEmpty()) {
        setParameters(params);
    }
}
