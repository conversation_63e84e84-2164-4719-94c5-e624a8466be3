#ifndef OPTIMIZATIONPARAMETERS_H
#define OPTIMIZATIONPARAMETERS_H

#include "BaseModel.h"
#include <QString>
#include <QStringList>
#include <QVariantMap>

/**
 * @brief 优化参数模型类
 * 
 * 存储优化算法的参数配置，包括优化方法、目标函数、约束条件等。
 */
class OptimizationParameters : public BaseModel
{
public:
    /**
     * @brief 优化方法枚举
     */
    enum class Method {
        GradientDescent,
        NewtonMethod,
        QuasiNewton,
        ConjugateGradient,
        GeneticAlgorithm,
        ParticleSwarm,
        SimulatedAnnealing,
        DifferentialEvolution,
        Custom
    };

    /**
     * @brief 构造函数
     */
    OptimizationParameters();

    /**
     * @brief 拷贝构造函数
     * @param other 另一个优化参数模型
     */
    OptimizationParameters(const OptimizationParameters& other);

    /**
     * @brief 析构函数
     */
    ~OptimizationParameters();

    /**
     * @brief 赋值操作符
     * @param other 另一个优化参数模型
     * @return 当前模型引用
     */
    OptimizationParameters& operator=(const OptimizationParameters& other);

    // 参数访问方法
    /**
     * @brief 设置优化方法
     * @param method 优化方法
     */
    void setMethod(Method method);

    /**
     * @brief 获取优化方法
     * @return 优化方法
     */
    Method method() const;

    /**
     * @brief 设置优化方法名称
     * @param methodName 优化方法名称
     */
    void setMethodName(const QString& methodName);

    /**
     * @brief 获取优化方法名称
     * @return 优化方法名称
     */
    QString methodName() const;

    /**
     * @brief 设置目标函数
     * @param objective 目标函数
     */
    void setObjective(const QString& objective);

    /**
     * @brief 获取目标函数
     * @return 目标函数
     */
    QString objective() const;

    /**
     * @brief 设置约束条件
     * @param constraints 约束条件列表
     */
    void setConstraints(const QStringList& constraints);

    /**
     * @brief 获取约束条件
     * @return 约束条件列表
     */
    QStringList constraints() const;

    /**
     * @brief 添加约束条件
     * @param constraint 约束条件
     */
    void addConstraint(const QString& constraint);

    /**
     * @brief 移除约束条件
     * @param index 约束条件索引
     * @return 是否成功移除
     */
    bool removeConstraint(int index);

    /**
     * @brief 设置变量范围
     * @param variableName 变量名称
     * @param min 最小值
     * @param max 最大值
     */
    void setVariableRange(const QString& variableName, double min, double max);

    /**
     * @brief 获取变量范围
     * @param variableName 变量名称
     * @return 变量范围 [min, max]
     */
    QPair<double, double> getVariableRange(const QString& variableName) const;

    /**
     * @brief 设置算法参数
     * @param paramName 参数名称
     * @param value 参数值
     */
    void setAlgorithmParameter(const QString& paramName, const QVariant& value);

    /**
     * @brief 获取算法参数
     * @param paramName 参数名称
     * @param defaultValue 默认值
     * @return 参数值
     */
    QVariant getAlgorithmParameter(const QString& paramName, const QVariant& defaultValue = QVariant()) const;

    /**
     * @brief 设置最大迭代次数
     * @param maxIterations 最大迭代次数
     */
    void setMaxIterations(int maxIterations);

    /**
     * @brief 获取最大迭代次数
     * @return 最大迭代次数
     */
    int maxIterations() const;

    /**
     * @brief 设置收敛容差
     * @param tolerance 收敛容差
     */
    void setTolerance(double tolerance);

    /**
     * @brief 获取收敛容差
     * @return 收敛容差
     */
    double tolerance() const;

    // BaseModel接口实现
    QVariant toVariant() const override;
    void fromVariant(const QVariant& variant) override;
    bool isValid() const override;
    QString modelName() const override;
    QString modelVersion() const override;
    BaseModel* clone() const override;

private:
    Method m_method;
    QString m_methodName;
    QString m_objective;
    QStringList m_constraints;
    QMap<QString, QPair<double, double>> m_variableRanges;
    QVariantMap m_algorithmParameters;
    int m_maxIterations;
    double m_tolerance;
};

#endif // OPTIMIZATIONPARAMETERS_H
