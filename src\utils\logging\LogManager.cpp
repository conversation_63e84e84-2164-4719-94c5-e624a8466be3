#include "LogManager.h"
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QCoreApplication>

LogManager::LogManager()
    : QObject(nullptr)
    , m_logLevel(LogLevel::Info)
    , m_consoleOutput(true)
    , m_fileOutput(true)
    , m_initialized(false)
{
}

LogManager::~LogManager()
{
    shutdown();
}

bool LogManager::initialize(const QString& logFilePath)
{
    if (m_initialized) {
        return true;
    }

    QString filePath = logFilePath;
    if (filePath.isEmpty()) {
        QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
        QDir().mkpath(logDir);
        filePath = logDir + "/application.log";
    }

    m_logFile.setFileName(filePath);
    if (!m_logFile.open(QIODevice::WriteOnly | QIODevice::Append)) {
        qWarning() << "Failed to open log file:" << filePath;
        m_fileOutput = false;
    } else {
        m_logStream.setDevice(&m_logFile);
    }

    m_initialized = true;
    logInfo("LogManager initialized");

    return true;
}

void LogManager::shutdown()
{
    if (!m_initialized) {
        return;
    }

    logInfo("LogManager shutting down");

    if (m_logFile.isOpen()) {
        m_logStream.flush();
        m_logFile.close();
    }

    m_initialized = false;
}

void LogManager::logDebug(const QString& message)
{
    log(LogLevel::Debug, message);
}

void LogManager::logInfo(const QString& message)
{
    log(LogLevel::Info, message);
}

void LogManager::logWarning(const QString& message)
{
    log(LogLevel::Warning, message);
}

void LogManager::logError(const QString& message)
{
    log(LogLevel::Error, message);
}

void LogManager::logFatal(const QString& message)
{
    log(LogLevel::Fatal, message);
}

void LogManager::log(LogLevel level, const QString& message)
{
    if (!m_initialized || level < m_logLevel) {
        return;
    }

    QMutexLocker locker(&m_mutex);

    QDateTime timestamp = QDateTime::currentDateTime();
    QString formattedMessage = formatLogMessage(level, message);

    // 输出到控制台
    if (m_consoleOutput) {
        switch (level) {
        case LogLevel::Debug:
            qDebug().noquote() << formattedMessage;
            break;
        case LogLevel::Info:
            qInfo().noquote() << formattedMessage;
            break;
        case LogLevel::Warning:
            qWarning().noquote() << formattedMessage;
            break;
        case LogLevel::Error:
        case LogLevel::Fatal:
            qCritical().noquote() << formattedMessage;
            break;
        }
    }

    // 输出到文件
    if (m_fileOutput && m_logFile.isOpen()) {
        writeToFile(formattedMessage);
    }

    // 发送信号
    emit logRecorded(level, message, timestamp);
}

void LogManager::setLogLevel(LogLevel level)
{
    m_logLevel = level;
}

LogLevel LogManager::logLevel() const
{
    return m_logLevel;
}

void LogManager::setConsoleOutput(bool enabled)
{
    m_consoleOutput = enabled;
}

void LogManager::setFileOutput(bool enabled)
{
    m_fileOutput = enabled;
}

void LogManager::clearLogFile()
{
    if (m_logFile.isOpen()) {
        m_logFile.resize(0);
    }
}

QString LogManager::levelToString(LogLevel level) const
{
    switch (level) {
    case LogLevel::Debug:   return "DEBUG";
    case LogLevel::Info:    return "INFO";
    case LogLevel::Warning: return "WARNING";
    case LogLevel::Error:   return "ERROR";
    case LogLevel::Fatal:   return "FATAL";
    default:                return "UNKNOWN";
    }
}

QString LogManager::formatLogMessage(LogLevel level, const QString& message) const
{
    QDateTime timestamp = QDateTime::currentDateTime();
    return QString("[%1] [%2] %3")
           .arg(timestamp.toString("yyyy-MM-dd hh:mm:ss.zzz"))
           .arg(levelToString(level))
           .arg(message);
}

void LogManager::writeToFile(const QString& message)
{
    m_logStream << message << Qt::endl;
    m_logStream.flush();
}
