#ifndef UTILS_H
#define UTILS_H

#include <QString>
#include <QDateTime>
#include <QDir>
#include <QFileInfo>
#include <QStringList>

/**
 * @brief The Utils class provides utility functions
 */
class Utils
{
public:
    /**
     * @brief Generate a timestamped filename
     * @param baseName Base name for the file
     * @param extension File extension (without the dot)
     * @return Timestamped filename
     */
    static QString generateTimestampedFilename(const QString &baseName, const QString &extension);
    
    /**
     * @brief Create a directory if it doesn't exist
     * @param dirPath Path to the directory
     * @return True if the directory exists or was created, false otherwise
     */
    static bool ensureDirectoryExists(const QString &dirPath);
    
    /**
     * @brief Check if a file exists
     * @param filePath Path to the file
     * @return True if the file exists, false otherwise
     */
    static bool fileExists(const QString &filePath);
    
    /**
     * @brief Get file size in human-readable format
     * @param filePath Path to the file
     * @return Human-readable file size (e.g., "1.2 MB")
     */
    static QString getHumanReadableFileSize(const QString &filePath);
    
    /**
     * @brief Split a string into lines
     * @param text The text to split
     * @return List of lines
     */
    static QStringList splitIntoLines(const QString &text);
    
    /**
     * @brief Format a date/time
     * @param dateTime The date/time to format
     * @param format The format string (optional, default is "yyyy-MM-dd hh:mm:ss")
     * @return Formatted date/time string
     */
    static QString formatDateTime(const QDateTime &dateTime, const QString &format = "yyyy-MM-dd hh:mm:ss");
    
    /**
     * @brief Generate a random string of a given length
     * @param length Length of the string
     * @return Random string
     */
    static QString generateRandomString(int length);
    
private:
    // Private constructor to prevent instantiation
    Utils() {}
};

#endif // UTILS_H 