/****************************************************************************
** Meta object code from reading C++ file 'CustomTreeWidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../src/widgets/CustomTreeWidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CustomTreeWidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CustomTreeWidget_t {
    QByteArrayData data[21];
    char stringdata0[232];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CustomTreeWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CustomTreeWidget_t qt_meta_stringdata_CustomTreeWidget = {
    {
QT_MOC_LITERAL(0, 0, 16), // "CustomTreeWidget"
QT_MOC_LITERAL(1, 17, 9), // "itemAdded"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 16), // "QTreeWidgetItem*"
QT_MOC_LITERAL(4, 45, 4), // "item"
QT_MOC_LITERAL(5, 50, 11), // "itemRemoved"
QT_MOC_LITERAL(6, 62, 9), // "itemMoved"
QT_MOC_LITERAL(7, 72, 9), // "newParent"
QT_MOC_LITERAL(8, 82, 9), // "oldParent"
QT_MOC_LITERAL(9, 92, 10), // "itemEdited"
QT_MOC_LITERAL(10, 103, 6), // "column"
QT_MOC_LITERAL(11, 110, 4), // "text"
QT_MOC_LITERAL(12, 115, 20), // "contextMenuRequested"
QT_MOC_LITERAL(13, 136, 3), // "pos"
QT_MOC_LITERAL(14, 140, 9), // "onAddItem"
QT_MOC_LITERAL(15, 150, 12), // "onRemoveItem"
QT_MOC_LITERAL(16, 163, 10), // "onEditItem"
QT_MOC_LITERAL(17, 174, 12), // "onMoveItemUp"
QT_MOC_LITERAL(18, 187, 14), // "onMoveItemDown"
QT_MOC_LITERAL(19, 202, 13), // "onItemChanged"
QT_MOC_LITERAL(20, 216, 15) // "showContextMenu"

    },
    "CustomTreeWidget\0itemAdded\0\0"
    "QTreeWidgetItem*\0item\0itemRemoved\0"
    "itemMoved\0newParent\0oldParent\0itemEdited\0"
    "column\0text\0contextMenuRequested\0pos\0"
    "onAddItem\0onRemoveItem\0onEditItem\0"
    "onMoveItemUp\0onMoveItemDown\0onItemChanged\0"
    "showContextMenu"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CustomTreeWidget[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   74,    2, 0x06 /* Public */,
       5,    1,   77,    2, 0x06 /* Public */,
       6,    3,   80,    2, 0x06 /* Public */,
       9,    3,   87,    2, 0x06 /* Public */,
      12,    2,   94,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      14,    0,   99,    2, 0x0a /* Public */,
      15,    0,  100,    2, 0x0a /* Public */,
      16,    0,  101,    2, 0x0a /* Public */,
      17,    0,  102,    2, 0x0a /* Public */,
      18,    0,  103,    2, 0x0a /* Public */,
      19,    2,  104,    2, 0x08 /* Private */,
      20,    1,  109,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3, 0x80000000 | 3, 0x80000000 | 3,    4,    7,    8,
    QMetaType::Void, 0x80000000 | 3, QMetaType::Int, QMetaType::QString,    4,   10,   11,
    QMetaType::Void, 0x80000000 | 3, QMetaType::QPoint,    4,   13,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 3, QMetaType::Int,    4,   10,
    QMetaType::Void, QMetaType::QPoint,   13,

       0        // eod
};

void CustomTreeWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CustomTreeWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->itemAdded((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1]))); break;
        case 1: _t->itemRemoved((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1]))); break;
        case 2: _t->itemMoved((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< QTreeWidgetItem*(*)>(_a[2])),(*reinterpret_cast< QTreeWidgetItem*(*)>(_a[3]))); break;
        case 3: _t->itemEdited((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 4: _t->contextMenuRequested((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< const QPoint(*)>(_a[2]))); break;
        case 5: _t->onAddItem(); break;
        case 6: _t->onRemoveItem(); break;
        case 7: _t->onEditItem(); break;
        case 8: _t->onMoveItemUp(); break;
        case 9: _t->onMoveItemDown(); break;
        case 10: _t->onItemChanged((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 11: _t->showContextMenu((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CustomTreeWidget::*)(QTreeWidgetItem * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CustomTreeWidget::itemAdded)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (CustomTreeWidget::*)(QTreeWidgetItem * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CustomTreeWidget::itemRemoved)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (CustomTreeWidget::*)(QTreeWidgetItem * , QTreeWidgetItem * , QTreeWidgetItem * );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CustomTreeWidget::itemMoved)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (CustomTreeWidget::*)(QTreeWidgetItem * , int , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CustomTreeWidget::itemEdited)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (CustomTreeWidget::*)(QTreeWidgetItem * , const QPoint & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CustomTreeWidget::contextMenuRequested)) {
                *result = 4;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CustomTreeWidget::staticMetaObject = { {
    QMetaObject::SuperData::link<QTreeWidget::staticMetaObject>(),
    qt_meta_stringdata_CustomTreeWidget.data,
    qt_meta_data_CustomTreeWidget,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CustomTreeWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CustomTreeWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CustomTreeWidget.stringdata0))
        return static_cast<void*>(this);
    return QTreeWidget::qt_metacast(_clname);
}

int CustomTreeWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QTreeWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void CustomTreeWidget::itemAdded(QTreeWidgetItem * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void CustomTreeWidget::itemRemoved(QTreeWidgetItem * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void CustomTreeWidget::itemMoved(QTreeWidgetItem * _t1, QTreeWidgetItem * _t2, QTreeWidgetItem * _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void CustomTreeWidget::itemEdited(QTreeWidgetItem * _t1, int _t2, const QString & _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void CustomTreeWidget::contextMenuRequested(QTreeWidgetItem * _t1, const QPoint & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
