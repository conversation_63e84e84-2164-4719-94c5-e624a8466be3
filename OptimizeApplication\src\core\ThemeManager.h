#ifndef THEMEMANAGER_H
#define THEMEMANAGER_H

#include <QObject>
#include <QColor>
#include <QMap>
#include <QString>
#include <QJsonObject>
#include <QJsonDocument>
#include <QFile>
#include <QStyleFactory>
#include <QApplication>
#include <QPalette>
#include <QMutex>

// Predefined theme types
enum class ThemeType {
    Light,
    Dark,
    System,
    Custom
};

// Theme color roles
enum class ColorRole {
    Primary,
    Secondary,
    Background,
    Foreground,
    Accent,
    Text,
    ButtonBackground,
    ButtonText,
    Highlight
};

class ThemeManager : public QObject
{
    Q_OBJECT

public:
    static ThemeManager* instance();

    // Theme selection and management
    void setTheme(ThemeType type);
    void setCustomTheme(const QJsonObject& themeData);
    ThemeType getCurrentTheme() const;

    // Color retrieval methods
    QColor getColor(ColorRole role) const;
    
    // Style and palette management
    void applyStyle(const QString& styleName);
    void applyPalette();

    // Theme export and import
    QJsonObject exportCurrentTheme() const;
    void importThemeFromFile(const QString& filePath);

    // Accessibility features
    void setHighContrastMode(bool enabled);
    bool isHighContrastMode() const;

signals:
    void themeChanged(ThemeType newTheme);
    void colorPaletteUpdated();

private:
    explicit ThemeManager(QObject *parent = nullptr);
    ~ThemeManager() = default;
    
    // Prevent copying
    ThemeManager(const ThemeManager&) = delete;
    ThemeManager& operator=(const ThemeManager&) = delete;
    
    // Internal theme initialization methods
    void initializeLightTheme();
    void initializeDarkTheme();
    void initializeSystemTheme();

    // Theme data storage
    QMap<ColorRole, QColor> m_currentPalette;
    ThemeType m_currentTheme;
    bool m_highContrastMode;

    // Singleton instance management
    static ThemeManager* m_instance;
    
    // Thread-safe instance creation mutex
    static QMutex m_instanceMutex;
};

#endif // THEMEMANAGER_H 