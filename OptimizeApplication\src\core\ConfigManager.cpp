#include "ConfigManager.h"
#include <QCoreApplication>
#include <QDir>
#include <QMutexLocker>

// 静态成员初始化
ConfigManager* ConfigManager::m_instance = nullptr;
QMutex ConfigManager::m_mutex;

// 配置键常量定义
const QString ConfigManager::KEY_WINDOW_GEOMETRY = "UI/WindowGeometry";
const QString ConfigManager::KEY_WINDOW_STATE = "UI/WindowState";
const QString ConfigManager::KEY_RECENT_FILES = "Files/RecentFiles";
const QString ConfigManager::KEY_LANGUAGE = "UI/Language";
const QString ConfigManager::KEY_THEME = "UI/Theme";
const QString ConfigManager::KEY_LOG_LEVEL = "Logging/Level";
const QString ConfigManager::KEY_LOG_TO_FILE = "Logging/ToFile";
const QString ConfigManager::KEY_OPTIMIZATION_PARAMS = "Params/Optimization";
const QString ConfigManager::KEY_INPUT_PARAMS = "Params/Input";
const QString ConfigManager::KEY_OUTPUT_PARAMS = "Params/Output";

ConfigManager* ConfigManager::instance()
{
    if (!m_instance) {
        QMutexLocker locker(&m_mutex);
        if (!m_instance) {
            m_instance = new ConfigManager();
        }
    }
    return m_instance;
}

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent)
    , m_settings(nullptr)
{
    // Set application information
    QCoreApplication::setOrganizationName(AppConstants::ORGANIZATION_NAME);
    QCoreApplication::setOrganizationDomain(AppConstants::ORGANIZATION_DOMAIN);
    QCoreApplication::setApplicationName(AppConstants::APP_NAME);
    QCoreApplication::setApplicationVersion(AppConstants::APP_VERSION);
    
    // Create configuration file
    QString configPath = Utils::getConfigPath();
    Utils::ensureDirectoryExists(configPath);
    
    QString configFile = QDir(configPath).absoluteFilePath("config.ini");
    m_settings = new QSettings(configFile, QSettings::IniFormat);
    m_settings->setIniCodec("UTF-8");
    
    initDefaultSettings();
}

ConfigManager::~ConfigManager()
{
    if (m_settings) {
        m_settings->sync();
        delete m_settings;
        m_settings = nullptr;
    }
}

void ConfigManager::initDefaultSettings()
{
    // Set default values
    if (!contains(KEY_LANGUAGE)) {
        setValue(KEY_LANGUAGE, "zh_CN");
    }
    
    if (!contains(KEY_THEME)) {
        setValue(KEY_THEME, "default");
    }
    
    if (!contains(KEY_LOG_LEVEL)) {
        setValue(KEY_LOG_LEVEL, logLevelToString(LogLevel::Info));
    }
    
    if (!contains(KEY_LOG_TO_FILE)) {
        setValue(KEY_LOG_TO_FILE, true);
    }
    
    if (!contains(KEY_RECENT_FILES)) {
        setValue(KEY_RECENT_FILES, QStringList());
    }
}

void ConfigManager::setValue(const QString& key, const QVariant& value)
{
    QMutexLocker locker(&m_settingsMutex);
    
    if (m_settings) {
        m_settings->setValue(key, value);
        m_settings->sync();
        emit configChanged(key, value);
    }
}

QVariant ConfigManager::getValue(const QString& key, const QVariant& defaultValue) const
{
    QMutexLocker locker(&m_settingsMutex);
    
    if (m_settings) {
        return m_settings->value(key, defaultValue);
    }
    
    return defaultValue;
}

bool ConfigManager::contains(const QString& key) const
{
    QMutexLocker locker(&m_settingsMutex);
    
    if (m_settings) {
        return m_settings->contains(key);
    }
    
    return false;
}

void ConfigManager::remove(const QString& key)
{
    QMutexLocker locker(&m_settingsMutex);
    
    if (m_settings) {
        m_settings->remove(key);
        m_settings->sync();
    }
}

void ConfigManager::clear()
{
    QMutexLocker locker(&m_settingsMutex);
    
    if (m_settings) {
        m_settings->clear();
        m_settings->sync();
        initDefaultSettings();
    }
}

void ConfigManager::setWindowGeometry(const QByteArray& geometry)
{
    setValue(KEY_WINDOW_GEOMETRY, geometry);
}

QByteArray ConfigManager::getWindowGeometry() const
{
    return getValue(KEY_WINDOW_GEOMETRY).toByteArray();
}

void ConfigManager::setWindowState(const QByteArray& state)
{
    setValue(KEY_WINDOW_STATE, state);
}

QByteArray ConfigManager::getWindowState() const
{
    return getValue(KEY_WINDOW_STATE).toByteArray();
}

void ConfigManager::setRecentFiles(const QStringList& files)
{
    setValue(KEY_RECENT_FILES, files);
}

QStringList ConfigManager::getRecentFiles() const
{
    return getValue(KEY_RECENT_FILES).toStringList();
}

void ConfigManager::addRecentFile(const QString& file)
{
    QStringList files = getRecentFiles();
    files.removeAll(file);  // Remove duplicates
    files.prepend(file);    // Add to beginning
    
    // Limit maximum count
    const int maxRecentFiles = 10;
    while (files.size() > maxRecentFiles) {
        files.removeLast();
    }
    
    setRecentFiles(files);
}

void ConfigManager::removeRecentFile(const QString& file)
{
    QStringList files = getRecentFiles();
    files.removeAll(file);
    setRecentFiles(files);
}

void ConfigManager::setLanguage(const QString& language)
{
    setValue(KEY_LANGUAGE, language);
}

QString ConfigManager::getLanguage() const
{
    return getValue(KEY_LANGUAGE, "zh_CN").toString();
}

void ConfigManager::setTheme(const QString& theme)
{
    setValue(KEY_THEME, theme);
}

QString ConfigManager::getTheme() const
{
    return getValue(KEY_THEME, "default").toString();
}

void ConfigManager::setLogLevel(LogLevel level)
{
    setValue(KEY_LOG_LEVEL, logLevelToString(level));
}

LogLevel ConfigManager::getLogLevel() const
{
    QString levelStr = getValue(KEY_LOG_LEVEL, "Info").toString();
    return stringToLogLevel(levelStr);
}

void ConfigManager::setLogToFile(bool enabled)
{
    setValue(KEY_LOG_TO_FILE, enabled);
}

bool ConfigManager::isLogToFileEnabled() const
{
    return getValue(KEY_LOG_TO_FILE, true).toBool();
}

void ConfigManager::setOptimizationParams(const QVariantMap& params)
{
    setValue(KEY_OPTIMIZATION_PARAMS, params);
}

QVariantMap ConfigManager::getOptimizationParams() const
{
    return getValue(KEY_OPTIMIZATION_PARAMS).toMap();
}

void ConfigManager::setInputParams(const QVariantMap& params)
{
    setValue(KEY_INPUT_PARAMS, params);
}

QVariantMap ConfigManager::getInputParams() const
{
    return getValue(KEY_INPUT_PARAMS).toMap();
}

void ConfigManager::setOutputParams(const QVariantMap& params)
{
    setValue(KEY_OUTPUT_PARAMS, params);
}

QVariantMap ConfigManager::getOutputParams() const
{
    return getValue(KEY_OUTPUT_PARAMS).toMap();
}

QString ConfigManager::logLevelToString(LogLevel level) const
{
    switch (level) {
        case LogLevel::Debug:    return "Debug";
        case LogLevel::Info:     return "Info";
        case LogLevel::Warning:  return "Warning";
        case LogLevel::Error:    return "Error";
        case LogLevel::Critical: return "Critical";
        default:                 return "Info";
    }
}

LogLevel ConfigManager::stringToLogLevel(const QString& str) const
{
    if (str == "Debug") return LogLevel::Debug;
    if (str == "Info") return LogLevel::Info;
    if (str == "Warning") return LogLevel::Warning;
    if (str == "Error") return LogLevel::Error;
    if (str == "Critical") return LogLevel::Critical;
    return LogLevel::Info;
} 