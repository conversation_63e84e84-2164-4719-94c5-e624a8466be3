#ifndef CHARTWIDGET_H
#define CHARTWIDGET_H

#include <QWidget>
#include <QChart>
#include <QChartView>
#include <QLineSeries>
#include <QScatterSeries>
#include <QBarSeries>
#include <QBarSet>
#include <QPieSeries>
#include <QAreaSeries>
#include <QValueAxis>
#include <QBarCategoryAxis>
#include <QPieSlice>
#include <QGridLayout>
#include <QComboBox>
#include <QPushButton>
#include <QLabel>
#include <QMenu>
#include <QAction>
#include <QToolBar>
#include <QFileDialog>
#include <QInputDialog>
#include <QColorDialog>

QT_CHARTS_USE_NAMESPACE

/**
 * @brief 图表控件类，提供多种图表类型和交互功能
 */
class ChartWidget : public QWidget
{
    Q_OBJECT

public:
    /**
     * @brief 图表类型枚举
     */
    enum ChartType {
        LineChart,      ///< 折线图
        ScatterChart,   ///< 散点图
        BarChart,       ///< 柱状图
        PieChart,       ///< 饼图
        AreaChart       ///< 面积图
    };

    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit ChartWidget(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~ChartWidget();

    /**
     * @brief 设置图表标题
     * @param title 标题
     */
    void setChartTitle(const QString &title);

    /**
     * @brief 设置图表类型
     * @param type 图表类型
     */
    void setChartType(ChartType type);

    /**
     * @brief 设置X轴标题
     * @param title 标题
     */
    void setXAxisTitle(const QString &title);

    /**
     * @brief 设置Y轴标题
     * @param title 标题
     */
    void setYAxisTitle(const QString &title);

    /**
     * @brief 设置X轴范围
     * @param min 最小值
     * @param max 最大值
     */
    void setXAxisRange(qreal min, qreal max);

    /**
     * @brief 设置Y轴范围
     * @param min 最小值
     * @param max 最大值
     */
    void setYAxisRange(qreal min, qreal max);

    /**
     * @brief 添加数据系列
     * @param name 系列名称
     * @param color 系列颜色
     * @return 系列索引
     */
    int addSeries(const QString &name, const QColor &color = QColor());

    /**
     * @brief 移除数据系列
     * @param index 系列索引
     */
    void removeSeries(int index);

    /**
     * @brief 清除所有数据系列
     */
    void clearAllSeries();

    /**
     * @brief 添加数据点到系列
     * @param seriesIndex 系列索引
     * @param x X坐标
     * @param y Y坐标
     */
    void addDataPoint(int seriesIndex, qreal x, qreal y);

    /**
     * @brief 设置系列数据
     * @param seriesIndex 系列索引
     * @param xValues X坐标数组
     * @param yValues Y坐标数组
     */
    void setSeriesData(int seriesIndex, const QVector<qreal> &xValues, const QVector<qreal> &yValues);

    /**
     * @brief 设置柱状图类别
     * @param categories 类别数组
     */
    void setBarCategories(const QStringList &categories);

    /**
     * @brief 设置饼图数据
     * @param labels 标签数组
     * @param values 值数组
     */
    void setPieData(const QStringList &labels, const QVector<qreal> &values);

    /**
     * @brief 设置是否显示图例
     * @param visible 是否显示
     */
    void setLegendVisible(bool visible);

    /**
     * @brief 设置是否启用工具栏
     * @param enabled 是否启用
     */
    void setToolBarEnabled(bool enabled);

    /**
     * @brief 设置是否启用动画
     * @param enabled 是否启用
     */
    void setAnimationEnabled(bool enabled);

    /**
     * @brief 设置是否启用主题选择
     * @param enabled 是否启用
     */
    void setThemeSelectEnabled(bool enabled);

    /**
     * @brief 获取当前图表
     * @return 图表对象
     */
    QChart* chart() const;

    /**
     * @brief 获取当前图表视图
     * @return 图表视图对象
     */
    QChartView* chartView() const;

public Q_SLOTS:
    /**
     * @brief 保存图表为图像
     * @param filePath 文件路径，如果为空则弹出保存对话框
     */
    void saveChart(const QString &filePath = QString());

    /**
     * @brief 打印图表
     */
    void printChart();

    /**
     * @brief 设置主题
     * @param theme 主题
     */
    void setTheme(QChart::ChartTheme theme);

    /**
     * @brief 缩放到数据范围
     */
    void zoomToData();

    /**
     * @brief 重置缩放
     */
    void resetZoom();

private Q_SLOTS:
    /**
     * @brief 处理图表类型改变
     * @param index 类型索引
     */
    void onChartTypeChanged(int index);

    /**
     * @brief 处理主题改变
     * @param index 主题索引
     */
    void onThemeChanged(int index);

    /**
     * @brief 处理导出图表
     */
    void onExportChart();

    /**
     * @brief 处理添加系列
     */
    void onAddSeries();

    /**
     * @brief 处理编辑系列
     */
    void onEditSeries();

    /**
     * @brief 处理移除系列
     */
    void onRemoveSeries();

    /**
     * @brief 处理图例点击
     */
    void onLegendClicked();

    /**
     * @brief 处理饼图切片点击
     * @param slice 切片对象
     */
    void onPieSliceClicked(QPieSlice *slice);

private:
    /**
     * @brief 初始化UI
     */
    void initUI();

    /**
     * @brief 创建工具栏
     */
    void createToolBar();

    /**
     * @brief 创建连接
     */
    void createConnections();

    /**
     * @brief 创建图表
     */
    void createChart();

    /**
     * @brief 创建折线图
     */
    void createLineChart();

    /**
     * @brief 创建散点图
     */
    void createScatterChart();

    /**
     * @brief 创建柱状图
     */
    void createBarChart();

    /**
     * @brief 创建饼图
     */
    void createPieChart();

    /**
     * @brief 创建面积图
     */
    void createAreaChart();

    /**
     * @brief 更新图表
     */
    void updateChart();

    /**
     * @brief 更新轴
     */
    void updateAxes();

    QChart *m_chart;                          ///< 图表对象
    QChartView *m_chartView;                  ///< 图表视图
    QGridLayout *m_layout;                    ///< 布局
    QToolBar *m_toolBar;                      ///< 工具栏
    QComboBox *m_chartTypeCombo;              ///< 图表类型下拉框
    QComboBox *m_themeCombo;                  ///< 主题下拉框
    QPushButton *m_exportButton;              ///< 导出按钮
    QPushButton *m_addSeriesButton;           ///< 添加系列按钮
    QPushButton *m_editSeriesButton;          ///< 编辑系列按钮
    QPushButton *m_removeSeriesButton;        ///< 移除系列按钮
    QPushButton *m_zoomToDataButton;          ///< 缩放到数据按钮
    QPushButton *m_resetZoomButton;           ///< 重置缩放按钮

    QList<QXYSeries*> m_series;               ///< XY系列列表
    QList<QString> m_seriesNames;             ///< 系列名称列表
    QList<QColor> m_seriesColors;             ///< 系列颜色列表

    QBarSeries *m_barSeries;                  ///< 柱状图系列
    QList<QBarSet*> m_barSets;                ///< 柱状图数据集列表
    QStringList m_barCategories;              ///< 柱状图类别列表

    QPieSeries *m_pieSeries;                  ///< 饼图系列
    QList<QPieSlice*> m_pieSlices;            ///< 饼图切片列表

    QValueAxis *m_axisX;                      ///< X轴
    QValueAxis *m_axisY;                      ///< Y轴
    QBarCategoryAxis *m_barAxisX;             ///< 柱状图X轴

    ChartType m_chartType;                    ///< 当前图表类型
    bool m_toolBarEnabled;                    ///< 是否启用工具栏
    bool m_animationEnabled;                  ///< 是否启用动画
    bool m_themeSelectEnabled;                ///< 是否启用主题选择
};

#endif // CHARTWIDGET_H 