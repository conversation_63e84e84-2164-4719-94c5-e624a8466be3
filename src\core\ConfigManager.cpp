#include "ConfigManager.h"
#include "../utils/Logger.h"
#include "../utils/Common.h"
#include <QDir>
#include <QFileInfo>

ConfigManager::ConfigManager()
    : settings(nullptr)
{
}

ConfigManager::~ConfigManager()
{
    if (settings) {
        saveSettings();
        delete settings;
        settings = nullptr;
    }
}

bool ConfigManager::initialize()
{
    LOG_INFO("Initializing ConfigManager", "ConfigManager");
    
    // Create settings object
    settings = new QSettings(
        QSettings::IniFormat,
        QSettings::UserScope,
        AppConstants::ORGANIZATION_NAME,
        AppConstants::APP_NAME
    );
    
    if (!settings) {
        LOG_ERROR("Failed to create settings object", "ConfigManager");
        return false;
    }
    
    // Load cached settings
    windowGeometry = settings->value(AppConstants::Settings::WINDOW_GEOMETRY).toByteArray();
    windowState = settings->value(AppConstants::Settings::WINDOW_STATE).toByteArray();
    recentFiles = settings->value(AppConstants::Settings::RECENT_FILES).toStringList();
    lastDirectory = settings->value(AppConstants::Settings::LAST_DIRECTORY, AppConstants::Defaults::DEFAULT_DIRECTORY).toString();
    
    LOG_INFO("ConfigManager initialized successfully", "ConfigManager");
    return true;
}

void ConfigManager::saveSettings()
{
    LOG_INFO("Saving settings", "ConfigManager");
    
    if (!settings) {
        LOG_WARNING("Settings object is null, cannot save settings", "ConfigManager");
        return;
    }
    
    // Save cached settings
    settings->setValue(AppConstants::Settings::WINDOW_GEOMETRY, windowGeometry);
    settings->setValue(AppConstants::Settings::WINDOW_STATE, windowState);
    settings->setValue(AppConstants::Settings::RECENT_FILES, recentFiles);
    settings->setValue(AppConstants::Settings::LAST_DIRECTORY, lastDirectory);
    
    // Ensure settings are written to disk
    settings->sync();
    
    LOG_INFO("Settings saved", "ConfigManager");
}

QByteArray ConfigManager::getWindowGeometry() const
{
    return windowGeometry;
}

void ConfigManager::setWindowGeometry(const QByteArray &geometry)
{
    windowGeometry = geometry;
}

QByteArray ConfigManager::getWindowState() const
{
    return windowState;
}

void ConfigManager::setWindowState(const QByteArray &state)
{
    windowState = state;
}

QStringList ConfigManager::getRecentFiles() const
{
    return recentFiles;
}

void ConfigManager::addRecentFile(const QString &filePath)
{
    // Add file to the beginning of the list
    recentFiles.removeAll(filePath);
    recentFiles.prepend(filePath);
    
    // Keep the list limited to MAX_RECENT_FILES
    while (recentFiles.size() > MAX_RECENT_FILES) {
        recentFiles.removeLast();
    }
    
    // Update in settings
    settings->setValue(AppConstants::Settings::RECENT_FILES, recentFiles);
}

QString ConfigManager::getLastDirectory() const
{
    return lastDirectory;
}

void ConfigManager::setLastDirectory(const QString &directory)
{
    lastDirectory = directory;
    settings->setValue(AppConstants::Settings::LAST_DIRECTORY, lastDirectory);
}

QVariant ConfigManager::getSetting(const QString &key, const QVariant &defaultValue) const
{
    if (!settings) {
        LOG_WARNING("Settings object is null, returning default value", "ConfigManager");
        return defaultValue;
    }
    
    return settings->value(key, defaultValue);
}

void ConfigManager::setSetting(const QString &key, const QVariant &value)
{
    if (!settings) {
        LOG_WARNING("Settings object is null, cannot set value", "ConfigManager");
        return;
    }
    
    settings->setValue(key, value);
} 